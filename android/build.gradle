buildscript {
    ext {
        buildToolsVersion = "35.0.0"
        minSdkVersion = 24
        compileSdkVersion = 35
        targetSdkVersion = 34
        ndkVersion = "26.1.10909125"
        kotlinVersion = "1.9.25"
    }
    ext.kotlin_version = '1.9.22'
    repositories {
        google()
        mavenCentral()
    }
    dependencies {
        classpath("com.android.tools.build:gradle:8.1.0")
        classpath("com.facebook.react:react-native-gradle-plugin")
        classpath("org.jetbrains.kotlin:kotlin-gradle-plugin:1.9.22")
        classpath 'com.google.gms:google-services:4.3.15'

    }
}
allprojects {
    repositories {
        jcenter()
        google()
        mavenCentral()
        maven { url "https://plugins.gradle.org/m2/" }
        maven {url = uri("https://storage.googleapis.com/r8-releases/raw")}
        maven { url "https://jitpack.io" } // For libraries not available on Maven Central.
        maven { url "https://oss.jfrog.org/artifactory/oss-snapshot-local" } // Additional repository for snapshots.
        gradle.projectsEvaluated {
            tasks.withType(JavaCompile) {
                options.compilerArgs << "-Xlint:unchecked"
            }
        }
    }
    def REACT_NATIVE_VERSION = new File(['node', '--print',"JSON.parse(require('fs').readFileSync(require.resolve('react-native/package.json'), 'utf-8')).version"].execute(null, rootDir).text.trim())
    configurations.all {
        resolutionStrategy {
            force "com.facebook.react:react-native:" + REACT_NATIVE_VERSION
            force 'com.google.android.gms:play-services-location:21.0.1'
        }
    }
}
project.ext {
    set('react-native', [
            versions: [
                    // Overriding Build/Android SDK Versions
                    android : [
                            minSdk    : 24, // 23+ if using auth module
                            targetSdk : 34,
                            compileSdk: 35
                    ],
                    play     : [
                            "play-services-auth": "20.7.0"
                    ],
            ],
    ])
}

apply plugin: "com.facebook.react.rootproject"
