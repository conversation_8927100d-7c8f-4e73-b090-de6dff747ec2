import React from 'react';
import {NavigationContainer} from '@react-navigation/native';
import {createNativeStackNavigator} from '@react-navigation/native-stack';
import DeviceInfo from 'react-native-device-info';
import networkService from './modules/jiomart-common/src/JMNetworkConnectionUtility';
import {JMSharedViewModel} from './modules/jiomart-common/src/JMSharedViewModel';
import {AppSourceType} from './modules/jiomart-common/src/SourceType';
import {JioMartMainUI} from './modules/jiomart-main/src';
import {AppScreens} from '@jm/jiomart-common/src/JMAppScreenEntry';
import {SafeAreaProvider} from 'react-native-safe-area-context';

const Stack = createNativeStackNavigator<RootStackNavigatorParamsList>();

type RootStackNavigatorParamsList = {
  JioMartMainUI: {
    params: {
      phoneNumber: string;
      deeplink: string;
      data: {
        token: string;
        webIosClientId: string;
        iosClientId: string;
      };
    };
  };
};

export const getAppVersion = () => {
  const appVersion = DeviceInfo.getVersion();
  console.log('getAppVersion ' + appVersion);
  return appVersion;
};

function getFirstRoute(): string {
  if (JMSharedViewModel.Instance.appSource === AppSourceType.JM_BAU) {
    return '';
  }
  return 'jhh://com.jio.jhh/JMSplashScreen';
}
const App = () => {
  networkService.startMonitoring();
  return (
    <SafeAreaProvider>
      <NavigationContainer ref={JMSharedViewModel.Instance.navigationRef}>
        <Stack.Navigator
          initialRouteName={AppScreens.JIOMART_MAIN_UI}
          screenOptions={{headerShown: false}}>
          <Stack.Screen
            name={AppScreens.JIOMART_MAIN_UI}
            component={JioMartMainUI}
            initialParams={{
              params: {
                phoneNumber: '',
                deeplink: getFirstRoute(),
                data: {
                  token: '',
                  webIosClientId: '',
                  iosClientId: '',
                },
              },
            }}
          />
        </Stack.Navigator>
      </NavigationContainer>
    </SafeAreaProvider>
  );
};
export default App;
