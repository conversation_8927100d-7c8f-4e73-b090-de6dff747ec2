package com.jiohealth

import com.facebook.react.ReactPackage
import com.facebook.react.bridge.NativeModule
import com.facebook.react.bridge.ReactApplicationContext
import com.facebook.react.uimanager.ViewManager
import com.jiohealth.module.biometrics.AndroidBiometrics
import com.jiohealth.module.GeneralPropertiesModule
import com.jiohealth.module.PhoneNumberHintModule
import com.jiohealth.module.SmsRetrieverModule

class JHModulePackages : ReactPackage {
    override fun createViewManagers(reactContext: ReactApplicationContext): List<ViewManager<*, *>> {
        return emptyList()
    }
    override fun createNativeModules(reactContext: ReactApplicationContext): List<NativeModule> {
        val modules = mutableListOf<NativeModule>()
        modules.add(GeneralPropertiesModule(reactContext))
        modules.add(SmsRetrieverModule(reactContext))
        modules.add(PhoneNumberHintModule(reactContext))
        modules.add(AndroidBiometrics(reactContext))
        return modules
    }
}

