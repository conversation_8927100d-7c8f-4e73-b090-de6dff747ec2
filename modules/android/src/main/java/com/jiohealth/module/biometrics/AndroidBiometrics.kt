package com.jiohealth.module.biometrics

import android.os.Build
import android.security.keystore.KeyGenParameterSpec
import android.security.keystore.KeyProperties
import android.util.Base64
import androidx.biometric.BiometricManager
import androidx.biometric.BiometricPrompt
import androidx.fragment.app.FragmentActivity
import com.facebook.react.bridge.Promise
import com.facebook.react.bridge.ReactApplicationContext
import com.facebook.react.bridge.ReactContextBaseJavaModule
import com.facebook.react.bridge.ReactMethod
import com.facebook.react.bridge.ReadableMap
import com.facebook.react.bridge.UiThreadUtil
import com.facebook.react.bridge.WritableMap
import com.facebook.react.bridge.WritableNativeMap
import com.jiohealth.module.biometrics.Configuration.ALGORITHM_PARAMETER_SPEC
import com.jiohealth.module.biometrics.Configuration.AUTHENTICATORS
import com.jiohealth.module.biometrics.Configuration.ENCRYPTION_ALGORITHM
import com.jiohealth.module.biometrics.Configuration.KEYSTORE_ALIAS
import com.jiohealth.module.biometrics.Configuration.KEYSTORE_NAME
import com.jiohealth.module.biometrics.Configuration.SIGNATURE_DIGESTS
import com.jiohealth.module.biometrics.Configuration.SIGNATURE_PADDING
import com.jiohealth.module.biometrics.Configuration.SIGNATURE_TYPE
import com.jiohealth.module.biometrics.Configuration.isDeviceCredentialEnabled
import java.security.KeyPairGenerator
import java.security.KeyStore
import java.security.PrivateKey
import java.security.Signature
import java.util.concurrent.Executor
import java.util.concurrent.Executors

class AndroidBiometrics(reactContext: ReactApplicationContext?) :
    ReactContextBaseJavaModule(reactContext) {
    override fun getName(): String {
        return "AndroidBiometrics"
    }

    @ReactMethod
    fun isSensorAvailable(promise: Promise) {
        try {
            if (isCurrentSDKMarshmallowOrLater) {
                val reactApplicationContext = reactApplicationContext
                val biometricManager = BiometricManager.from(reactApplicationContext)
                val canAuthenticate =
                    biometricManager.canAuthenticate(AUTHENTICATORS)
                when(canAuthenticate){
                    BiometricManager.BIOMETRIC_SUCCESS -> {
                        promise.resolve(WritableNativeMap().apply {
                            putBoolean("available", true)
                            putString("biometryType", "Biometrics")
                        })
                    }
                    else -> {
                        val resultMap: WritableMap = WritableNativeMap()
                        resultMap.putBoolean("available", false)
                        when (canAuthenticate) {
                            BiometricManager.BIOMETRIC_ERROR_NO_HARDWARE -> resultMap.putString(
                                "error",
                                "BIOMETRIC_ERROR_NO_HARDWARE"
                            )

                            BiometricManager.BIOMETRIC_ERROR_HW_UNAVAILABLE -> resultMap.putString(
                                "error",
                                "BIOMETRIC_ERROR_HW_UNAVAILABLE"
                            )

                            BiometricManager.BIOMETRIC_ERROR_NONE_ENROLLED -> resultMap.putString(
                                "error",
                                "BIOMETRIC_ERROR_NONE_ENROLLED"
                            )

                            BiometricManager.BIOMETRIC_ERROR_SECURITY_UPDATE_REQUIRED -> resultMap.putString(
                                "error",
                                "BIOMETRIC_ERROR_SECURITY_UPDATE_REQUIRED"
                            )
                        }
                        promise.resolve(resultMap)
                    }
                }
            } else {
                promise.resolve(WritableNativeMap().apply {
                    putBoolean("available", false)
                    putString("error", "Unsupported android version")
                })
            }
        } catch (e: Exception) {
            promise.reject(
                "Error detecting biometrics availability: ${e.message}" ,
                "Error detecting biometrics availability: ${e.message}"
            )
        }
    }

    @ReactMethod
    fun createKeys(params: ReadableMap?, promise: Promise) {
        try {
            if (isCurrentSDKMarshmallowOrLater) {
                deleteBiometricAlias()
                val keyPairGenerator =
                    KeyPairGenerator.getInstance(ENCRYPTION_ALGORITHM, KEYSTORE_NAME)
                val keyGenParameterSpec =
                    KeyGenParameterSpec.Builder(KEYSTORE_ALIAS, KeyProperties.PURPOSE_SIGN) // Purpose specified depends on the algorithm you are using
                        .setDigests(SIGNATURE_DIGESTS)
                        .setSignaturePaddings(SIGNATURE_PADDING)
                        .setAlgorithmParameterSpec(ALGORITHM_PARAMETER_SPEC)
                        .setUserAuthenticationRequired(true)
                        .build()
                keyPairGenerator.initialize(keyGenParameterSpec)
                val keyPair = keyPairGenerator.generateKeyPair()
                val publicKey = keyPair.public
                val encodedPublicKey = publicKey.encoded
                var publicKeyString = Base64.encodeToString(encodedPublicKey, Base64.DEFAULT)
                publicKeyString =
                    publicKeyString.replace("\r".toRegex(), "").replace("\n".toRegex(), "")
                val resultMap: WritableMap = WritableNativeMap()
                resultMap.putString("publicKey", publicKeyString)
                promise.resolve(resultMap)
            } else {
                promise.reject(
                    "Cannot generate keys on android versions below 6.0",
                    "Cannot generate keys on android versions below 6.0"
                )
            }
        } catch (e: Exception) {
            promise.reject(
                "Error generating public private keys: ${e.message}",
                "Error generating public private keys"
            )
        }
    }

    private val isCurrentSDKMarshmallowOrLater: Boolean
        private get() = Build.VERSION.SDK_INT >= Build.VERSION_CODES.M

    @ReactMethod
    fun deleteAlias(promise: Promise) {
        if (doesBiometricAliasExist()) {
            val deletionSuccessful = deleteBiometricAlias()
            if (deletionSuccessful) {
                val resultMap: WritableMap = WritableNativeMap()
                resultMap.putBoolean("keysDeleted", true)
                promise.resolve(resultMap)
            } else {
                promise.reject(
                    "Error deleting biometric key from keystore",
                    "Error deleting biometric key from keystore"
                )
            }
        } else {
            val resultMap: WritableMap = WritableNativeMap()
            resultMap.putBoolean("keysDeleted", false)
            promise.resolve(resultMap)
        }
    }

    /**
     * For Generating Signature and Signing the Payload,
     * */
    @ReactMethod
    fun createSignature(params: ReadableMap, promise: Promise) {
        if (isCurrentSDKMarshmallowOrLater) {
            UiThreadUtil.runOnUiThread {
                try {
                    val promptTitle = params.getString(PROMPT_TITLE) //*Mandatory fields
                    val promptSubTitle = params.getString(PROMPT_SUBTITLE)
                    val payload = params.getString(PAYLOAD)?: "" //*Mandatory fields
                    var cancelButtonText: String? = null
                    if(params.hasKey("cancelButtonText")){
                        cancelButtonText = params.getString(CANCEL_BUTTON_TEXT) //*Mandatory fields
                    }
                    val signature = Signature.getInstance(SIGNATURE_TYPE)
                    val keyStore = KeyStore.getInstance(KEYSTORE_NAME)
                    keyStore.load(null)
                    val privateKey = keyStore.getKey(KEYSTORE_ALIAS, null) as PrivateKey
                    signature.initSign(privateKey)
                    val cryptoObject = BiometricPrompt.CryptoObject(signature)
                    val authCallback: BiometricPrompt.AuthenticationCallback = CreateSignatureAuthenticationCallback(promise, payload)
                    val executor: Executor = Executors.newSingleThreadExecutor()
                    (currentActivity as? FragmentActivity)?.let{
                        val biometricPrompt = BiometricPrompt(
                            it, executor, authCallback
                        )
                        biometricPrompt.authenticate(
                            getPromptInfo(
                                promptTitle,
                                promptSubTitle,
                                cancelButtonText,
                            ), cryptoObject
                        )
                    }?: promise.reject(
                        "Current Activity is not a FragmentActivity",
                        "Current Activity is not a FragmentActivity"
                    )

                } catch (e: Exception) {
                    promise.reject(
                        "Error signing payload: ${e.message}" ,
                        "Error generating signature: ${e.message}"
                    )
                }
            }
        } else {
            promise.reject(
                "Cannot generate keys on android versions below 6.0",
                "Cannot generate keys on android versions below 6.0"
            )
        }
    }

    private fun getPromptInfo(
        promptTitle: String?,
        promptSubtitle: String?,
        cancelButtonText: String?,
    ): BiometricPrompt.PromptInfo {
        val builder = BiometricPrompt.PromptInfo.Builder()
        promptTitle?.let { builder.setTitle(it) }
        promptSubtitle?.let{ builder.setDescription(it)}
        builder.setAllowedAuthenticators(AUTHENTICATORS)
        //Ideally we should check if the API is lower than SDK 29, and then set it accordingly, but this function is causing issues,
        /*if (isCurrentSDK29OrEarlier) {
            //java.lang.IllegalArgumentException: Negative text must not be set if device credential authentication is allowed.
            cancelButtonText?.let{ builder.setNegativeButtonText(it) }
        }*/
        if(!isDeviceCredentialEnabled){
            cancelButtonText?.let { builder.setNegativeButtonText(it) }
        }

        return builder.build()
    }

    private val isCurrentSDK29OrEarlier: Boolean
        private get() = Build.VERSION.SDK_INT <= Build.VERSION_CODES.Q

    @ReactMethod
    fun simplePrompt(params: ReadableMap, promise: Promise) {
        if (isCurrentSDKMarshmallowOrLater) {
            UiThreadUtil.runOnUiThread {
                try {
                    val promptTitle = params.getString(PROMPT_TITLE)
                    val promptSubTitle = params.getString(PROMPT_SUBTITLE)
                    val cancelButtonText = params.getString(CANCEL_BUTTON_TEXT)
                    val authCallback: BiometricPrompt.AuthenticationCallback =
                        SimplePromptAuthenticationCallback(promise)
                    val executor: Executor = Executors.newSingleThreadExecutor()
                    (currentActivity as? FragmentActivity)?.let {
                        val biometricPrompt = BiometricPrompt(
                            it, executor, authCallback
                        )
                        biometricPrompt.authenticate(
                            getPromptInfo(
                                promptTitle,
                                promptSubTitle,
                                cancelButtonText,
                            )
                        )
                    }?: promise.reject(
                        "Current Activity is not a FragmentActivity",
                        "Current Activity is not a FragmentActivity"
                    )

                } catch (e: Exception) {
                    promise.reject(
                        "Error displaying local biometric prompt: ${e.message}",
                        "Error displaying local biometric prompt: ${e.message}"
                    )
                }
            }
        } else {
            promise.reject(
                "Cannot display biometric prompt on android versions below 6.0",
                "Cannot display biometric prompt on android versions below 6.0"
            )
        }
    }

    @ReactMethod
    fun biometricAliasExist(promise: Promise) {
        try {
            val doesBiometricKeyExist = doesBiometricAliasExist()
            val resultMap: WritableMap = WritableNativeMap()
            resultMap.putBoolean("keysExist", doesBiometricKeyExist)
            promise.resolve(resultMap)
        } catch (e: Exception) {
            promise.reject(
                "Error checking if biometric key exists: ${e.message}",
                "Error checking if biometric key exists: ${e.message}"
            )
        }
    }

    protected fun doesBiometricAliasExist(): Boolean {
        return try {
            val keyStore = KeyStore.getInstance(KEYSTORE_NAME)
            keyStore.load(null)
            keyStore.containsAlias(KEYSTORE_ALIAS)
        } catch (e: Exception) {
            false
        }
    }

    protected fun deleteBiometricAlias(): Boolean {
        return try {
            val keyStore = KeyStore.getInstance(KEYSTORE_NAME)
            keyStore.load(null)
            keyStore.deleteEntry(KEYSTORE_ALIAS)
            true
        } catch (e: Exception) {
            false
        }
    }

    companion object {
        private const val PROMPT_TITLE = "promptTitle"
        private const val PROMPT_SUBTITLE = "promptSubTitle"
        private const val CANCEL_BUTTON_TEXT = "cancelButtonText"
        private const val PAYLOAD = "payload"
    }
}