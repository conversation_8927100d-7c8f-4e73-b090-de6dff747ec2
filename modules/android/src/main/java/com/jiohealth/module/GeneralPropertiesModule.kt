package com.jiohealth.module

import android.os.Handler
import android.os.Looper
import android.view.WindowManager
import com.facebook.react.bridge.ReactApplicationContext
import com.facebook.react.bridge.ReactContextBaseJavaModule
import com.facebook.react.bridge.ReactMethod
import com.facebook.react.module.annotations.ReactModule


@ReactModule(name = "GeneralPropertiesModule")
class GeneralPropertiesModule(var context: ReactApplicationContext) : ReactContextBaseJavaModule(context) {
    override fun getName(): String {
        return "GeneralPropertiesModule"
    }

    override fun canOverrideExistingModule(): <PERSON><PERSON>an {
        return true
    }

    @ReactMethod
    fun setResizeMode(flag : <PERSON><PERSON><PERSON>) {
        val activity = currentActivity
        Handler(Looper.getMainLooper()).post {
            if(flag){
                activity?.window?.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_RESIZE)
            }
            else{
                activity?.window?.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_PAN)
            }
        }
    }
}

