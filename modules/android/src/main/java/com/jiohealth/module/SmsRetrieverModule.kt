package com.jiohealth.module

import android.annotation.SuppressLint
import android.app.Activity
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.os.Build
import android.util.Log
import com.facebook.react.bridge.BaseActivityEventListener
import com.facebook.react.bridge.Promise
import com.facebook.react.bridge.ReactApplicationContext
import com.facebook.react.bridge.ReactContextBaseJavaModule
import com.facebook.react.bridge.ReactMethod
import com.facebook.react.modules.core.DeviceEventManagerModule
import com.google.android.gms.auth.api.phone.SmsRetriever
import com.google.android.gms.common.api.CommonStatusCodes
import com.google.android.gms.common.api.Status


class SmsRetrieverModule(
    reactContext: ReactApplicationContext
) : ReactContextBaseJavaModule(reactContext) {
    override fun getName(): String = "SmsRetrieverModule"

    /**
     * @param isSmsRetrieverMethod if true = sms retriever method, else, sms consent method
     * */
    private var isSmsRetrieverMethod: Boolean = false
    override fun canOverrideExistingModule(): Boolean {
        return true
    }
    private var isReceiverRegistered: Boolean = false
    private var smsPromise: Promise? = null
    private val broadcastReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context?, intent: Intent?) {
            Log.d("SMS_GET", "Intent $intent")
            if (intent != null && SmsRetriever.SMS_RETRIEVED_ACTION == intent.action) {
                val extras = intent.extras
                val smsRetrieverStatus = try {
                    extras?.get(SmsRetriever.EXTRA_STATUS) as? Status
                } catch (e: Exception) {
                    null
                }

                when (smsRetrieverStatus?.statusCode) {
                    CommonStatusCodes.SUCCESS -> {
                        if(isSmsRetrieverMethod) {
                            val message = extras?.getString(SmsRetriever.EXTRA_SMS_MESSAGE) ?: ""

                            Log.d("SMS_GET", "Message $message")
                            reactContext.getJSModule(DeviceEventManagerModule.RCTDeviceEventEmitter::class.java)
                                .emit("onSMSReceived", message)

                            smsPromise?.resolve(getVerificationCodeFromSms(message, 6))
                        }else{
                            val consentIntent = try {
                                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                                    extras?.getParcelable(SmsRetriever.EXTRA_CONSENT_INTENT, Intent::class.java)
                                } else {
                                    extras?.getParcelable(SmsRetriever.EXTRA_CONSENT_INTENT)
                                }


                            } catch (e: Exception) {
                                null
                            }

                            currentActivity?.startActivityForResult(consentIntent, SMS_CONSENT_REQUEST_CODE)
                        }
                    }

                    CommonStatusCodes.TIMEOUT -> {}
                }
            }
        }
    }

    private val activityEventListener =
        object : BaseActivityEventListener() {
            override fun onActivityResult(
                activity: Activity?,
                requestCode: Int,
                resultCode: Int,
                data: Intent?
            ) {
                super.onActivityResult(activity, requestCode, resultCode, data)
                android.util.Log.d("SMS_GET","inside activityEventListener")
                try {
                    if (requestCode == SMS_CONSENT_REQUEST_CODE && resultCode == Activity.RESULT_OK) {
                        val message = data?.getStringExtra(SmsRetriever.EXTRA_SMS_MESSAGE)
                        if(message.isNullOrEmpty()){
                            smsPromise?.reject(E_EXCEPTION, "message is null or empty")
                        }else{
                            android.util.Log.d("SMS_GET","recieved message $message")
                            smsPromise?.resolve(getVerificationCodeFromSms(message, 6))
                        }
                    } else {
                        smsPromise?.reject(E_EXCEPTION, "sms requestCode didn't match or result code wrong")
                    }
                } catch (e: Exception) {
                    smsPromise?.reject(E_EXCEPTION, "115: ${e.stackTraceToString()}")
                }
            }
        }

    init {
        reactApplicationContext.addActivityEventListener(activityEventListener)
    }

    @SuppressLint("UnspecifiedRegisterReceiverFlag")
    private fun registerReceiverIfNecessary(receiver: BroadcastReceiver) {
        try {
            val activity = currentActivity
            activity?.let { mActivity ->
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.UPSIDE_DOWN_CAKE) {
                    mActivity.registerReceiver(
                        /* receiver = */ receiver,
                        /* filter = */ IntentFilter(SmsRetriever.SMS_RETRIEVED_ACTION),
                        /* broadcastPermission = */ SmsRetriever.SEND_PERMISSION,
                        /* scheduler = */ null,
                        /* flags = */ Context.RECEIVER_EXPORTED
                    )
                } else {
                    mActivity.registerReceiver(
                        /* receiver = */ receiver,
                        /* filter = */ IntentFilter(SmsRetriever.SMS_RETRIEVED_ACTION),
                        /* broadcastPermission = */ SmsRetriever.SEND_PERMISSION,
                        /* scheduler = */ null
                    )
                }
                Log.d("SMS_GET", "BR Registered")
                isReceiverRegistered = true
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    private fun unregisterReceiver(receiver: BroadcastReceiver) {
        if (isReceiverRegistered && currentActivity != null) {
            try {
                val activity = currentActivity
                activity?.let { mActivity ->
                    mActivity.unregisterReceiver(receiver)
                    isReceiverRegistered = false
                    Log.d("SMS_GET", "BR UnRegistered")
                }
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
    }

    @ReactMethod
    fun startAutoRead(isSmsRetrieverMethod: Boolean = false, promise: Promise) {
        val activity = currentActivity
        activity?.let { mActivity ->
            this.isSmsRetrieverMethod = isSmsRetrieverMethod
            smsPromise = promise
            val client = SmsRetriever.getClient(mActivity)

            Log.d("SMS_GET", "isSmsRetrieverMethod ${this.isSmsRetrieverMethod}")

            val task = if (isSmsRetrieverMethod) {
                client.startSmsRetriever()
            } else {
                client.startSmsUserConsent(null)
            }

            task.addOnSuccessListener {
                Log.d("SMS_GET", "Task Success")
                registerReceiverIfNecessary(broadcastReceiver)
            }.addOnCanceledListener {
                    Log.d("SMS_GET", "Task cancelled")
                    promise.reject(ERROR_EXCEPTION, "Task cancelled")
                }.addOnFailureListener {
                    Log.d("SMS_GET", "Task Failed")
                    promise.reject(ERROR_EXCEPTION, "Task Failed")
                }.addOnCompleteListener {
                    Log.d("SMS_GET", "Task Complete")
                }
        }
    }


    fun getVerificationCodeFromSms(sms: String, smsCodeLength: Int): String =
        sms.filter { it.isDigit() }
            .substring(0 until smsCodeLength)

    companion object {
        const val ERROR_EXCEPTION = "Exception"
        const val SMS_CONSENT_REQUEST_CODE = 8243
        const val E_ACTIVITY_DOES_NOT_EXIST = "E_ACTIVITY_DOES_NOT_EXIST"
        const val E_EXCEPTION = "E_EXCEPTION"
    }
}