package com.jiohealth.module.biometrics

import android.hardware.biometrics.BiometricManager.Authenticators.BIOMETRIC_STRONG
import android.hardware.biometrics.BiometricManager.Authenticators.BIOMETRIC_WEAK
import android.hardware.biometrics.BiometricManager.Authenticators.DEVICE_CREDENTIAL
import android.security.keystore.KeyProperties
import java.security.spec.AlgorithmParameterSpec
import java.security.spec.RSAKeyGenParameterSpec

/**
 * Do bear in mind,
 * There are 2 kinds of KeyGenerators we wind up using depending on what we want to really do using the mechanisms here
 * 1. javax.crypto.KeyGenerator: This class is part of the Java Cryptography Extension (JCE) framework and is used for generating symmetric keys. Symmetric encryption uses the same key for both encryption and decryption. Algorithms for symmetric encryption include AES (Advanced Encryption Standard), DES (Data Encryption Standard), and others. The KeyGenerator class provides a generateKey() method which generates a SecretKey suitable for a specified algorithm.
 * 2. java.security.KeyPairGenerator: This class is part of the Java Cryptography Architecture (JCA) and is used for generating pairs of keys suitable for asymmetric encryption. Asymmetric encryption uses a pair of keys: a public key for encryption and a private key for decryption. Algorithms for asymmetric encryption include RSA, DSA (Digital Signature Algorithm), and others. The KeyPairGenerator class provides a generateKeyPair() method which generates a KeyPair object containing a pair of PublicKey and PrivateKey.
 *
 * For all intents and purposes , The code that is in place in this implementation is using the 2nd approach, where we are trying to generate a pair.
 * */
object Configuration {

    /**
     * KeyStore class supports several keystore, the details are available in the comment for the class KeyStore
     * Predefined Provider in the Android Operating System.A secure container for storing cryptographic keys and their properties.
     *
     * Algorithm - Supported API Levels
     * AndroidCAStore 14+
     * AndroidKeyStore 18+
     * BCPKCS12 1-8
     * BKS 1+
     * BouncyCastle 1+
     * PKCS12 1+
     * PKCS12-DEF 1-8
    */
    public const val KEYSTORE_NAME = "AndroidKeyStore"// OR KeyStore.getDefaultType() //returns BCK , bouncing castle
    public const val KEYSTORE_ALIAS = "biometric_key"  // user defined alias
    public const val AUTHENTICATORS = BIOMETRIC_STRONG or BIOMETRIC_WEAK or DEVICE_CREDENTIAL

    public const val isDeviceCredentialEnabled = AUTHENTICATORS and DEVICE_CREDENTIAL != 0

    /**
     * Known combinations of encryption related algorithms, block-mode and encryption
     * symmetric:
     * => algorithms: KEY_ALGORITHM_AES , block mode:  BLOCK_MODE_CBC, encryption padding: ENCRYPTION_PADDING_PKCS7
     * => algorithms: KEY_ALGORITHM_RSA , block mode:  BLOCK_MODE_ECB, encryption padding: ENCRYPTION_PADDING_RSA_PKCS1
     * asymmetric:
     * => algorithms: KEY_ALGORITHM_RSA , signature digest: DIGEST_SHA256 , signature padding: SIGNATURE_PADDING_RSA_PKCS1
     * */

    public const val ENCRYPTION_ALGORITHM = KeyProperties.KEY_ALGORITHM_RSA
    
    //encryption-block-mode and encryption padding is relevant for only symmetric encryption using javax.crypto.KeyGenerator
    public const val ENCRYPTION_BLOCK_MODE = ""
    public const val ENCRYPTION_PADDING = ""
   
    //signature-digest and signature-padding is relevant for asymmetric encryption using java.security.KeyPairGenerator
    public const val SIGNATURE_DIGESTS = KeyProperties.DIGEST_SHA256
    public const val SIGNATURE_PADDING = KeyProperties.SIGNATURE_PADDING_RSA_PKCS1

    /**
     * Signature Types
     * Algorithm    Supported API Levels
     * DSA  1+
     * DSAwithSHA1  1+
     * DSS  1-19
     * ECDSA  11+
     * ECDSAwithSHA1 11+
     * MD2withRSA 1-3
     * MD4withRSA 1-8
     * MD5withRSA 1+
     * MD5withRSA/ISO9796-2 1-8
     * NONEwithDSA 1+
     * NONEwithECDSA 11+
     * NONEwithRSA 17+
     * RSASSA-PSS 1-8
     * SHA1withDSA 1+
     * SHA1withECDSA 11+
     * SHA1withRSA 1+
     * SHA1withRSA/ISO9796-2 1-8
     * SHA1withRSA/PSS 23+
     * SHA224withDSA 20+
     * SHA224withECDSA 20+
     * SHA224withRSA 20+
     * SHA224withRSA/PSS 23+
     * SHA256withDSA 1+
     * SHA256withECDSA 11+
     * SHA256withRSA 1+
     * SHA256withRSA/PSS 23+
     * SHA384withECDSA 11+
     * SHA384withRSA 1+
     * SHA384withRSA/PSS 23+
     * SHA512withECDSA 11+
     * SHA512withRSA 1+
     * SHA512withRSA/PSS 23+
     */
    public const val SIGNATURE_TYPE = "SHA256withRSA"

    /**
     * Known Algorithm Specs
     * ECGenParameterSpec("secp256r1")
     * RSAKeyGenParameterSpec(2048, RSAKeyGenParameterSpec.F4)
     * */
    public val ALGORITHM_PARAMETER_SPEC : AlgorithmParameterSpec = RSAKeyGenParameterSpec(2048, RSAKeyGenParameterSpec.F4)
}