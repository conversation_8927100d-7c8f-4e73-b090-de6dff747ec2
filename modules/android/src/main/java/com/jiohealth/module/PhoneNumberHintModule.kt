package com.jiohealth.module

import android.app.Activity
import android.content.Intent
import android.content.IntentSender
import android.os.Bundle
import android.util.Log
import com.facebook.react.bridge.ActivityEventListener
import com.facebook.react.bridge.Promise
import com.facebook.react.bridge.ReactApplicationContext
import com.facebook.react.bridge.ReactContextBaseJavaModule
import com.facebook.react.bridge.ReactMethod
import com.google.android.gms.auth.api.credentials.Credential
import com.google.android.gms.auth.api.credentials.Credentials
import com.google.android.gms.auth.api.credentials.CredentialsApi
import com.google.android.gms.auth.api.credentials.CredentialsOptions
import com.google.android.gms.auth.api.credentials.HintRequest

class PhoneNumberHintModule(reactContext: ReactApplicationContext) : ReactContextBaseJavaModule(reactContext),
    ActivityEventListener {

    override fun getName(): String {
        return "PhoneNumberHintModule"
    }

    companion object {
        private const val CREDENTIAL_PICKER_REQUEST = 1
    }

    private var promise: Promise? = null

    init {
        reactContext.addActivityEventListener(this)
    }

    @ReactMethod
    fun showPhoneHint(promise: Promise) {
        Log.d("PhoneNumberHintModule", "showPhoneHint: 1")
        val activity = currentActivity

        if (activity == null) {
            promise.reject("Activity doesn't exist")
            return
        }

        this.promise = promise

        val hintRequest = HintRequest.Builder()
            .setPhoneNumberIdentifierSupported(true)
            .build()

        val options = CredentialsOptions.Builder()
            .forceEnableSaveDialog()
            .build()

        val intent = Credentials.getClient(activity, options)
            .getHintPickerIntent(hintRequest)

        try {
            activity.startIntentSenderForResult(
                intent.intentSender, CREDENTIAL_PICKER_REQUEST,
                null, 0, 0, 0, Bundle()
            )
        } catch (e: IntentSender.SendIntentException) {
            e.printStackTrace()
            promise.reject("IntentSenderException", e)
        }
    }

    override fun onActivityResult(activity: Activity?, requestCode: Int, resultCode: Int, data: Intent?) {
        if (requestCode == CREDENTIAL_PICKER_REQUEST && resultCode == Activity.RESULT_OK) {
            val credential: Credential? = data?.getParcelableExtra(Credential.EXTRA_KEY)
            if (credential != null) {
                promise?.resolve(credential.id)  // Phone number
            } else {
                promise?.resolve("")
            }
        } else if (resultCode == CredentialsApi.ACTIVITY_RESULT_NO_HINTS_AVAILABLE) {
            promise?.resolve("")
        }
    }

    override fun onNewIntent(p0: Intent?) {

    }

}