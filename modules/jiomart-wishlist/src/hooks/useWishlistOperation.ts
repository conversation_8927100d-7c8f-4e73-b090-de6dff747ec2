import {useMutation} from '@tanstack/react-query';
import {useEffect} from 'react';
import {useDispatch} from 'react-redux';
import {setWishlist} from '../slices/wishlistSlice';
import JMWishlistNetworkController from '@jm/jiomart-networkmanager/src/JMNetworkController/JMWishlistNetworkController';
import {JMSharedViewModel} from '../../../jiomart-common/src/JMSharedViewModel';

const wishlistController = new JMWishlistNetworkController();

const useWishlistOperation = () => {
  const dispatch = useDispatch();

  const getWishlistIds = useMutation({
    mutationFn: wishlistController.fetchWishlistIds,
    onSuccess: (response: any) => {
      dispatch(setWishlist(response));
    },
  });

  const addToWishlist = useMutation({
    mutationFn: wishlistController.addToWishlist,
    onSuccess: (_: any) => {
      getWishlistIds.mutate();
    },
  });

  const removeFromWishlist = useMutation({
    mutationFn: wishlistController.removeFromWishlist,
    onSuccess: (_: any) => {
      getWishlistIds.mutate();
    },
  });

  return {
    getWishlistIds,
    addToWishlist,
    removeFromWishlist,
  };
};

export default useWishlistOperation;
