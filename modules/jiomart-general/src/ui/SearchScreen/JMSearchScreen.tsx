import {ScrollView, Keyboard, FlatList, StyleSheet} from 'react-native';
import React, {useState, useCallback, useRef} from 'react';
import ScreenSlot, {DeeplinkHandler} from '../JMScreenSlot';
import {
  AppScreens,
  type ScreenProps,
} from '../../../../jiomart-common/src/JMAppScreenEntry';
import useJMSearchScreenController from './controller/useJMSearchScreenController';
import MultiSearch from './components/MultiSearch';
import RecentSearch from './components/RecentSearch';
import {SearchScreenContentProps} from './types/JMSearchScrennType';
import DiscoverMore from './components/DiscoverMore';
import TopCategories from './components/TopCategories';
import RecommendedProducts from './components/RecommendedProducts';
import SearchResultItem from './components/SearchResultItem';
import BottomSheet from '../BottomSheet/BottomSheet';
import MultiSearchBottomSheet from '../BottomSheet/MultiSearchBottomSheet';
import SearchHorizontalList from './components/SearchHorizontalList';
import {JioText} from '@jio/rn_components';
import NewProductGridCard from './components/NewProductGridCard';
import {
  isHyperLocal,
  isScheduleDelivery,
} from './controller/useSearchScreenController';
import {JioTypography} from '@jio/rn_components/src/index.types';
import {rh, rw} from '../../../../jiomart-common/src/JMResponsive';
import useRedirection from '../../hooks/useRedirection';

export type JProps = ScreenProps<typeof AppScreens.SEARCH_SCREEN>;

const SearchScreenContent = React.memo(
  ({
    sortedComponents,
    config,
    textInputRef,
    handleDiscoverMoreSearchResult,
    handleTrendingCategoryResult,
    handleRecentSearchResult,
    openMultiSearchBtmSheet,
    isSearchActive,
    searchResultData,
    searchResultFilteredData,
    categoriesData,
    brandsData,
    searchResulExtData,
    onBrandItemClick,
    onCategoryItemClick,
    onSearchResultItemClick,
  }: SearchScreenContentProps) => {
    const {redirectToPdp} = useRedirection();
    const renderItem = ({item, index}: any) => {
      return (
        <SearchResultItem
          key={`s_i_${index}`}
          onPress={() => {
            onSearchResultItemClick(item, config?.searchSuggestion_List?.cta);
          }}
          searchResultItem={item}
        />
      );
    };

    return (
      <ScrollView
        style={{backgroundColor: 'white'}}
        showsVerticalScrollIndicator={false}
        onScrollBeginDrag={() => Keyboard.dismiss()}>
        {isSearchActive ? (
          <>
            {searchResultData &&
            searchResultData?.items !== undefined &&
            searchResultData?.items?.length > 0 ? (
              <>
                {config?.searchSuggestion_List?.is_visible &&
                  searchResultFilteredData &&
                  searchResultFilteredData
                    .slice(
                      0,
                      config?.searchSuggestion_List?.maxItemsVisible ?? 5,
                    )
                    .map((item, index) => {
                      return renderItem({item, index});
                    })}

                {config?.searchSuggestion_Categories?.is_visible &&
                categoriesData?.current?.length > 0 ? (
                  <SearchHorizontalList
                    data={categoriesData.current}
                    title={
                      config?.searchSuggestion_Categories?.header_title ??
                      'Categories'
                    }
                    maxItemsVisible={
                      config?.searchSuggestion_Categories.maxItemsVisible ?? 4
                    }
                    borderRadius={8}
                    onPress={(l3CategoryName: string) => {
                      onCategoryItemClick(
                        l3CategoryName,
                        config?.searchSuggestion_Categories?.cta,
                      );
                    }}
                    showShadowTextChip
                  />
                ) : null}

                {config?.searchSuggestion_Brands?.is_visible &&
                brandsData?.current?.length > 0 ? (
                  <SearchHorizontalList
                    data={brandsData.current}
                    title={
                      config?.searchSuggestion_Brands?.header_title ?? 'Brands'
                    }
                    maxItemsVisible={
                      config?.searchSuggestion_Brands?.maxItemsVisible ?? 4
                    }
                    borderRadius={80}
                    onPress={(brand: string) => {
                      onBrandItemClick(
                        brand,
                        config?.searchSuggestion_Brands?.cta,
                      );
                    }}
                    showShadowTextChip
                  />
                ) : null}

                {config?.searchSuggestion_product?.is_visible &&
                searchResulExtData?.items &&
                searchResulExtData?.items.length > 0 ? (
                  <>
                    <JioText
                      text={
                        config?.searchSuggestion_product?.header_title ??
                        'Products'
                      }
                      appearance={JioTypography.HEADING_XXS}
                      color={'primary_grey_100'}
                      style={styles.recommendedTextHeading}
                    />
                    <FlatList
                      horizontal={true}
                      showsHorizontalScrollIndicator={false}
                      scrollEnabled={searchResulExtData?.items?.length > 2}
                      data={searchResulExtData?.items.slice(
                        0,
                        config.searchSuggestion_product.maxItemsVisible ?? 4,
                      )}
                      keyExtractor={(_, index) => index.toString()}
                      renderItem={({item}) => {
                        return (
                          <NewProductGridCard
                            productCode={item?.uid}
                            sellerId={item?.seller_id}
                            title={item?.name}
                            image={item?.medias?.[0]?.url}
                            sellingPrice={item?.price?.effective?.min}
                            stridePrice={item?.price?.marked?.min}
                            discount={item?.discount}
                            verticalCode={item?.attributes['vertical-code']}
                            isQuickDeliveryLable={{
                              hyperLocal: isHyperLocal(
                                item?.seller_id,
                                item?.attributes['vertical-code'],
                              ),
                              IcScheduleDelivery: isScheduleDelivery(
                                item?.seller_id,
                                item?.attributes['vertical-code'],
                              ),
                              displayText: 'Scheduled Delivery By',
                            }}
                            slug={item?.slug}
                            onPress={() => {
                              redirectToPdp({
                                item: {
                                  params: {
                                    slug: item?.slug,
                                  },
                                },
                                verticalCode: ['GROCERIES'],
                                vertical: item?.attributes?.['vertical-code'],
                              });
                            }}
                            onCartPress={() => {}}
                            onWishlistPress={() => {}}
                            sellable={item?.sellable}
                            itemSize={item?.sizes?.[0]}
                            storeId={item?.store_id}
                            articleAssignment={item?.article_assignment}
                          />
                        );
                      }}
                      contentContainerStyle={styles.container}
                    />
                  </>
                ) : null}
              </>
            ) : null}
          </>
        ) : (
          <>
            {sortedComponents?.map(component => {
              switch (component.name) {
                case 'shopping_list_block':
                  return config?.shopping_list_block?.is_visible ? (
                    <MultiSearch
                      key={component.name}
                      openMultiSearchBtmSheet={openMultiSearchBtmSheet}
                      textInputRef={textInputRef}
                      headerTitle={config?.shopping_list_block?.header_title}
                      subTitle={config?.shopping_list_block?.sub_title}
                    />
                  ) : null;
                case 'recent_search':
                  return config?.recent_search?.is_visible ? (
                    <RecentSearch
                      key={component.name}
                      headerTitle={config?.recent_search?.header_title}
                      visibleSearchKeywordCount={
                        config?.recent_search?.visible_search_keyword_count
                      }
                      updateChip={text => {
                        handleRecentSearchResult(
                          text,
                          config?.recent_search?.cta,
                        );
                      }}
                      buttonTitle={
                        config?.recent_search?.action_button[0]?.is_visible
                          ? config?.recent_search?.action_button[0]
                              ?.button_title
                          : ''
                      }
                    />
                  ) : null;
                case 'discover_more':
                  return config?.discover_more?.is_visible ? (
                    <DiscoverMore
                      key={component.name}
                      updateChip={text => {
                        handleDiscoverMoreSearchResult(
                          text,
                          config?.discover_more?.cta,
                        );
                      }}
                      headerTitle={config?.discover_more?.header_title}
                      visibleSearchKeywordCount={
                        config?.discover_more?.visible_search_keyword_count
                      }
                    />
                  ) : null;
                case 'popular_category':
                  return config?.popular_category?.is_visible ? (
                    <TopCategories
                      key={component.name}
                      updateCategory={(slug, page) => {
                        handleTrendingCategoryResult(
                          slug,
                          page,
                          config?.popular_category?.cta,
                        );
                      }}
                      headerTitle={config?.popular_category?.header_title}
                    />
                  ) : null;
                case 'recommended_product':
                  return config?.recommended_product?.is_visible ? (
                    <RecommendedProducts
                      key={component.name}
                      headerTitle={config?.recommended_product?.header_title}
                    />
                  ) : null;
                default:
                  return null;
              }
            })}
          </>
        )}
      </ScrollView>
    );
  },
);

const JMSearchScreen = (props: JProps) => {
  const {route, navigation} = props;
  const navigationBean = route.params;
  const [searchKeyword, setSearchValue] = useState('');
  const textInputRef = useRef(null);
  const {
    config,
    sortedComponents,
    handleRecentSearchResult,
    handleDiscoverMoreSearchResult,
    handleTrendingCategoryResult,
    openMultiSearchBtmSheet,
    closeMultiSearchBtmSheet,
    handleAutoComplete,
    onCategoryItemClick,
    onBrandItemClick,
    isSearchActive,
    searchResultData,
    searchResultFilteredData,
    categoriesData,
    brandsData,
    searchResulExtData,
    multiSearchBottomSheet,
    onSearchResultItemClick,
    onChangeMultiSearch,
  } = useJMSearchScreenController();

  const searchTextHandler = useCallback((text: string) => {
    handleAutoComplete(text);
  }, []);

  const searchScreenMainUI = useCallback(() => {
    return (
      <SearchScreenContent
        sortedComponents={sortedComponents}
        config={config}
        textInputRef={textInputRef}
        remoteConfig={config}
        handleRecentSearchResult={handleRecentSearchResult}
        handleDiscoverMoreSearchResult={handleDiscoverMoreSearchResult}
        handleTrendingCategoryResult={handleTrendingCategoryResult}
        openMultiSearchBtmSheet={openMultiSearchBtmSheet}
        closeMultiSearchBtmSheet={closeMultiSearchBtmSheet}
        isSearchActive={isSearchActive}
        searchResultData={searchResultData ?? []}
        searchResultFilteredData={searchResultFilteredData}
        categoriesData={categoriesData}
        brandsData={brandsData}
        searchResulExtData={searchResulExtData}
        onCategoryItemClick={onCategoryItemClick}
        onBrandItemClick={onBrandItemClick}
        onSearchResultItemClick={onSearchResultItemClick}
      />
    );
  }, [
    sortedComponents,
    config,
    handleRecentSearchResult,
    handleDiscoverMoreSearchResult,
    handleTrendingCategoryResult,
    openMultiSearchBtmSheet,
    closeMultiSearchBtmSheet,
    isSearchActive,
    searchResultData,
    searchResultFilteredData,
    categoriesData,
    brandsData,
    searchResulExtData,
    onCategoryItemClick,
    onBrandItemClick,
    onSearchResultItemClick,
  ]);

  return (
    <DeeplinkHandler
      navigationBean={navigationBean}
      navigation={navigation}
      children={bean => (
        <ScreenSlot
          navigationBean={navigationBean}
          navigation={navigation}
          bottomSheetContent={
            <>
              {config?.btm_sheet?.shopping_list_block?.is_visible ? (
                <BottomSheet
                  isStretchEnabled
                  maxHeightPercent={50}
                  enableKeyboarAvoidingView
                  onBackDropClick={closeMultiSearchBtmSheet}
                  onDrag={closeMultiSearchBtmSheet}
                  visible={multiSearchBottomSheet}>
                  <MultiSearchBottomSheet
                    textInputRef={textInputRef}
                    closeMultiSearchBtmSheet={closeMultiSearchBtmSheet}
                    onChangeMultiSearch={(text: string) =>
                      onChangeMultiSearch(
                        text,
                        config?.btm_sheet?.shopping_list_block?.cta,
                      )
                    }
                    config={config?.btm_sheet?.shopping_list_block}
                  />
                </BottomSheet>
              ) : null}
            </>
          }
          searchTextHandler={searchTextHandler}
          onSubmitHandler={handleDiscoverMoreSearchResult}
          children={_ => {
            return searchScreenMainUI();
          }}
        />
      )}
    />
  );
};

const styles = StyleSheet.create({
  recommendedTextHeading: {
    marginHorizontal: rw(24),
    marginTop: rh(20),
    marginBottom: rh(12),
  },
  container: {
    columnGap: 8,
    paddingHorizontal: 24,
    paddingVertical: 8,
  },
  flex: {
    flex: 1,
  },
});

export default JMSearchScreen;
