import {JioText} from '@jio/rn_components';
import {JioTypography} from '@jio/rn_components/src/index.types';
import React, {useEffect, useRef, useState} from 'react';
import {Dimensions, FlatList, StyleSheet} from 'react-native';
import TextChip from '../../../rn-components/custom/TextChip';

interface SearchHorizontalListProps {
  data: any;
  title: string;
  onPress: (brand: string) => void;
  maxItemsVisible?: number;
  borderRadius?: any;
  showShadowTextChip?: boolean;
}
const SCREEN_WIDTH = Dimensions.get('window').width;

const SearchHorizontalList = (props: SearchHorizontalListProps) => {
  const {
    title,
    maxItemsVisible,
    data,
    borderRadius,
    showShadowTextChip,
    onPress,
  } = props;
  const [scrollEnabled, setScrollEnabled] = useState(false);
  const flatListRef = useRef(null);

  useEffect(() => {
    let totalWidth = 0;

    data?.slice(0, maxItemsVisible).forEach(item => {
      const itemWidth = item.length * 10 + 40;
      totalWidth += itemWidth;
    });
    setScrollEnabled(totalWidth > SCREEN_WIDTH);
  }, [data, maxItemsVisible]);

  return (
    <>
      <JioText
        text={title ?? ''}
        appearance={JioTypography.HEADING_XXS}
        color={'primary_grey_100'}
        style={styles.headerTitle}
      />
      <FlatList
        ref={flatListRef}
        data={data?.slice(0, maxItemsVisible)}
        horizontal
        showsHorizontalScrollIndicator={false}
        scrollEnabled={scrollEnabled}
        keyExtractor={(_item, index) => `category-${title}-${index}`}
        contentContainerStyle={styles.container}
        renderItem={({item}) => {
          return (
            <TextChip
              onPress={() => {
                onPress(item);
              }}
              text={item?.display}
              containerStyle={[
                {
                  borderRadius: borderRadius ?? 80,
                },
                showShadowTextChip ? styles.shadowTextChip : null,
              ]}
            />
          );
        }}
      />
    </>
  );
};

export default React.memo(SearchHorizontalList);

const styles = StyleSheet.create({
  headerTitle: {
    marginTop: 20,
    marginBottom: 12,
    paddingHorizontal: 24,
  },
  shadowTextChip: {
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.08,
    shadowRadius: 4,
    elevation: 1.75,
    backgroundColor: '#ffff',
  },
  container: {
    paddingHorizontal: 24,
    paddingVertical: 4,
    columnGap: 12,
  },
});
