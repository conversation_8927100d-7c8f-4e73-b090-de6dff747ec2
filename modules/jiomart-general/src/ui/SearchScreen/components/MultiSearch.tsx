import React from 'react';
import {NativeModules, View} from 'react-native';
import {JioIcon, JioText} from '@jio/rn_components';
import {
  IconColor,
  IconKind,
  IconSize,
  JioTypography,
} from '@jio/rn_components/src/index.types';
import {TouchableWithoutFeedback} from 'react-native-gesture-handler';
import {MultiSearchProps} from '../types/JMSearchScrennType';
import styles from '../styles/MultiSearchStyles';

const {KeyboardHandling, JMRNUserModule} = NativeModules;

const MultiSearch = (props: MultiSearchProps) => {
  const {headerTitle, subTitle, openMultiSearchBtmSheet, textInputRef} = props;

  return (
    <TouchableWithoutFeedback
      style={[styles.cardElevated, styles.container]}
      onPress={() => {
        setTimeout(() => {
          textInputRef?.current?.focus();
        }, 600);
        KeyboardHandling?.nativeKeyboardEnabled(false);
        openMultiSearchBtmSheet();
      }}>
      <View style={styles.box}>
        <View>
          <JioText
            text={headerTitle}
            style={styles.headerTitle}
            appearance={JioTypography.HEADING_XXS}
            color={'primary_grey_100'}
          />
          <JioText
            text={subTitle}
            appearance={JioTypography.BODY_XXS}
            color={'primary_grey_80'}
          />
        </View>
        <JioIcon
          color={IconColor.PRIMARY60}
          kind={IconKind.DEFAULT}
          size={IconSize.SMALL}
          ic={'IcNext'}
          style={styles.icon}
        />
      </View>
    </TouchableWithoutFeedback>
  );
};

export default MultiSearch;
