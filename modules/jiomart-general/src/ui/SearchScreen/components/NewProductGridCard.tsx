import {View, TouchableOpacity, Text} from 'react-native';
import React from 'react';
import {JioText} from '@jio/rn_components';
import {JioTypography} from '@jio/rn_components/src/index.types';
import FastImage from 'react-native-fast-image';
import {rh, rw} from '../../../../../jiomart-common/src/JMResponsive';
import QCTag from './QCTag';
import WishlistToggleView from '../../Wishlist/WishlistToggleView';
import JMTag from './JMTag';
import {formatPrice} from '../../../../../jiomart-common/src/utils/JMCommonMethods';
import styles from '../styles/NewProductGridCartStyle';
import {ProductGridCardProps} from '../types/ProductGridCardType';
import {IcQcTagsLogoMini} from '../../../../../jiomart-common/src/icons/exportIcons';
import AddCtaView from '../../AddCta/AddCtaView';

const NewProductGridCard = (props: ProductGridCardProps) => {
  const {
    image,
    verticalCode,
    title,
    productCode,
    sellerId,
    sellingPrice,
    stridePrice,
    discount,
    isQuickDeliveryLable,
    onPress,
    slug,
    sellable,
    itemSize,
    storeId,
    articleAssignment,
    isMultiVariants,
    containerStyle,
    multiVariantsOnPress,
    addCta,
    gridConfig,
  } = props;

  const deliveryDate = new Date(Date.now() + 5 * 24 * 60 * 60 * 1000);

  const shouldShowHyperLocal =
    gridConfig?.deliveryConfig?.hyperLocal?.showHyperLocal !== false;
  const shouldShowScheduleDelivery =
    gridConfig?.deliveryConfig?.scheduleDelivery?.showScheduleDelivery !==
    false;

  const hyperLocalText =
    gridConfig?.deliveryConfig?.hyperLocal?.hyperLocalText ||
    'Quick \nDelivery';
  const scheduleDeliveryText =
    gridConfig?.deliveryConfig?.scheduleDelivery?.scheduleDeliveryText ||
    'Scheduled Delivery By';

  const shouldShowWishlist = gridConfig?.showWishListButton !== false;

  return (
    <TouchableOpacity
      style={[
        styles.container,
        {marginHorizontal: 1, marginBottom: 1},
        containerStyle,
      ]}
      activeOpacity={0.65}
      onPress={onPress}
      onLongPress={() => {}}>
      <FastImage
        source={{uri: image}}
        style={styles.image}
        resizeMode={FastImage.resizeMode.contain}
      />
      {shouldShowWishlist && (
        <WishlistToggleView
          uid={productCode}
          style={{
            position: 'absolute',
            right: 8,
            top: 8,
          }}
        />
      )}
      <Text
        numberOfLines={2}
        style={[
          styles.title,
          {
            lineHeight: 16,
            minHeight: 16 * 2,
            color: 'rgba(0, 0, 0, 0.65)',
            fontWeight: '500',
            fontSize: 12,
          },
        ]}>
        {title}
      </Text>
      <View style={styles.priceContainer}>
        <View>
          <JioText
            text={`₹${formatPrice(sellingPrice)}`}
            appearance={JioTypography.BODY_XS_BOLD}
            color={'primary_grey_100'}
            style={sellingPrice ? styles.activeOpacity : styles.deActiveOpacity}
          />
          <JioText
            text={`₹${formatPrice(stridePrice)}`}
            appearance={JioTypography.BODY_XS}
            color={'primary_grey_60'}
            style={[
              styles.stridePrice,
              sellingPrice != stridePrice && stridePrice
                ? styles.activeOpacity
                : styles.deActiveOpacity,
            ]}
          />
        </View>
        {discount ? (
          <JioText
            text={`${discount}`}
            appearance={JioTypography.BODY_XS_BOLD}
            color={'secondary_50'}
            style={[
              styles.discount,
              discount ? styles.activeOpacity : styles.deActiveOpacity,
            ]}
          />
        ) : null}
      </View>

      {verticalCode === 'GROCERIES' &&
        (sellable ? (
          <AddCtaView
            request={{
              uid: productCode,
              slug: slug,
              size: `${itemSize}`,
              meta: {vertical_code: verticalCode},
            }}
            style={{marginRight: 4}}
          />
        ) : (
          <JioText
            text="Out of Stock"
            appearance={JioTypography.BODY_XXS}
            style={{
              alignSelf: 'flex-end',
              marginRight: 4,
              paddingVertical: rh(9),
            }}
            color={'feedback_error_50'}
          />
        ))}
      <View
        style={{
          flex: 1,
          flexDirection: 'row',
          marginTop: 'auto',
          justifyContent: 'center',
        }}>
        {isQuickDeliveryLable?.hyperLocal && shouldShowHyperLocal ? (
          <QCTag
            icon={<IcQcTagsLogoMini />}
            color={'#E5F7EE'}
            textColor={'sparkle_70'}
            primaryText={'Delivery'}
            secondaryText={'in 10 to 30 mins'}
            style={[
              styles.hyperLocal,
              isQuickDeliveryLable?.hyperLocal ||
              isQuickDeliveryLable?.IcScheduleDelivery
                ? styles.activeOpacity
                : styles.deActiveOpacity,
            ]}
          />
        ) : isQuickDeliveryLable?.IcScheduleDelivery &&
          shouldShowScheduleDelivery ? (
          <JMTag
            color={'#E5F1F7'}
            textColor={'primary_60'}
            text={`${scheduleDeliveryText} ${deliveryDate.toLocaleDateString(
              'en-US',
              {
                weekday: 'long',
              },
            )}, ${deliveryDate.getDate()} ${deliveryDate.toLocaleDateString(
              'en-US',
              {month: 'long'},
            )}`}
            style={[
              styles.hyperLocal,
              {width: rw(120)},
              isQuickDeliveryLable?.hyperLocal ||
              isQuickDeliveryLable?.IcScheduleDelivery
                ? styles.activeOpacity
                : styles.deActiveOpacity,
            ]}
            minLines={2}
          />
        ) : null}
      </View>
    </TouchableOpacity>
  );
};

export default React.memo(NewProductGridCard);
