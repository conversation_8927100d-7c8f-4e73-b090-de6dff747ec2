import {useEffect} from 'react';
import {WishlistToggleProps} from '../types/WishlistToggleType';
import {Platform} from 'react-native';
import {useWishlistItems} from '@jm/jiomart-wishlist/src/hooks/useWishlist';
import useWishlistOperation from '@jm/jiomart-wishlist/src/hooks/useWishlistOperation';
import handleHapticFeedback from '@jm/jiomart-common/src/utils/JMHapticFeedback';

const useWishlistToggleViewController = (props: WishlistToggleProps) => {
  const {uid, style, onPressWishlist} = props;
  const wishlistToggle = useWishlistItems({uid});
  const {addToWishlist, removeFromWishlist} = useWishlistOperation();
  const actionWishlist = async () => {
    if (Platform.OS === 'android') {
      handleHapticFeedback('impactMedium');
    } else {
      handleHapticFeedback('impactLight');
    }
    // if (1 === 1) {
    //   console.log('Open Login');
    //   return;
    // }
    if (wishlistToggle) {
      removeFromWishlist.mutate({collection: 'products', item_id: `${uid}`});
      onPressWishlist?.('remove');
      return;
    }
    addToWishlist.mutate({collection: 'products', item_id: `${uid}`});
    onPressWishlist?.('add');
  };

  // useEffect(() => {
  //   // if (wishlistData?.wishlistDataMap?.includes?.(Number(uid))) {
  //   //   setWishlistIconToggle(true);
  //   // } else {
  //   //   setWishlistIconToggle(false);
  //   // }
  //   // eslint-disable-next-line react-hooks/exhaustive-deps
  // }, [uid]);

  return {style, actionWishlist, wishlistToggle};
};

export default useWishlistToggleViewController;
