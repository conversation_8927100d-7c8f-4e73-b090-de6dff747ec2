import {useEffect, useState} from 'react';
import {NativeModules} from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {
  addStringPref,
  getPrefString,
  removeStringPref,
} from '../../../../../jiomart-common/src/JMAsyncStorageHelper';
import {AsyncStorageKeys} from '../../../../../jiomart-common/src/JMConstants';
import {RecommendedItemsData} from '../types/RecommendedItemsData';
import useRecommendedProductsViewModel from '../viewmodel/useRecommededProductsViewModel';
import { JMSharedViewModel } from '../../../../../jiomart-common/src/JMSharedViewModel';

interface RecommendedProductsProps {
  headerTitle: string;
}

const {AuthModule} = NativeModules;

const useRecommendedProductsController = (props: RecommendedProductsProps) => {
  const {getRecommendedItems} = useRecommendedProductsViewModel();
  const {headerTitle} = props;
  useEffect(() => {
    recommendedItemsData();
  }, []);

  const [recommendedItemData, setRecommendedItemData] = useState<
    RecommendedItemsData | null | undefined
  >();
  const [loading, setLoading] = useState<boolean>(true);

  const recommendedItemsData = async () => {
    try {
      const isLoginData = JMSharedViewModel.Instance.loggedInStatus;
      if (isLoginData) {
        await removeStringPref(AsyncStorageKeys.RECOMMENDED_ITEMS);
        const data = await getPrefString(AsyncStorageKeys.RECOMMENDED_ITEMS);
        const formatedData = JSON.parse(data);
        if (formatedData?.items.length > 0) {
          setRecommendedItemData(formatedData);
        } else {
          const recommendProductsData = await getRecommendedItems();
          setRecommendedItemData(recommendProductsData);
          storeRecommendedItemsData(recommendProductsData);
        }
      }
    } catch (error) {
      console.error(' Failed recommendedItemsData fetch', error);
    } finally {
      setLoading(false);
    }
  };

  const storeRecommendedItemsData = async data => {
    await addStringPref(
      AsyncStorageKeys.RECOMMENDED_ITEMS,
      JSON.stringify(data),
    );
  };
  return {
    recommendedItemData,
    headerTitle,
    loading,
  };
};

export default useRecommendedProductsController;
