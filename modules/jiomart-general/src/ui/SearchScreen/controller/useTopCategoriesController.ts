import {useEffect, useState} from 'react';
import {useColor} from '@jio/rn_components';
import {AsyncStorageKeys} from '../../../../../jiomart-common/src/JMConstants';
import {
  addStringPref,
  getPrefString,
  removeStringPref,
} from '../../../../../jiomart-common/src/JMAsyncStorageHelper';
import {
  getChipHeight,
  getChipWidth,
} from '../../../../../jiomart-common/src/utils/JMCommonFunctions';
import {CustomCategoryItems} from '../types/JMAllCategoriesResponse';
import useAllCategoriesController from './useAllCategoriesController';
import {getConfigFileData} from '../../../../../jiomart-networkmanager/src/JMConfigFileManager/JMFileManager';
import {JMConfigFileName} from '../../../../../jiomart-networkmanager/src/JMConfigFileManager/JMFileName';

const useTopCategoriesController = () => {
  const {getAllCategoryApi} = useAllCategoriesController();
  const [topCategoriesData, setTopCategoriesData] = useState<
    CustomCategoryItems[]
  >([]);

  const [loading, setLoading] = useState(true);
  const frameBackgroundColor = useColor('feedback_error_20');

  const imageRatio =
    getChipWidth() < getChipHeight() ? getChipWidth() : getChipHeight();

  useEffect(() => {
    getTopCategoriesData();
  }, []);

  const fetchTopCategoriesData = async () => {
    getConfigFileData(
      JMConfigFileName.JMAllCategoriesConfigurationFileName,
      async content => {
        const data = await getAllCategoryApi(content?.topNavigation);
        const newdata = JSON.stringify(data);
        await storeTopCategoriesData(newdata);
        setTopCategoriesData(data);
        setLoading(false);
      },
    );
  };

  const getTopCategoriesData = async () => {
    fetchTopCategoriesData();
  };

  const storeTopCategoriesData = async (data: string) => {
    await addStringPref(AsyncStorageKeys.ALL_CATEGORIES, data);
  };

  return {
    topCategoriesData,
    imageRatio,
    frameBackgroundColor,
    loading,
  };
};

export default useTopCategoriesController;
