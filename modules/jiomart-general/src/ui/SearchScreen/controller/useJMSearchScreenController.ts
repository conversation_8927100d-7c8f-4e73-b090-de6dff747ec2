import {useEffect, useState, useRef} from 'react';
import {getConfigFileData} from '../../../../../jiomart-networkmanager/src/JMConfigFileManager/JMFileManager';
import {JMConfigFileName} from '../../../../../jiomart-networkmanager/src/JMConfigFileManager/JMFileName';
import {SearchComponent} from '../types/JMSearchScrennType';
import {useNavigation} from '@react-navigation/native';
import {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {navigateTo} from '../../../navigation/JMNavGraph';
import {
  navBeanObj,
  type NavigationBean,
} from '../../../../../jiomart-common/src/JMNavGraphUtil';
import {getBaseURL} from '../../../../../jiomart-networkmanager/src/JMEnvironmentConfig';
import {AsyncStorageKeys} from '../../../../../jiomart-common/src/JMConstants';
import {searchProducts} from '../../../../../jiomart-networkmanager/src/JMNetworkController/JMProductListingAPINetworkController';
import {
  addStringPref,
  getPrefString,
} from '../../../../../jiomart-common/src/JMAsyncStorageHelper';
import SearchService from '../../../../../jiomart-networkmanager/src/JMNetworkController/JMSearchAPINetworkController';
import {
  formatCategoryName,
  generateFilterReq,
} from '../../../../../jiomart-common/src/utils/JMCommonFunctions';

const useJMSearchScreenController = () => {
  const [config, setConfigData] = useState<any>(null);
  const [sortedComponents, setSortedComponents] = useState<SearchComponent[]>(
    [],
  );
  const [searchTextLocalState, setSearchTextLocalState] = useState('');
  const [multiSearchBottomSheet, setMultiSearchBottomSheet] = useState(false);
  const navigation = useNavigation<NativeStackNavigationProp<any>>();
  const searchApi = 'none';
  const [isSearchActive, setSearchActive] = useState<boolean>(false);
  const searchTimeout = useRef<NodeJS.Timeout | null>(null);
  const categoriesData = useRef<any>(null);
  const brandsData = useRef<any>(null);
  const textInputRef = useRef(null);

  const [searchResultData, setSearchResultData] = useState<any>();
  const [searchResulExtData, setSearchResulExtData] = useState<any>();

  const searchResultSet = new Set();
  searchResultData?.items?.forEach(item => {
    searchResultSet.add(item?.display?.toLowerCase());
  });
  const searchResultFilteredData = [...searchResultSet];

  useEffect(() => {
    console.log('Fetching config data');
    getConfigFileData(
      JMConfigFileName.JMSearchConfigurationFileName,
      content => {
        let searchConfig = content.searchConfig[0];
        setConfigData(searchConfig);
      },
    );
  }, []);

  useEffect(() => {
    if (config) {
      console.log('Processing config data');
      let configArray = Object.keys?.(config)?.map(key => ({
        name: key,
        ...config?.[key],
      }));
      const filteredAndSorted = configArray
        ?.filter(component => component.is_visible)
        ?.sort((a, b) => (a.sorted_order || 0) - (b.sorted_order || 0));

      if (
        JSON.stringify(filteredAndSorted) !== JSON.stringify(sortedComponents)
      ) {
        setSortedComponents(filteredAndSorted || []);
      }
    }
  }, [config, sortedComponents]);

  const handleAutoComplete = async (searchQuery: any) => {
    //  dispatch(setSearchText(searchQuery));
    setSearchTextLocalState(searchQuery);
    if (searchQuery?.length > 0) {
      setSearchActive(true);
      if (searchTimeout.current) {
        clearTimeout(searchTimeout.current);
      }
      const timeOutID = setTimeout(() => {
        fetchSearchResults(searchQuery);
        fetchSearchExtResults(searchQuery);
      }, 200);
      searchTimeout.current = timeOutID;
    } else {
      setSearchActive(false);
      searchResultData?.items.splice(0, searchResultData?.items?.length);
    }
  };

  const fetchSearchResults = async (searchQuery: string) => {
    const queryParams = {
      q: searchQuery,
    };
    console.log('This is called');

    try {
      const searchResultData = await SearchService.getSearchResults(
        queryParams,
      );
      setSearchResultData(searchResultData);
    } catch (error) {
      console.error('Error fetching search results:', error);
    }
  };

  const fetchSearchExtResults = async (searchQuery: string) => {
    const queryParams = {
      // filters: true,
      page_id: '%2A',
      page_size: 5,
      q: searchQuery,
    };
    await searchProducts(queryParams, null)
      .then(searchResultExtData => {
        handleCategoryAndBrandData(searchResultExtData);
        setSearchResulExtData(searchResultExtData);
        // console.warn('fetchSearchExtResults Data: ' + searchResultExtData);
      })
      .catch(error => {
        return error;
      });
  };

  const handleCategoryAndBrandData = (data: any) => {
    const result = {
      brand: [],
      categories: [],
    };

    data?.filters?.forEach(filter => {
      const keyName = filter.key?.name.toLowerCase();

      // Extract brand
      if (keyName === 'brand') {
        result.brand = filter.values;
      }

      // Extract category-related fields
      if (keyName === 'l3_category_names') {
        result.categories = filter.values?.map(v => {
          return {
            ...v,
            display: formatCategoryName(v?.display),
          };
        });
      }
    });

    brandsData.current = result.brand;
    categoriesData.current = result.categories;
  };

  const handleRecentSearchResult = (text: string, cta: NavigationBean) => {
    const actionUrl = `${getBaseURL()}${cta?.actionUrl?.replace(
      '[QUERY]',
      text,
    )}`;
    onChangeDiscoverMore({
      chip: text,
      cta,
      actionUrl,
      params: {
        query: text,
      },
    });
    setSearchTextLocalState(text);
  };

  const handleDiscoverMoreSearchResult = (
    text: string,
    cta: NavigationBean,
  ) => {
    const actionUrl = `${getBaseURL()}${cta?.actionUrl?.replace(
      '[QUERY]',
      text,
    )}`;
    onChangeDiscoverMore({
      chip: text,
      cta,
      actionUrl,
      params: {
        query: text,
      },
    });
    setSearchTextLocalState(text);
  };

  const onChangeDiscoverMore = ({chip, cta, actionUrl, params}: any) => {
    let keyWord = chip.replace(/\s*,\s*/g, ',');
    let searchText = keyWord
      .replace(/,+/g, ',')
      .replace(/,\s*,/g, ',')
      .replace(/^,|,$/g, '')
      .trim();
    keyWord = encodeURIComponent(keyWord);
    storeRecentSearchData(searchText);
    openPlpScreen({
      cta,
      actionUrl,
      params,
    });
  };

  const handleTrendingCategoryResult = (text: string, query: any, cta: any) => {
    const actionUrl = `${getBaseURL()}${cta?.actionUrl?.replace(
      '[SLUG]',
      text,
    )}`;
    openPlpScreen({
      cta,
      actionUrl,
      params: {
        slug: text,
        page: {
          department: query?.query?.department,
          category: query?.query?.category,
        },
      },
    });
  };

  const openPlpScreen = ({actionUrl, params, cta}: any) => {
    navigateTo(
      navBeanObj({
        ...cta,
        actionUrl,
        params,
      }),
      navigation,
    );
  };

  const openMultiSearchBtmSheet = () => {
    setMultiSearchBottomSheet(true);
  };

  const storeRecentSearchData = async (newData: string) => {
    const data = await getPrefString(AsyncStorageKeys.RECENT_SEARCH);
    let jsonData = [];
    if (data !== null) {
      jsonData = JSON.parse(data);
      jsonData = jsonData.filter((item: string) => item !== newData);
      jsonData.unshift(newData);
    } else {
      jsonData = [newData];
    }
    await addStringPref(
      AsyncStorageKeys.RECENT_SEARCH,
      JSON.stringify(jsonData),
    );
  };

  const closeMultiSearchBtmSheet = () => {
    setMultiSearchBottomSheet(false);
  };

  const onCategoryItemClick = (l3_category_names: any, cta: NavigationBean) => {
    const actionUrl = `${getBaseURL()}${cta?.actionUrl
      ?.replace('[QUERY]', searchTextLocalState.toLowerCase())
      ?.replace('[L3_CATEGORY_NAMES]', l3_category_names?.value)}`;

    openPlpScreen({
      cta,
      actionUrl,
      params: {
        query: searchTextLocalState.toLowerCase(),
        filter: generateFilterReq({
          l3_category_names: [l3_category_names?.value],
        }),
      },
    });
  };

  const onBrandItemClick = (brand: any, cta: NavigationBean) => {
    const actionUrl = `${getBaseURL()}${cta?.actionUrl
      ?.replace('[QUERY]', searchTextLocalState.toLowerCase())
      ?.replace('[BRAND]', brand?.value)}`;

    openPlpScreen({
      cta,
      actionUrl,
      params: {
        query: searchTextLocalState.toLowerCase(),
        filter: generateFilterReq({
          brand: [brand?.value],
        }),
      },
    });
  };

  const onSearchResultItemClick = (item: string, cta: NavigationBean) => {
    let keyWord = item.replace(/\s*,\s*/g, ',');
    keyWord = encodeURIComponent(keyWord);
    const actionUrl = `${getBaseURL()}${cta?.actionUrl?.replace(
      '[QUERY]',
      keyWord,
    )}`;
    openPlpScreen({
      cta,
      actionUrl,
      params: {
        query: keyWord,
      },
    });
  };

  const filterWhiteSearches = (text: string) => {
    var multiText = '';
    const subText = text.split(',');
    subText.map((item, index) => {
      if (item.trim() != '') {
        multiText = multiText + item + ',';
      }
    });
    text = multiText.slice(0, -1);
    return text;
  };

  const convertToPipeEncoded = (inputString: string): string => {
    // Split the input string by commas
    const items = inputString.split(',');
    // Join the items with '|'
    const pipeSeparated = items.join('|');
    // URL encode the string
    const encodedString = encodeURIComponent(pipeSeparated);
    return encodedString;
  };

  const onChangeMultiSearch = (searchString: string, cta: NavigationBean) => {
    searchString = filterWhiteSearches(searchString);
    const query = searchString?.split(',') ?? [];
    const actionUrl = `${getBaseURL()}${cta?.actionUrl
      ?.replace('[QUERY]', query?.[0]?.trim())
      ?.replace('[SLIST]', convertToPipeEncoded(searchString))}`;
    openPlpScreen({
      cta,
      actionUrl,
      params: {
        query,
      },
    });
  };

  return {
    config,
    sortedComponents,
    searchText: searchTextLocalState,
    handleRecentSearchResult,
    handleDiscoverMoreSearchResult,
    handleTrendingCategoryResult,
    openMultiSearchBtmSheet,
    closeMultiSearchBtmSheet,
    handleAutoComplete,
    onCategoryItemClick,
    onBrandItemClick,
    onSearchResultItemClick,
    onChangeMultiSearch,
    isSearchActive,
    searchResultData,
    searchResultFilteredData,
    categoriesData,
    brandsData,
    searchResulExtData,
    multiSearchBottomSheet,
  };
};

export default useJMSearchScreenController;
