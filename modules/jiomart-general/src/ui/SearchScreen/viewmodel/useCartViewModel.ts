import {useDispatch} from 'react-redux';
import {
  buyNowRequest,
  getCartRequest,
  initiateAddToCartRequest,
  resetCart,
  setHideAddCtaButton,
  updateCartRequest,
} from '../../../redux/actions/cartAction';

const useCartViewModel = () => {
  const dispatch = useDispatch();
  const CART_QUERY_PARAMS = {
    i: true,
    b: true,
    buy_now: false,
  };

  return {
    fetchCart: () => dispatch(getCartRequest(CART_QUERY_PARAMS)),
    initiateAddToCart: (body: any) =>
      dispatch(initiateAddToCartRequest(CART_QUERY_PARAMS, body)),
    updateAddToCart: (body: any) =>
      dispatch(updateCartRequest(CART_QUERY_PARAMS, body)),
    buyNow: (body: any) =>
      dispatch(buyNowRequest({...CART_QUERY_PARAMS, buy_now: true}, body)),
    resetCart: () => dispatch(resetCart()),
    setHideButton: (uid: any, val: any) =>
      dispatch(setHideAddCtaButton(uid, val)),
  };
};

export default useCartViewModel;
