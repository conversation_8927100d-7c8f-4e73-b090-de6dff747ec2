import {NativeModules} from 'react-native';
import SearchService from '../../../../../jiomart-networkmanager/src/JMNetworkController/JMSearchAPINetworkController';
import {
  getPrefString,
  removeStringPref,
} from '../../../../../jiomart-common/src/JMAsyncStorageHelper';
import {AsyncStorageKeys} from '../../../../../jiomart-common/src/JMConstants';
import { isNullOrUndefinedOrEmpty } from '../../../../../jiomart-common/src/JMObjectUtility';

const useRecommendedProductsViewModel = () => {
  const getRecommendedItems = async () => {
    try {
      const user = await getPrefString(AsyncStorageKeys.USER_DETAILS)

      const userDetails = !isNullOrUndefinedOrEmpty(user) ? JSON.parse(user ?? '') : {};
      const queryParams = {
        userid: userDetails?._id,
      };
      console
      const apiData = await SearchService.fetchRecommendedItems(queryParams);
      const itemCodesArray = apiData?.data?.details?.map(
        item => item?.item_code,
      );

      // Call service to get products serviceability based on item codes
      if (itemCodesArray && itemCodesArray.length > 0) {
        console.log(itemCodesArray, 'response -- ');
        return await getProductsServiceabilityItems(itemCodesArray);
      }
    } catch (error) {
      console.error('getRecommendedItems API Failed', error);
    }
  };

  const getProductsServiceabilityItems = async (items_codes: Array<string>) => {
    try {
      const body = items_codes;
      return await SearchService.fetchRecommendedItemsServiceability(body).then(
        recommendedItemsData => {
          return recommendedItemsData;
        },
      );
    } catch (error) {
      console.error('getProductsServiceabilityItems API Failed', error);
    }
  };

  return {
    getRecommendedItems,
  };
};

export default useRecommendedProductsViewModel;
