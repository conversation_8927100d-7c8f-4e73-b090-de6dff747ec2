import type {NavigationBean} from '../../../../../jiomart-common/src/JMNavGraphUtil';
import {AutoCompleteResponse, Item} from './JMSearchAutoCompleteResponse';

export type SearchComponent = {
  name: string;
  is_visible: boolean;
  header_title?: string;
  maxItemsVisible?: number;
  sorted_order?: number;
  visible_search_keyword_count?: number;
  default_image?: string;
  is_buy_again_api_enabled?: boolean;
  is_buy_again_appear_first?: boolean;
  is_recommended_product_api_enabled?: boolean;
  sub_title?: string;
  action_button?: Array<{
    is_visible?: boolean;
    button_title?: string;
    [key: string]: any;
  }>;
  categoryViewContent?: Array<{
    [key: string]: any;
  }>;
};

export interface MultiSearchBottomSheetProps {
  textInputRef: any;
  onClearAll?: () => void;
  closeMultiSearchBtmSheet?: () => void;
  onChangeMultiSearch: (value: string) => void;
  config: any;
}

export interface MultiSearchProps {
  headerTitle: string;
  subTitle: string;
  openMultiSearchBtmSheet: Function;
  textInputRef: any;
}

export interface RecentSearchProps {
  updateChip: Function;
  headerTitle: string;
  visibleSearchKeywordCount: number;
  buttonTitle: string;
}

export interface SearchScreenContentProps {
  sortedComponents: SearchComponent[];
  config: any;
  textInputRef: React.RefObject<any>;
  remoteConfig: any;
  handleRecentSearchResult: (text: string, cta: NavigationBean) => void;
  handleDiscoverMoreSearchResult: (text: string, cta: NavigationBean) => void;
  handleTrendingCategoryResult: (
    text: string,
    page: any,
    cta: NavigationBean,
  ) => void;
  openMultiSearchBtmSheet: () => void;
  closeMultiSearchBtmSheet: () => void;
  onCategoryItemClick: (l3CategoryName: any, cta: NavigationBean) => void;
  onBrandItemClick: (brand: any, cta: NavigationBean) => void;
  onSearchResultItemClick: (item: any, cta: NavigationBean) => void;
  isSearchActive: boolean;
  searchResultData: AutoCompleteResponse;
  searchResultFilteredData: any;
  categoriesData: any;
  brandsData: any;
  searchResulExtData: any;
}

export interface DiscoverMoreProps {
  updateChip: Function;
  headerTitle: string;
  visibleSearchKeywordCount: number;
  buttonTitle: string;
}
