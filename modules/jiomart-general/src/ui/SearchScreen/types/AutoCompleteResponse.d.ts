export type AutoCompleteResponse = Item[];

export interface Item {
  type: string;
  display: string;
  departments?: boolean;
  logo: Logo;
  _custom_json: any; // You can define a proper type for _custom_json if needed
  action: Action;
}

export interface Logo {
  type: string;
  url: string;
}

export interface Action {
  page: Page;
  type: string;
}

export interface Page {
  type: string;
  query: {
    q: string;
  };
}
