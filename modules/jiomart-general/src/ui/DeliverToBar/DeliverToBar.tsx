import {TouchableOpacity, View} from 'react-native';
import React, {forwardRef} from 'react';
import {JioIcon, JioText} from '@jio/rn_components';
import {
  IconColor,
  IconSize,
  JioTypography,
} from '@jio/rn_components/src/index.types';
import Animated from 'react-native-reanimated';
import type {
  DeliverToBarProps,
  DeliverToBarRef,
} from './types/DeliverToBarType';
import useDeliverToBar from './hooks/useDeliverToBar';
import {styles} from './styles/DeliverToBarStyle';

const DeliverToBar = forwardRef<DeliverToBarRef, DeliverToBarProps>(
  (props: DeliverToBarProps, ref: React.Ref<DeliverToBarRef>) => {
    const {text, onPress, deliverToBarStyle, primary20} = useDeliverToBar(
      props,
      ref,
    );

    return (
      <Animated.View style={[styles.negZindex, deliverToBarStyle]}>
        <TouchableOpacity
          style={[
            styles.container,
            {
              backgroundColor: primary20,
            },
          ]}
          activeOpacity={0.65}
          onPress={onPress}>
          <View style={styles.wrapper}>
            <JioIcon
              ic="IcLocation"
              color={IconColor.GREY80}
              size={IconSize.SMALL}
            />
            <JioText
              text={text ?? ''}
              color={'primary_grey_100'}
              appearance={JioTypography.BODY_S}
            />
          </View>
          <JioIcon ic="IcChevronDown" color={IconColor.PRIMARY60} />
        </TouchableOpacity>
      </Animated.View>
    );
  },
);

export default React.memo(DeliverToBar);
