import {
  useAnimatedStyle,
  useSharedValue,
  withTiming,
} from 'react-native-reanimated';
import type {
  UseDeliverToBarProps,
  UseDeliverToBarRef,
} from '../types/DeliverToBarType';
import {useColor} from '@jio/rn_components';
import {useImperativeHandle} from 'react';

const useDeliverToBar = (
  props: UseDeliverToBarProps,
  ref?: React.Ref<UseDeliverToBarRef>,
) => {
  const primary20 = useColor('primary_20');
  const prevScrollY = useSharedValue(0);
  const isVisible = useSharedValue(true);

  const offsetAnim = 40;
  const deliverToBarStyle = useAnimatedStyle(() => {
    const translateY = withTiming(isVisible.value ? 0 : -offsetAnim, {
      duration: 500,
    });

    const height = withTiming(isVisible.value ? offsetAnim : 0, {
      duration: 500,
    });

    const opacity = withTiming(Number(isVisible.value), {
      duration: 500,
    });

    return {
      transform: [{translateY}],
      height,
      opacity,
    };
  });

  useImperativeHandle(ref, () => ({
    onScroll: (value: any) => {
      if (value < 0) {
        return;
      }
      isVisible.value = value <= prevScrollY.value;
      prevScrollY.value = value;
    },
  }));

  return {
    ...props,
    primary20,
    deliverToBarStyle,
  };
};

export default useDeliverToBar;
