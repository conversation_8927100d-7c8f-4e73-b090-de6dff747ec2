import React from 'react';
import {TouchableOpacity} from 'react-native';
import {CardHeart} from '@jm/jiomart-common/src/icons/exportIcons';
import {WishlistToggleProps} from '../SearchScreen/types/WishlistToggleType';
import MicroScale from '@jm/jiomart-common/src/Animation/Micro/MicroScale';
import useWishlistToggleViewController from '../SearchScreen/controller/useWishlistToggleViewController';

const WishlistToggleView = (props: WishlistToggleProps) => {
  const {actionWishlist, style, wishlistToggle} =
    useWishlistToggleViewController(props);
  return (
    <TouchableOpacity onPress={actionWishlist} style={style} hitSlop={8}>
      <MicroScale scale={1.5}>
        <CardHeart
          fill={wishlistToggle ? '#FC6770' : '#fff'}
          strokeWidth={wishlistToggle ? '0' : '1'}
        />
      </MicroScale>
    </TouchableOpacity>
  );
};

export default WishlistToggleView;
