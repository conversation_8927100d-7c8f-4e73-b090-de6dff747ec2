import {SafeAreaView, View} from 'react-native';
import React from 'react';
import {JioButton, JioText} from '@jio/rn_components';
import {
  ButtonSize,
  JioTypography,
  type JioButtonProps,
  type JioTextProps,
} from '@jio/rn_components/src/index.types';
import {styles} from '../styles/NegativeScreenUIStyle';
import FastImage from 'react-native-fast-image';
import {rw} from '../../../jiomart-common/src/JMResponsive';

/*
uses
<NegativeScreenUI
  logoSvg={<NoProducts />}
  title={'Oopps!'}
  subTitle={
    'Slow or No internet connection. Please check your internet settings.'
  }
  isButtonVisible
  buttonTitle={'Refresh'}
  onPress={() => {
    console.log('clicked ');
  }}
/>; */
export interface NegativeScreenUIProps {
  image?: {
    uri: string;
    size?: number;
  };
  title?: JioTextProps;
  subTitle?: JioTextProps;
  isButtonVisible?: boolean;
  shouldShowContentInCenter?: boolean;
  button?: JioButtonProps;
  onPress?: () => void;
  offset?: number;
}

const NegativeScreenUI = (props: NegativeScreenUIProps) => {
  const {
    image,
    title,
    subTitle,
    isButtonVisible,
    shouldShowContentInCenter = true,
    offset,
    button,
    onPress,
  } = props;
  return (
    <SafeAreaView style={styles.flex}>
      <View style={styles.container}>
        <View
          style={[shouldShowContentInCenter ? styles.center : styles.start]}>
          <View style={styles.content}>
            {image?.uri ? (
              <FastImage
                source={{uri: image?.uri}}
                resizeMode={FastImage.resizeMode.contain}
                style={[
                  styles.image,
                  {
                    width: rw(image?.size ?? 164),
                    height: rw(image?.size ?? 164),
                  },
                ]}
              />
            ) : null}

            {title ? (
              <JioText
                appearance={JioTypography.HEADING_XXS}
                color={'primary_grey_100'}
                maxLines={1}
                textAlign="center"
                {...title}
                style={styles.marginBottom12}
              />
            ) : null}
            {subTitle ? (
              <JioText
                appearance={JioTypography.BODY_XXS}
                color={'primary_grey_80'}
                textAlign="center"
                {...subTitle}
              />
            ) : null}
          </View>
        </View>
        {isButtonVisible ? (
          <View style={[styles.bottom, {marginBottom: offset}]}>
            <JioButton
              title=""
              size={ButtonSize.LARGE}
              stretch
              onClick={onPress}
              {...button}
            />
          </View>
        ) : null}
      </View>
    </SafeAreaView>
  );
};

export default NegativeScreenUI;
