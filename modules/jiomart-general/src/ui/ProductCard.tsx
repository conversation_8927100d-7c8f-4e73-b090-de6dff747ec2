import React from 'react';
import {
  View,
  StyleSheet,
  TouchableOpacity,
  ViewStyle,
  StyleProp,
} from 'react-native';
import JioText from '@jio/rn_components/src/components/JioText/JioText';
import {
  JioTypography,
  type JioColor,
  type JioIconProps,
} from '@jio/rn_components/src/index.types';
import {JioIcon} from '@jio/rn_components';
import FastImage from 'react-native-fast-image';
// import {formatPrice} from '../../utilities/methods/utility';
import JMTag from './JMTag';
import {rh, rw} from '../../.../../../jiomart-common/src/JMResponsive';
import AddCtaView from './AddCta/AddCtaView';
import WishlistToggleView from './Wishlist/WishlistToggleView';
import {formatPrice} from '../../../jiomart-common/src/utils/JMStringUtility';

interface ProductCardProps {
  imageUrl?: string;
  disableWishlist?: boolean;
  disableAddToCart?: boolean;
  shouldShowVeg?: boolean;
  vegIcon?: JioIconProps;
  shouldShowSmartBazzar?: boolean;
  smartBazzarImage?: string;
  tag?: string;
  tagColor?: string;
  tagBackgroundColor?: string;
  disableTag?: boolean;
  uid?: any;
  slug?: string;
  title?: string;
  titleColor?: string;
  titleMaxLine?: number;
  effectivePrice?: number;
  effectivePriceColor?: JioColor;
  markedPrice?: number;
  markedPriceColor?: JioColor;
  discount?: string;
  discountBackgroundColor?: string;
  discountColor?: JioColor;
  onMultiVariantPress?: () => void;
  shouldShowMultiVariant?: boolean;
  size?: string;
  totalMultiVariant?: string;
  totalMultiVariantColor?: JioColor;
  multiVariantColor?: JioColor;
  multiVariantIcon?: JioIconProps;
  addToCartTitle?: string;
  verticalCode?: string;
  shouldShowOutofStock?: boolean;
  outOfStockText?: string;
  outOfStockColor?: JioColor;
  offers?: any[];
  onPress: () => void;
  style?: StyleProp<ViewStyle>;
}

interface ImageContainerProps {
  imageUrl?: string;
  disableWishlist?: boolean;
  shouldShowVeg?: boolean;
  vegIcon?: JioIconProps;
  shouldShowSmartBazzar?: boolean;
  smartBazzarImage?: string;
  uid?: number;
  imageStyle?: any;
}

const ImageContainer = ({
  imageUrl,
  disableWishlist,
  shouldShowSmartBazzar,
  shouldShowVeg,
  vegIcon,
  smartBazzarImage,
  uid,
  imageStyle,
}: ImageContainerProps) => {
  return (
    <View>
      <View style={[styles.imageContainer, imageStyle]}>
        <FastImage
          source={{uri: imageUrl}}
          style={[styles.image, imageStyle]}
          resizeMode={FastImage.resizeMode.contain}
        />
        {!disableWishlist ? (
          <WishlistToggleView uid={uid} style={styles.wishlist} />
        ) : null}
        {shouldShowVeg ? <JioIcon {...vegIcon} style={styles.veg} /> : null}
        {shouldShowSmartBazzar && (
          <FastImage
            source={{uri: smartBazzarImage}}
            resizeMode={FastImage.resizeMode.contain}
            style={styles.smartBazaar}
          />
        )}
      </View>
    </View>
  );
};

interface PriceInfoProps {
  effectivePrice: number;
  effectivePriceColor: JioColor;
  markedPrice: number;
  markedPriceColor: JioColor;
  discount?: string;
  discountBackgroundColor?: string;
  discountColor?: JioColor;
  containerStyle?: any;
}

const PriceInfo = ({
  effectivePrice,
  effectivePriceColor,
  markedPrice,
  markedPriceColor,
  discount,
  discountBackgroundColor,
  discountColor,

  containerStyle,
}: PriceInfoProps) => {
  return (
    <View style={containerStyle}>
      <View style={styles.priceContainer}>
        <JioText
          text={`₹${formatPrice(effectivePrice)}`}
          appearance={JioTypography.BODY_S_BOLD}
          color={effectivePriceColor}
          maxLines={2}
          ellipsizeMode="tail"
        />
        {discount && (
          <JioText
            text={`₹${formatPrice(markedPrice)}`}
            appearance={JioTypography.BODY_XXS}
            color={markedPriceColor}
            style={styles.discount}
            maxLines={2}
            ellipsizeMode="tail"
          />
        )}
      </View>
      <View>
        {discount && (
          <JMTag
            color={discountBackgroundColor as string}
            textColor={discountColor}
            text={discount}
          />
        )}
      </View>
    </View>
  );
};

interface OffersSectionProps {
  offers?: {
    icon: JioIconProps;
    title?: string;
    textColor?: JioColor;
  }[];
}

const OffersSection: React.FC<OffersSectionProps> = ({offers}) => {
  return (
    <View style={styles.offersSection}>
      {offers?.map((item, index) => {
        return (
          <View style={styles.offerRow} key={`offer-${index}`}>
            <JioIcon {...item.icon} />
            <JioText
              text={item?.title ?? ''}
              appearance={JioTypography.BODY_XXS}
              color={item?.textColor}
              maxLines={2}
              ellipsizeMode="tail"
            />
          </View>
        );
      })}
    </View>
  );
};

const ProductCard = ({
  imageUrl,
  disableWishlist,
  disableAddToCart,
  shouldShowSmartBazzar,
  shouldShowVeg,
  vegIcon,
  smartBazzarImage,
  tag,
  tagBackgroundColor,
  tagColor,
  disableTag,
  uid,
  slug,
  title,
  titleColor,
  titleMaxLine,
  effectivePrice,
  effectivePriceColor,
  markedPrice,
  markedPriceColor,
  discount,
  discountBackgroundColor,
  discountColor,
  onMultiVariantPress,
  shouldShowMultiVariant,
  size,
  totalMultiVariant,
  multiVariantColor,
  multiVariantIcon,
  totalMultiVariantColor,
  addToCartTitle,
  verticalCode,
  shouldShowOutofStock,
  outOfStockColor,
  outOfStockText,
  offers,
  onPress,
  style,
}: ProductCardProps) => {
  return (
    <TouchableOpacity
      onPress={onPress}
      onLongPress={() => {}}
      activeOpacity={0.9}
      style={style}>
      <View style={styles.contentContainer}>
        <ImageContainer
          imageUrl={imageUrl}
          disableWishlist={disableWishlist}
          shouldShowVeg={shouldShowVeg}
          vegIcon={vegIcon}
          shouldShowSmartBazzar={shouldShowSmartBazzar}
          smartBazzarImage={smartBazzarImage}
          uid={uid}
        />

        <View style={styles.detailsContainer}>
          {!disableTag && tag ? (
            <JMTag
              color={tagBackgroundColor as string}
              text={tag ?? ''}
              textColor={tagColor as JioColor}
              appearance={JioTypography.BODY_XXS}
            />
          ) : null}

          <JioText
            text={title}
            appearance={JioTypography.BODY_XXS}
            color={titleColor as JioColor}
            maxLines={titleMaxLine}
          />

          <PriceInfo
            effectivePrice={effectivePrice as number}
            effectivePriceColor={effectivePriceColor as JioColor}
            markedPrice={markedPrice as number}
            markedPriceColor={markedPriceColor as JioColor}
            discount={discount}
            discountBackgroundColor={discountBackgroundColor}
            discountColor={discountColor}
            containerStyle={styles.price}
          />

          {shouldShowMultiVariant && (
            <>
              <TouchableOpacity
                onPress={onMultiVariantPress}
                style={styles.multiVariant}>
                <JioText
                  text={size ?? ''}
                  color={multiVariantColor}
                  appearance={JioTypography.BODY_XXS_BOLD}
                />
                <JioIcon {...multiVariantIcon} />
              </TouchableOpacity>
              <JioText
                text={totalMultiVariant}
                appearance={JioTypography.BODY_XXS}
                color={totalMultiVariantColor}
              />
            </>
          )}

          {!disableAddToCart ? (
            <AddCtaView
              request={{
                uid: uid,
                slug: slug,
                size: `${size}`,
                meta: {vertical_code: verticalCode},
              }}
              title={addToCartTitle ?? ''}
              style={styles.addToCart}
            />
          ) : null}
          {shouldShowOutofStock && (
            <JioText
              text={outOfStockText ?? ''}
              appearance={JioTypography.BODY_XXS}
              style={styles.outOfStock}
              color={outOfStockColor}
            />
          )}
        </View>
      </View>

      <OffersSection offers={offers} />
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  contentContainer: {
    flexDirection: 'row',
    gap: 12,
  },
  detailsContainer: {
    flex: 1,
  },

  imageContainer: {
    width: rw(108),
    borderRadius: 16,
  },
  smartBazaar: {
    marginTop: 4,
    width: '100%',
    height: 20,
  },
  image: {
    width: rw(108),
    height: rw(108),
    borderRadius: 8,
    backgroundColor: '#F5F5F5',
  },
  wishlist: {
    position: 'absolute',
    right: 8,
    top: 8,
  },
  veg: {
    padding: 4,
    left: 0,
    bottom: 26,
    position: 'absolute',
  },

  discount: {textDecorationLine: 'line-through'},
  priceContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 2,
    gap: 4,
  },
  price: {
    flexDirection: 'row',
    gap: 4,
    alignItems: 'center',
  },
  multiVariant: {flexDirection: 'row', alignItems: 'center'},
  addToCart: {marginBottom: 8},
  outOfStock: {
    alignSelf: 'flex-end',
    marginRight: 4,
    marginTop: 'auto',
    paddingVertical: rh(8),
  },
  offersSection: {
    paddingTop: 8,
    rowGap: 4,
  },
  offerRow: {flexDirection: 'row', alignItems: 'center', columnGap: 4},
});

export default React.memo(ProductCard);
