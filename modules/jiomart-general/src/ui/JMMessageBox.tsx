import {View, StyleSheet} from 'react-native';
import React from 'react';
import {
  JioTypography,
  type JioColor,
  type JioIconProps,
  type JioTextProps,
} from '@jio/rn_components/src/index.types';
import {JioText, useColor} from '@jio/rn_components';
import {JioIcon} from '@jio/rn_components';
import {StyleProp} from 'react-native';
import {ViewStyle} from 'react-native';

export interface JMMessageBoxProps {
  title?: JioTextProps;
  subTitle?: JioTextProps;
  leftIcon?: JioIconProps;
  backgroundColor?: JioColor;
  style?: StyleProp<ViewStyle>;
}
const JMMessageBox: React.FC<JMMessageBoxProps> = ({
  title,
  subTitle,
  leftIcon,
  style,
  backgroundColor,
}) => {
  const bgColor = useColor(backgroundColor as JioColor);
  return (
    <View style={[style, {backgroundColor: bgColor}, styles.container]}>
      {leftIcon?.ic ? <JioIcon {...leftIcon} /> : null}
      <View style={styles.messageViewWithIcon}>
        <JioText
          appearance={JioTypography.BODY_XXS_BOLD}
          color={'black'}
          {...title}
        />
        <JioText
          appearance={JioTypography.BODY_XXS}
          color={'primary_grey_80'}
          {...subTitle}
        />
      </View>
    </View>
  );
};

export default JMMessageBox;

const styles = StyleSheet.create({
  container: {
    padding: 16,
    flexDirection: 'row',
    alignItems: 'center',
    columnGap: 12,
    borderRadius: 16,
  },
  messageViewWithIcon: {
    rowGap: 4,
    flexShrink: 1,
  },
});
