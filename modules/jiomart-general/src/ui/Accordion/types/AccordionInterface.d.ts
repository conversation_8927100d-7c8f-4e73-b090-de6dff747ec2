import {StyleProp, ViewStyle} from 'react-native';

export interface AccordionProps {
  iconType?: 'arrow' | 'plus';
  allowMultiple?: boolean;
  children: React.ReactNode;
  sameAccordionHeader?: React.ReactNode;
  onChange?: (expandedAccordionPanel: number) => void;
  style?: StyleProp<ViewStyle>;
}

export interface AccordionPanelProps {
  accordionHeader?: React.ReactNode;
  children: React.ReactNode;
  open?: boolean;
  iconType?: 'arrow' | 'plus';
  disable?: boolean;
  onPress?: () => void;
  style?: StyleProp<ViewStyle>;
}
