import {LayoutAnimation, Pressable, View} from 'react-native';
import React from 'react';
import {styles} from './styles/AccordionStyleSheet';
import useAccordion from './hook/useAccordion';
import {AccordionPanelProps, AccordionProps} from './types/AccordionInterface.d';
import {JioIcon, useColor} from '@jio/rn_components';
import {IconColor} from '@jio/rn_components/src/index.types';
import {useDerivedValue, withTiming} from 'react-native-reanimated';
// import MicroTap from '../Animation/Micro/MicroTap';
// import Chevron from '../../screens/Chevron';

const Accordion = (props: AccordionProps) => {
  const {
    iconType = 'arrow',
    allowMultiple = false,
    children,
    sameAccordionHeader,
    onChange,
    style,
  } = props;

  const [Children, openAccordionPanel, handleAccordionEvent] = useAccordion(
    children,
    allowMultiple,
  );
  return (
    <View style={[styles.accordion, style]}>
      {Children.map((child, index) => {
        return (
          <AccordionPanel
            key={`acc-panel-${index}`}
            iconType={iconType}
            accordionHeader={
              sameAccordionHeader
                ? sameAccordionHeader
                : child.props?.accordionHeader
            }
            open={openAccordionPanel[index] ?? child.props?.open}
            disable={child.props?.disable}
            onPress={() => {
              LayoutAnimation.configureNext(
                LayoutAnimation.Presets.easeInEaseOut,
              );
              if (onChange) {
                onChange(index);
              }
              handleAccordionEvent(index);
            }}
            style={child.props?.style}>
            {child.props?.children}
          </AccordionPanel>
        );
      })}
    </View>
  );
};

const AccordionPanel = (props: AccordionPanelProps) => {
  const {
    accordionHeader,
    children,
    open,
    onPress,
    iconType = 'arrow',
    disable,
    style,
  } = props;

  const articleColor = useColor('primary_20');
  const progress = useDerivedValue(() =>
    open ? withTiming(1) : withTiming(0),
  );

  return (
    <View
      style={[styles.accordionPanel, style, disable ? {opacity: 0.65} : null]}>
      <Pressable
        onPress={onPress}
        style={styles.accordionHeader}
        disabled={disable}>
        {accordionHeader}
        <View style={[styles.accordionHeaderIcon]}>
          {iconType === 'plus' ? (
            open ? (
              <JioIcon ic={'IcMinus'} color={IconColor.PRIMARY60} />
            ) : (
              <JioIcon ic={'IcAdd'} color={IconColor.PRIMARY60} />
            )
          ) : (
            null
            // <MicroTap color={articleColor} borderRadius={100}>
            //   <View
            //     style={{
            //       width: 24,
            //       height: 24,
            //       justifyContent: 'center',
            //       alignItems: 'center',
            //     }}>
            //     <Chevron progress={progress} />
            //   </View>
            // </MicroTap>
          )}
        </View>
      </Pressable>
      {open ? <View style={[styles.accordionBody]}>{children}</View> : null}
    </View>
  );
};

export default Accordion;
export {AccordionPanel};
