import React, {useCallback, useEffect, useState} from 'react';

const useAccordion = (children: React.ReactNode, allowMultiple: boolean) => {
  const [openAccordionPanel, setOpenAccordionPanel] = useState<{
    [key: number]: boolean;
  }>([]);

  const Children = React.Children.toArray(children);

  const handleAccordionEvent = useCallback(
    (index: number) => {
      if (allowMultiple) {
        setOpenAccordionPanel(prev => {
          const updatedState = [...prev];
          updatedState[index] = !prev[index];
          return updatedState;
        });
      } else {
        setOpenAccordionPanel(prev => {
          const updatedState = prev.map((item, i) =>
            i === index ? !item : false,
          );
          return updatedState;
        });
      }
    },
    [allowMultiple],
  );

  useEffect(() => {
    Children.map((child, index) => {
      if (child.props?.open) {
        setOpenAccordionPanel(prev => {
          const updatedState = [...prev];
          updatedState[index] = true;
          return updatedState;
        });
      } else {
        setOpenAccordionPanel(prev => {
          const updatedState = [...prev];
          updatedState[index] = false;
          return updatedState;
        });
      }
    });
  }, []);
  return [Children, openAccordionPanel, handleAccordionEvent];
};

export default useAccordion;
