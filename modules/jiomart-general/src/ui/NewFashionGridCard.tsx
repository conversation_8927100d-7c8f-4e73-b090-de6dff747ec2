import {JioIcon, JioText} from '@jio/rn_components';
import {
  JioTypography,
  type JioColor,
  type JioIconProps,
} from '@jio/rn_components/src/index.types';
import React from 'react';
import {Pressable, StyleProp, StyleSheet, View, ViewStyle} from 'react-native';
import FastImage from 'react-native-fast-image';
import WishlistToggleView from './Wishlist/WishlistToggleView';
import {formatPrice} from '../../../jiomart-common/src/utils/JMStringUtility';
import JMTag from './JMTag';
import {rw} from '../../../jiomart-common/src/JMResponsive';

interface NewFashionGridCardProps {
  imageUrl?: string;
  disableWishlist?: boolean;
  disableAddToCart?: boolean;
  shouldShowVeg?: boolean;
  vegIcon?: JioIconProps;
  shouldShowSmartBazzar?: boolean;
  smartBazzarImage?: string;
  tag?: string;
  tagColor?: string;
  tagBackgroundColor?: string;
  disableTag?: boolean;
  uid?: any;
  slug?: string;
  title?: string;
  titleColor?: string;
  titleMaxLine?: number;
  effectivePrice?: number;
  effectivePriceColor?: JioColor;
  markedPrice?: number;
  markedPriceColor?: JioColor;
  discount?: string;
  discountBackgroundColor?: string;
  discountColor?: JioColor;
  onMultiVariantPress?: () => void;
  shouldShowMultiVariant?: boolean;
  size?: string;
  totalMultiVariant?: string;
  totalMultiVariantColor?: JioColor;
  multiVariantColor?: JioColor;
  multiVariantIcon?: JioIconProps;
  addToCartTitle?: string;
  verticalCode?: string;
  shouldShowOutofStock?: boolean;
  outOfStockText?: string;
  outOfStockColor?: JioColor;
  offers?: any[];
  onPress: () => void;
  style?: StyleProp<ViewStyle>;
}

interface ImageContainerProps {
  imageUrl?: string;
  disableWishlist?: boolean;
  shouldShowVeg?: boolean;
  vegIcon?: JioIconProps;
  shouldShowSmartBazzar?: boolean;
  smartBazzarImage?: string;
  uid?: number;
  imageStyle?: any;
}

const ImageContainer = ({
  imageUrl,
  disableWishlist,
  shouldShowSmartBazzar,
  shouldShowVeg,
  vegIcon,
  smartBazzarImage,
  uid,
  imageStyle,
}: ImageContainerProps) => {
  return (
    <View>
      <View style={[styles.imageContainer, imageStyle]}>
        <FastImage
          source={{uri: imageUrl}}
          style={[styles.image, imageStyle]}
          resizeMode={FastImage.resizeMode.contain}
        />
        {!disableWishlist ? (
          <WishlistToggleView uid={uid} style={styles.wishlist} />
        ) : null}
        {shouldShowVeg ? <JioIcon {...vegIcon} style={styles.veg} /> : null}
        {shouldShowSmartBazzar && (
          <FastImage
            source={{uri: smartBazzarImage}}
            resizeMode={FastImage.resizeMode.contain}
            style={styles.smartBazaar}
          />
        )}
      </View>
    </View>
  );
};

interface PriceInfoProps {
  effectivePrice: number;
  effectivePriceColor: JioColor;
  markedPrice: number;
  markedPriceColor: JioColor;
  discount?: string;
  discountBackgroundColor?: string;
  discountColor?: JioColor;
  containerStyle?: any;
}

const PriceInfo = ({
  effectivePrice,
  effectivePriceColor,
  markedPrice,
  markedPriceColor,
  discount,
  discountBackgroundColor,
  discountColor,
  containerStyle,
}: PriceInfoProps) => {
  return (
    <View style={containerStyle}>
      <View style={styles.priceContainer}>
        <JioText
          text={`₹${formatPrice(effectivePrice)}`}
          appearance={JioTypography.BODY_S_BOLD}
          color={effectivePriceColor}
          maxLines={2}
          ellipsizeMode="tail"
        />
        {discount && (
          <JioText
            text={`₹${formatPrice(markedPrice)}`}
            appearance={JioTypography.BODY_XXS}
            color={markedPriceColor}
            style={styles.discount}
            maxLines={2}
            ellipsizeMode="tail"
          />
        )}
      </View>
      <View>
        {discount && (
          <JMTag
            color={discountBackgroundColor as string}
            textColor={discountColor}
            text={discount}
          />
        )}
      </View>
    </View>
  );
};

const NewFashionGridCard = (props: NewFashionGridCardProps) => {
  const {
    imageUrl,
    disableWishlist,
    shouldShowVeg,
    vegIcon,
    shouldShowSmartBazzar,
    smartBazzarImage,
    uid,
    tag,
    tagColor,
    tagBackgroundColor,
    disableTag,
    title,
    titleColor,
    titleMaxLine,
    effectivePrice,
    effectivePriceColor,
    markedPrice,
    markedPriceColor,
    discount,
    discountBackgroundColor,
    discountColor,
    shouldShowOutofStock,
    outOfStockText,
    outOfStockColor,
    onPress,
  } = props;
  return (
    <Pressable style={[styles.cardWrapper]} onPress={onPress}>
      <ImageContainer
        imageUrl={imageUrl}
        disableWishlist={disableWishlist}
        shouldShowVeg={shouldShowVeg}
        vegIcon={vegIcon}
        shouldShowSmartBazzar={shouldShowSmartBazzar}
        smartBazzarImage={smartBazzarImage}
        uid={uid}
        imageStyle={{width: rw(148), height: rw(148)}}
      />
      <View style={styles.cardDetail}>
        {!disableTag && tag ? (
          <JMTag
            color={tagBackgroundColor as string}
            text={tag}
            textColor={tagColor as JioColor}
            appearance={JioTypography.BODY_XXS}
          />
        ) : null}
        <JioText
          text={title ?? ''}
          appearance={JioTypography.BODY_XXS}
          color={titleColor as JioColor}
          maxLines={titleMaxLine}
        />

        <PriceInfo
          effectivePrice={effectivePrice as number}
          effectivePriceColor={effectivePriceColor as JioColor}
          markedPrice={markedPrice as number}
          markedPriceColor={markedPriceColor as JioColor}
          discount={discount}
          discountBackgroundColor={discountBackgroundColor}
          discountColor={discountColor}
          containerStyle={styles.price}
        />

        {shouldShowOutofStock && (
          <JioText
            text={outOfStockText ?? ''}
            appearance={JioTypography.BODY_XXS}
            color={outOfStockColor}
          />
        )}
      </View>
    </Pressable>
  );
};

export default React.memo(NewFashionGridCard);

const styles = StyleSheet.create({
  cardWrapper: {
    flex: 0.5,
    borderRadius: 16,
    borderWidth: 1,
    borderColor: 'transparent',
  },
  cardDetail: {
    marginTop: 4,
  },

  imageContainer: {
    width: rw(108),
    borderRadius: 16,
  },
  smartBazaar: {
    marginTop: 4,
    width: '100%',
    height: 20,
  },
  image: {
    width: rw(108),
    height: rw(108),
    borderRadius: 8,
    backgroundColor: '#F5F5F5',
  },
  wishlist: {
    position: 'absolute',
    right: 8,
    top: 8,
  },
  veg: {
    padding: 4,
    left: 0,
    bottom: 26,
    position: 'absolute',
  },
  priceContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 2,
    gap: 4,
  },
  discount: {textDecorationLine: 'line-through'},
  price: {
    flexDirection: 'column',
  },
});
