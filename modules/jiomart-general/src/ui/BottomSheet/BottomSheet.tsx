import React from 'react';
import {Platform, StatusBar, TouchableWithoutFeedback} from 'react-native';
import {PanGestureHandler, ScrollView} from 'react-native-gesture-handler';
import Animated from 'react-native-reanimated';
import type {BottomSheetProps} from './types/BottomSheetType';
import useBottomSheetViewController from './controller/useBottomSheetViewController';

const BottomSheet = ({children, ...rest}: BottomSheetProps) => {
  const {
    visible,
    contentHeight,
    isStatic,
    onBackDropClick,
    disabledBackDropClick,
    headerComponent,
    footerComponent,
    isStretchEnabled,
    enableScroll,
    backDropStyle,
    animatedStyle,
    dynamicHeightStyle,
    handleGesture,
    handleGestureEnd,
    calculateTop,
    styles,
    close,
    setContentHeight,
    statusBarRef,
  } = useBottomSheetViewController(rest);

  // Only render BottomSheet when visible
  if (!visible) {
    return null;
  }
  return (
    <>
      {Platform.OS === 'android' ? (
        <StatusBar
          ref={statusBarRef}
          barStyle="light-content"
          backgroundColor="rgba(0,0,0,0.95)"
        />
      ) : null}
      <TouchableWithoutFeedback
        onPress={() => {
          if (!disabledBackDropClick) {
            close(onBackDropClick);
          }
        }}>
        <Animated.View style={[styles.modalBackground, backDropStyle]} />
      </TouchableWithoutFeedback>
      <TouchableWithoutFeedback
        style={[
          styles.position,
          {
            top: calculateTop(contentHeight),
          },
        ]}>
        <PanGestureHandler
          onGestureEvent={handleGesture}
          onEnded={handleGestureEnd}>
          <Animated.View
            style={[
              styles.bottomSheetContainer,
              styles.position,
              animatedStyle,
              dynamicHeightStyle,
            ]}>
            {React.isValidElement(headerComponent)
              ? React.cloneElement(headerComponent)
              : null}
            {isStatic ? (
              React.isValidElement(children) ? (
                React.cloneElement(children, {...rest, close})
              ) : null
            ) : (
              <ScrollView
                bounces={false}
                scrollEventThrottle={20}
                nestedScrollEnabled
                scrollEnabled={enableScroll}
                showsHorizontalScrollIndicator={false}
                showsVerticalScrollIndicator={false}
                onContentSizeChange={(w, h) => {
                  if (isStretchEnabled) {
                    setContentHeight(h);
                  } else {
                    if (contentHeight < h) {
                      setContentHeight(h);
                    }
                  }
                }}>
                {React.isValidElement(children)
                  ? React.cloneElement(children, {...rest, close})
                  : null}
              </ScrollView>
            )}
            {React.isValidElement(footerComponent)
              ? React.cloneElement(footerComponent)
              : null}
          </Animated.View>
        </PanGestureHandler>
      </TouchableWithoutFeedback>
    </>
  );
};

export default BottomSheet;
