import type {ReactElement} from 'react';

export interface BottomSheetProps {
  visible: boolean;
  children: ReactElement<BottomSheetChildren>;
  headerComponent?: React.ReactNode;
  footerComponent?: React.ReactNode;
  isStatic?: boolean;
  staticHeight?: number;
  onBackDropClick?: () => void;
  onDrag?: () => void;
  disabledBackDropClick?: boolean;
  disabledGesture?: boolean;
  dragTime?: number;
  smoothTime?: number;
  maxHeightPercent?: number;
  isStretchEnabled?: boolean;
  enableKeyboarAvoidingView?: boolean;
  enableScroll?: boolean;
  dragThresholdPercent?: number;
  offset?: number;
  disableBlurGain?: boolean;
  minHeight?: number;
}

export interface BottomSheetChildren {
  close?: (callback?: () => void) => void;
}

export interface UseBottomSheetViewControllerProps
  extends Omit<BottomSheetProps, 'children'> {}
