import {
  Animated,
  TextInput,
  TouchableHighlight,
  TouchableOpacity,
  View,
} from 'react-native';
import React, {useState} from 'react';
import {JioAvatar, JioIcon, JioText, useColor} from '@jio/rn_components';
import useJioHeaderController from './controllers/useJioHeaderController';
import type {
  JioHeaderBadgeProps,
  JioHeaderIconList,
  JioHeaderIconListViewProps,
  JioHeaderNotifyProps,
  JioHeaderSearchBarProps,
  JMHeaderNavProps,
} from './types/JioHeader';
import {styles} from './styles/JioHeaderStyle';
import {
  IconColor,
  JioAvatarKind,
  JioAvatarSize,
  JioColor,
  JioTypography,
} from '@jio/rn_components/src/index.types';
import useJioHeaderSearchBar from './hooks/useJioHeaderSearchBar';
import {
  IconType,
  useJMHeaderController,
} from './controllers/useJMHeaderController';
import {<PERSON><PERSON>K<PERSON>} from '@jio/rn_components/src/utils/IconUtility';
import {useGlobalState} from '../../utils/JMGlobalStateProvider';
import {useCartCount} from '@jm/jiomart-cart/src/hooks/useCart';

const JioHeader = (props: JMHeaderNavProps) => {
  const {
    data,
    navTitle,
    onBackPress,
    subTitle,
    style,
    headerTopComponent,
    headerBottomComponent,
    searchTextHandler,
    onSubmitHandler,
  } = useJioHeaderController(props);
  const {backgroundColor, headerIconsList, searchData} = data;
  const leftIconList = headerIconsList?.leftIconList;
  const rightIconList = headerIconsList?.rightIconList;
  const headerBackgroundColor = useColor(
    (backgroundColor as JioColor) ?? 'primary_50',
  );
  const [searchValue, setSearchValue] = useState('');

  if (!Object.keys(data)?.length) {
    return null;
  }

  return (
    <>
      {React.isValidElement(headerTopComponent) &&
        React.cloneElement(headerTopComponent)}

      <View
        style={[
          styles.container,
          {backgroundColor: headerBackgroundColor},
          style?.headerStyle,
        ]}>
        {leftIconList && leftIconList?.length > 0 && (
          <JioHeaderIconListView
            iconList={leftIconList as JioHeaderIconList[]}
            onBackPress={onBackPress}
          />
        )}

        {navTitle && (
          <JioText
            text={navTitle}
            appearance={JioTypography.BODY_M_BOLD}
            maxLines={1}
            color="primary_inverse"
            style={styles.headerTitle}
          />
        )}

        {searchData?.enable && (
          <JioHeaderSearchBar
            {...searchData}
            onSubmitHandler={onSubmitHandler}
            textInput={{
              onChangeText: text => {
                setSearchValue(text);
                searchTextHandler?.(text);
              },
              value: searchValue,
            }}
          />
        )}

        {subTitle && (
          <JioText
            text={subTitle}
            appearance={JioTypography.BODY_XXS}
            color="primary_30"
            style={styles.marginLeftAuto}
          />
        )}

        {rightIconList && rightIconList?.length > 0 && (
          <JioHeaderIconListView
            iconList={rightIconList as JioHeaderIconList[]}
            style={[styles.marginLeftAuto]}
            onBackPress={onBackPress}
          />
        )}
      </View>

      {React.isValidElement(headerBottomComponent) &&
        React.cloneElement(headerBottomComponent)}
    </>
  );
};

const JioHeaderSearchBar = React.memo((props: JioHeaderSearchBarProps) => {
  const {
    style,
    backgroundColor,
    inputRef,
    textInput,
    isEditable,
    value,
    JioHeaderSearchBarIconListView,
    onSearchBarPress,
    onSearchText,
    onFocus,
    onBlur,
    closeSearchButton,
    onSubmitHandler,
    enableAnimation,
    animationTexts,
    fadeAnim,
    translateYAnim,
    currentIndex,
  } = useJioHeaderSearchBar(props);

  return (
    <View
      style={[
        styles.searchBarContainer,
        {
          backgroundColor: backgroundColor,
        },
        style,
      ]}>
      <JioHeaderSearchBarIconListView />
      <TouchableOpacity
        style={styles.flexOne}
        hitSlop={7}
        onPress={onSearchBarPress}
        disabled={isEditable}>
        <View
          style={[
            styles.flexOne,
            !isEditable && value !== '' ? styles.flexWrap : null,
          ]}
          pointerEvents={isEditable ? 'auto' : 'none'}>
          <TextInput
            ref={inputRef}
            style={[styles.searchBarTextInput, textInput?.style]}
            pointerEvents={isEditable ? 'auto' : 'none'}
            underlineColorAndroid={
              textInput?.underlineColorAndroid ?? 'transparent'
            }
            placeholderTextColor={textInput?.placeholderTextColor ?? 'white'}
            enablesReturnKeyAutomatically={
              textInput?.enablesReturnKeyAutomatically ?? true
            }
            selectionColor={textInput?.selectionColor ?? 'rgba(0, 120, 173, 1)'}
            onChangeText={onSearchText}
            onFocus={onFocus}
            onBlur={onBlur}
            onSubmitEditing={() => {
              onSubmitHandler?.(value);
            }}
            {...textInput}
            placeholder={
              enableAnimation ? '' : props.placeholder ?? 'Search in JioMart'
            }
            value={value}
            numberOfLines={1}
          />
          {enableAnimation && animationTexts && animationTexts?.length > 0 && (
            <View style={styles.animationTextWrapper}>
              <Animated.Text
                style={[
                  styles.animationText,
                  {
                    opacity: fadeAnim,
                    transform: [{translateY: translateYAnim}],
                  },
                ]}>
                {animationTexts?.[currentIndex]}
              </Animated.Text>
            </View>
          )}
        </View>
      </TouchableOpacity>

      {value && isEditable ? (
        <TouchableOpacity
          onPress={closeSearchButton}
          activeOpacity={0.65}
          hitSlop={6}>
          <JioIcon ic="IcCloseRemove" color={IconColor.INVERSE} />
        </TouchableOpacity>
      ) : null}
    </View>
  );
});

const JioHeaderIconListView = React.memo(
  (props: JioHeaderIconListViewProps) => {
    const {iconList = [], style, onBackPress} = props;
    const {handleIconPress} = useJMHeaderController();
    const {userInitials} = useGlobalState();
    const count = useCartCount();

    const IconList = iconList?.map((list, index) => {
      return (
        <TouchableHighlight
          key={`h_icon-${index}`}
          style={[
            list?.cta?.type !== 'avatar'
              ? styles.padding12
              : {borderRadius: 100},
          ]}
          underlayColor={'#0C5273A6'}
          onPress={() => {
            if ((list.cta?.type ?? '') === IconType.BACK) {
              onBackPress?.();
            } else {
              handleIconPress(list);
            }
          }}
          activeOpacity={1}>
          <>
            {list?.cta?.type === 'avatar' ? (
              <JioAvatar
                kind={
                  !userInitials ? JioAvatarKind.ICON : JioAvatarKind.INITIALS
                }
                name={userInitials}
                size={
                  ((list?.icon?.size ?? '') as JioAvatarSize) ??
                  JioAvatarSize.SMALL
                }
              />
            ) : (
              <JioIcon ic={list?.icon?.ic as IconKey} {...list?.icon} />
            )}
            {list?.cta?.type === 'cart' ? (
              <JioHeaderBadge count={count} />
            ) : null}
            {list?.icon?.isNotify ? (
              <JioHeaderNotify style={styles.notifyIcon} />
            ) : null}
          </>
        </TouchableHighlight>
      );
    });
    return <View style={[style, styles.iconListContainer]}>{IconList}</View>;
  },
);

const JioHeaderBadge = React.memo((props: JioHeaderBadgeProps) => {
  const {count, style} = props;

  const secondary50 = useColor('secondary_50');

  return count ? (
    <View
      style={[
        styles.badge,
        {
          backgroundColor: secondary50,
        },
        style,
      ]}>
      <JioText
        text={`${count}`}
        appearance={JioTypography.BODY_XXS}
        color={'primary_inverse'}
      />
    </View>
  ) : null;
});

const JioHeaderNotify = React.memo((props: JioHeaderNotifyProps) => {
  const {style} = props;
  const secondary50 = useColor('secondary_50');

  return (
    <View style={[styles.notify, {backgroundColor: secondary50}, style]} />
  );
});

export default React.memo(JioHeader);
