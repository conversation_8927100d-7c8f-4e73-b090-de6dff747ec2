import React from 'react';
import {JioIcon} from '@jio/rn_components';
import {
  IconColor,
  IconKind,
  IconSize,
} from '@jio/rn_components/src/index.types';
import {useCallback, useEffect, useRef, useState} from 'react';
import {
  Animated,
  Easing,
  Keyboard,
  Platform,
  type TextInput,
  TouchableOpacity,
} from 'react-native';
import type {
  Jio<PERSON>eaderIconList,
  UseJioHeaderSearchBarProps,
} from '../types/JioHeader';
import {useFocusEffect} from '@react-navigation/native';
import useDebounce, {debounce} from '../../../hooks/useDebounce';
import {useJMHeaderController} from '../controllers/useJMHeaderController';

const useJioHeaderSearchBar = (props: UseJioHeaderSearchBarProps) => {
  const {
    textInput,
    searchBarBackgroundColor,
    isEditable,
    debounceDelaySearchBarClick,
    enableAnimation,
    animationTexts,
  } = props;
  const {handleIconPress} = useJMHeaderController();
  const [value, setValue] = useState('');
  const [isFocused, setIsFocused] = useState(textInput?.autoFocus ?? false);
  const inputRef = useRef<TextInput | null>(null);
  const clearingInput = useRef(false);
  const backgroundColor = searchBarBackgroundColor ?? '#0C5273';
  const [currentIndex, setCurrentIndex] = useState(0);
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const translateYAnim = useRef(new Animated.Value(18)).current;
  const isMounted = useRef(false);

  useEffect(() => {
    isMounted.current = true;
    return () => {
      isMounted.current = false;
    };
  }, []);

  useEffect(() => {
    if (!enableAnimation || animationTexts?.length === 0) return;

    const animate = () => {
      fadeAnim.setValue(0);
      translateYAnim.setValue(18);

      Animated.parallel([
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 300,
          easing: Easing.in(Easing.ease),
          useNativeDriver: true,
        }),
        Animated.timing(translateYAnim, {
          toValue: 0,
          duration: 300,
          easing: Easing.inOut(Easing.ease),
          useNativeDriver: true,
        }),
      ]).start(() => {
        setTimeout(() => {
          if (!isMounted.current) return;
          Animated.parallel([
            Animated.timing(fadeAnim, {
              toValue: 0,
              duration: 300,
              easing: Easing.out(Easing.ease),
              useNativeDriver: true,
            }),
            Animated.timing(translateYAnim, {
              toValue: -18,
              duration: 300,
              easing: Easing.inOut(Easing.ease),
              useNativeDriver: true,
            }),
          ]).start(() => {
            if (!isMounted.current) return;
            setCurrentIndex(prev => (prev + 1) % (animationTexts?.length || 1));
            animate();
          });
        }, 2000);
      });
    };

    animate();

    return () => {
      isMounted.current = false;
    };
  }, [enableAnimation, animationTexts?.length]);

  const onSearchBarPress = useDebounce(() => {
    {
      props.searchBar && handleIconPress(props.searchBar);
    }
  }, debounceDelaySearchBarClick ?? 100);

  const shouldHideIcon = useCallback(
    (
      icon: {shouldHideIconOnFocus?: boolean; shouldHideIconOnText?: boolean},
      isInputFocused: boolean,
      inputValue: string,
    ) => {
      if (icon?.shouldHideIconOnFocus && isInputFocused) {
        return true;
      }
      if (icon?.shouldHideIconOnText && !!inputValue) {
        return true;
      }
      return false;
    },
    [],
  );

  const closeSearchButton = useCallback(() => {
    clearingInput.current = true;
    setValue('');
    inputRef.current?.clear();
    inputRef.current?.blur();
    textInput?.onChangeText?.('');
  }, [textInput]);

  const onSearchText = useCallback(
    (text: any) => {
      if (clearingInput.current) {
        clearingInput.current = false;
        return;
      }
      setValue(text);
      textInput?.onChangeText?.(text);
    },
    [textInput],
  );

  const onFocus = useCallback(
    (e: any) => {
      setIsFocused(true);
      textInput?.onFocus?.(e);
    },
    [textInput],
  );

  const onBlur = useCallback(
    (e: any) => {
      setIsFocused(false);
      textInput?.onBlur?.(e);
    },
    [textInput],
  );

  const JioHeaderSearchBarIconListView = useCallback(
    (props: JioHeaderIconList) => {
      return (
        <TouchableOpacity
          onPress={debounce(() => {
            console.log('Search Icon Pressed');
          })}
          activeOpacity={0.65}
          hitSlop={6}>
          <JioIcon
            ic={props?.icon?.ic ?? 'IcSearch'}
            kind={props?.icon?.kind ?? IconKind.DEFAULT}
            color={props?.icon?.color ?? IconColor.INVERSE}
            size={props?.icon?.size ?? IconSize.MEDIUM}
          />
        </TouchableOpacity>
      );
    },
    [isFocused, shouldHideIcon, value],
  );

  useEffect(() => {
    if (textInput?.value !== undefined) {
      setValue(textInput?.value as string);
    }
  }, [textInput?.value]);

  useFocusEffect(
    React.useCallback(() => {
      setTimeout(
        () => {
          isEditable && inputRef.current && inputRef?.current?.focus();
        },
        Platform.OS === 'ios' ? 1000 : 400,
      );
      return () => {
        Keyboard.dismiss();
      };
    }, []),
  );

  return {
    ...props,
    backgroundColor,
    inputRef,
    textInput,
    isFocused,
    value,
    JioHeaderSearchBarIconListView,
    onBlur,
    onFocus,
    onSearchText,
    closeSearchButton,
    onSearchBarPress,
    enableAnimation,
    animationTexts,
    fadeAnim,
    translateYAnim,
    currentIndex,
  };
};

export default useJioHeaderSearchBar;
