import {useNavigation} from '@react-navigation/native';
import {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {JioHeaderIconList} from '../types/JioHeader';
import {navigateTo} from '../../../navigation/JMNavGraph';
import {
  navBeanObj,
  NavigationType,
} from '../../../../../jiomart-common/src/JMNavGraphUtil';
import {getUserDetails} from '../../../../../jiomart-common/src/JMDataBaseManager';

export enum IconType {
  BACK = 'back',
  SEARCH = 'search',
  CART = 'cart',
  AVATAR = 'avatar',
  SCANNER = 'qrscanner',
  WISHLIST = 'wishlist',
  FILTER = 'filter',
}

export const useJMHeaderController = () => {
  const navigation = useNavigation<NativeStackNavigationProp<any>>();

  const handleIconPress = (list: JioHeaderIconList) => {
    navigateTo(
      navBeanObj({
        ...list.cta,
        navTitle: list.cta?.navTitle ?? '',
        actionType: list.cta?.actionType ?? '',
        destination: list.cta?.destination ?? '',
        navigationType:
          NavigationType[
            (
              list.cta?.navigationType ?? NavigationType.PUSH
            ).toUpperCase() as keyof typeof NavigationType
          ],
        actionUrl: list.cta?.actionUrl ?? '',
        headerType: list.cta?.headerType ?? 1,
        userJourneyRequiredState: list.cta?.userJourneyRequiredState ?? 0,
      }),
      navigation,
    );
  };

  return {
    handleIconPress,
  };
};
