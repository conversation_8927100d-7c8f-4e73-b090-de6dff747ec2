import {StyleSheet} from 'react-native';
export const styles = StyleSheet.create({
  container: {
    paddingLeft: 12,
    paddingRight: 24,
    paddingVertical: 8,
    flexDirection: 'row',
    alignItems: 'center',
    zIndex: 1000,
  },
  padding12: {
    padding: 12,
    borderRadius: 50,
  },

  marginLeftAuto: {
    marginLeft: 'auto',
  },
  iconListContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  badge: {
    position: 'absolute',
    top: 4,
    right: 4,
    width: 20,
    height: 20,
    alignItems: 'center',
    borderRadius: 100,
    justifyContent: 'center',
  },
  notify: {
    width: 10,
    height: 10,
    borderRadius: 100,
    position: 'absolute',
    top: 0,
    right: 0,
    alignItems: 'center',
    justifyContent: 'center',
  },
  avatarBadge: {
    right: -8,
    top: -8,
  },
  headerTitle: {flexShrink: 1, marginRight: 4},
  notifyIcon: {top: 10, right: 10},
  searchBarTextInput: {
    backgroundColor: 'transparent',
    color: 'white',
    fontSize: 16,
    fontWeight: '500',
    flex: 1,
  },
  flexOne: {flex: 1},
  flexWrap: {flexWrap: 'wrap', overflow: 'hidden'},
  searchBarContainer: {
    flex: 1,
    paddingHorizontal: 12,
    borderRadius: 24,
    justifyContent: 'center',
    flexDirection: 'row',
    height: 48,
    alignItems: 'center',
    columnGap: 12,
  },
  animationTextWrapper: {
    position: 'absolute',
    left: 0,
    right: 0,
    paddingVertical: 14,
  },
  animationText: {
    color: 'white',
    fontSize: 16,
    includeFontPadding: false,
    fontWeight: '500',
    lineHeight: 20,
  },
});
