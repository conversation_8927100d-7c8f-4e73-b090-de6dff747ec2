import {useState} from 'react';
import {AddCtaViewProps} from '../types/AddCtaModel';
import {useCartItems} from '@jm/jiomart-cart/src/hooks/useCart';
import useCartOperation from '@jm/jiomart-cart/src/hooks/useCartOperation';
import {Platform} from 'react-native';
import handleHapticFeedback from '../../../utils/JMHapticFeedback';
import {isNullOrUndefinedOrEmpty} from '@jm/jiomart-common/src/JMObjectUtility';

const useAddCta = (props: AddCtaViewProps) => {
  let {request} = props;
  const productCart = useCartItems({uid: request?.uid});
  const [loader, setLoader] = useState(false);
  const {
    addToCart,
    removeFromCart,
    generateAddToCartRequest,
    generateRemoveFromCartRequest,
  } = useCartOperation();

  const handleCartHapticClick = () =>
    Platform.OS === 'android'
      ? handleHapticFeedback('impactMedium')
      : handleHapticFeedback('impactLight');

  const handleAddToCart = () => {
    try {
      if (
        !isNullOrUndefinedOrEmpty(productCart) &&
        (productCart?.moq?.max ?? productCart?.moq?.min) ===
          productCart?.quantity
      ) {
        return;
      }

      setLoader(true);
      handleCartHapticClick();
      let quantity = productCart?.quantity ? productCart?.quantity + 1 : 1;
      const req = generateAddToCartRequest({
        ...request,
        quantity,
        parent_item_identifiers: productCart?.parent_item_identifiers,
        identifiers: productCart?.identifiers,
        article_id: productCart?.article_id,
      });

      addToCart.mutate(req);
    } catch (error) {
    } finally {
      setLoader(false);
    }
  };
  const handleRemoveFromCart = () => {
    try {
      setLoader(true);
      handleCartHapticClick();

      let quantity = productCart?.quantity ? productCart?.quantity - 1 : 0;
      const req = generateRemoveFromCartRequest({
        ...request,
        quantity,
        parent_item_identifiers: productCart?.parent_item_identifiers,
        identifiers: productCart?.identifiers,
        article_id: productCart?.article_id,
      });
      removeFromCart.mutate(req);
    } catch (error) {
    } finally {
      setLoader(false);
    }
  };

  return {
    ...props,
    productCart,
    loader,
    handleAddToCart,
    handleRemoveFromCart,
  };
};

export default useAddCta;
