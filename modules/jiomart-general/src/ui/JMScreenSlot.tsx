import React from 'react';
import {AuthState} from '../../../jiomart-common/src/JMAuthType';
import networkService from '../../../jiomart-common/src/JMNetworkConnectionUtility';
import {
  DeeplinkProps,
  ScreenSlotProps,
  genericToastTypeData,
  GenericToast,
} from '../../../jiomart-common/src/JMScreenSlot.types';
import {JMSharedViewModel} from '../../../jiomart-common/src/JMSharedViewModel';
import {JioToast} from '@jio/rn_components';
import {useFocusEffect} from '@react-navigation/native';
import {useState, useEffect, useCallback} from 'react';
import {View, Text, ActivityIndicator, BackHandler} from 'react-native';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import {
  getInitialNavBean,
  updatedNavBeanData,
} from '../deeplink/JMDeeplinkUtility';
import {useGlobalState} from '../utils/JMGlobalStateProvider';
import useNetworkController from '../../../jiomart-common/src/JMNetworkController';
import JioMartHeader from './Header/JioMartHeader';
import {CustomStatusBar} from '../rn-components/custom/CustomStatusBar';
import {getConfigFileData} from '../../../jiomart-networkmanager/src/JMConfigFileManager/JMFileManager';
import {JMConfigFileName} from '../../../jiomart-networkmanager/src/JMConfigFileManager/JMFileName';
import {NewJMHeaderProps} from './Header/types/JioHeader';
import DeliverToBar from './DeliverToBar/DeliverToBar';
import BottomSheet from './BottomSheet/BottomSheet';
import JMDeliverToBarBtmSheet from '@jm/jiomart-address/src/BottomSheet/JMDeliverToBarBtmSheet';
import JMDeliverToPincodeBtmSheet from '../../../jiomart-address/src/BottomSheet/JMDeliverToPincodeBtmSheet';
import {capitalizeFirstLetter} from '../../../jiomart-common/src/utils/JMStringUtility';
import useDeliverToBarBtmSheet from '../../../jiomart-address/src/hooks/useDeliverToBarBtmSheet';

export const userAuthenticationErrorState = () => {
  return (
    <View>
      <Text>Authentication failed</Text>
    </View>
  );
};
export const userAuthenticationLoadingState = () => {
  return (
    <View style={{flex: 1, justifyContent: 'center', alignItems: 'center'}}>
      <ActivityIndicator size="large" color="#0000ff" />
    </View>
  );
};

export function DeeplinkHandler(props: DeeplinkProps): JSX.Element {
  let [navigationBeanData, setNavigationBeanData] = useState(
    getInitialNavBean(props.navigationBean),
  );

  useEffect(() => {
    // will check user is loggedin or not and set the value accordingly
    updatedNavBeanData(
      JMSharedViewModel.Instance.deeplinkUrl,
      navigationBeanData,
    ).then(bean => {
      if (bean) {
        setNavigationBeanData(bean);
      }
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return <>{props?.children ? props.children(navigationBeanData) : null}</>;
}

function ScreenSlot(props: ScreenSlotProps) {
  const {userAuthenticationStatus, toastTypeData, address, setToastTypeData} =
    useGlobalState();
  const {
    showDeliverToBarBtmSheet,
    showDeliverToBarPincodeBtmSheet,
    openDeliverToBarBtmSheet,
    openDeliverToBarPincodeBtmSheet,
    closeDeliverToBarBtmSheet,
    closeDeliverToBarPincodeBtmSheet,
  } = useDeliverToBarBtmSheet();
  let deliverToBarText: string = '';
  if (address?.name && address?.name?.length > 10) {
    deliverToBarText = capitalizeFirstLetter(
      address?.name?.slice(0, 10) + '...',
    );
  } else {
    deliverToBarText = capitalizeFirstLetter(address?.name ?? '');
  }

  if (address?.name) {
    deliverToBarText +=
      ' ' +
      capitalizeFirstLetter(address?.city ?? '') +
      ', ' +
      (address?.pin ?? '');
  } else {
    deliverToBarText +=
      capitalizeFirstLetter(address?.city ?? '') + ', ' + (address?.pin ?? '');
  }

  const navigation = props.navigation;
  const insets = useSafeAreaInsets();
  const {isNetworkConnected} = useNetworkController();
  const [headerConfig, setHeaderConfig] = useState<NewJMHeaderProps>();

  const onBackPress = () => {
    if (
      JMSharedViewModel.Instance.previousPageURL !== '' &&
      props.onCustomBackPress
    ) {
      props.onCustomBackPress?.();
    } else if (navigation.canGoBack()) {
      if (props.onCustomBackPress) {
        props.onCustomBackPress?.();
      } else {
        navigation.goBack();
      }
    } else {
      networkService.stopMonitoring();
      BackHandler.exitApp();
    }
    props.onBackPressCallback?.();
    return true;
  };

  const searchTextHandler = (text: string) => {
    props.searchTextHandler?.(text);
    // store in redux if value !=
  };

  const onSubmitHandler = (text: string) => {
    props.onSubmitHandler?.(text);
  };

  useEffect(() => {
    if (isNetworkConnected === false) {
      setToastTypeData(genericToastTypeData(GenericToast.NO_INTERNET));
    } else if (toastTypeData?.genericToastType === GenericToast.NO_INTERNET) {
      setToastTypeData(undefined);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isNetworkConnected]);

  useEffect(() => {
    getConfigFileData(
      JMConfigFileName.JMHeaderConfigurationFileName,
      content => {
        const headerType = props.navigationBean?.headerType ?? '1';
        var headerConfig = content?.data?.[headerType];
        setHeaderConfig(headerConfig);
      },
    );
  }, [props.navigationBean?.headerType]);

  useFocusEffect(
    useCallback(() => {
      const backHandler = BackHandler.addEventListener(
        'hardwareBackPress',
        onBackPress,
      );
      return () => backHandler.remove();
    }, [props.onCustomBackPress, props.onCustomBackPress]),
  );

  useEffect(() => {
    const unsubscription = navigation.addListener('focus', () => {
      closeDeliverToBarBtmSheet();
      closeDeliverToBarPincodeBtmSheet();
    });
    return () => {
      closeDeliverToBarBtmSheet();
      closeDeliverToBarPincodeBtmSheet();
      unsubscription();
    };
  }, []);

  function getLocalUserAuthStatus(
    userAuthenticationStatus: AuthState,
  ): AuthState {
    if (
      (props.navigationBean?.userAuthenticationRequired === 1 &&
        userAuthenticationStatus === AuthState.SESSION_CREATED) ||
      props.navigationBean?.userAuthenticationRequired === 0
    ) {
      return AuthState.AUTHENTICATED;
    }
    return userAuthenticationStatus;
  }

  return (
    <View
      style={{
        flex: 1,
      }}>
      <CustomStatusBar
        color={props.navigationBean?.params?.customStatusBarColor}
      />
      {headerConfig && (
        <JioMartHeader
          data={headerConfig}
          navTitle={props?.navigationBean?.navTitle ?? ''}
          headerBottomComponent={
            props?.navigationBean?.shouldShowDeliverToBar ? (
              <DeliverToBar
                text={deliverToBarText}
                onPress={openDeliverToBarBtmSheet}
              />
            ) : null
          }
          onBackPress={onBackPress}
          searchTextHandler={searchTextHandler}
          onSubmitHandler={onSubmitHandler}
        />
      )}
      {props?.children
        ? props.children(
            getLocalUserAuthStatus(userAuthenticationStatus),
            openDeliverToBarBtmSheet,
          )
        : null}
      {toastTypeData !== undefined && (
        <JioToast
          message={toastTypeData?.message}
          isVisible={toastTypeData.isVisible}
          semanticState={toastTypeData?.semanticState}
          duration={toastTypeData?.duration}
          showClose={toastTypeData?.showClose}
          type={toastTypeData?.type}
          offset={insets.top}
          onDismiss={() => {
            setToastTypeData(undefined);
          }}
        />
      )}
      <BottomSheet
        visible={showDeliverToBarBtmSheet}
        enableKeyboarAvoidingView
        onBackDropClick={closeDeliverToBarBtmSheet}
        onDrag={closeDeliverToBarBtmSheet}>
        <JMDeliverToBarBtmSheet
          onClose={closeDeliverToBarBtmSheet}
          openDeliverToBarPincode={openDeliverToBarPincodeBtmSheet}
        />
      </BottomSheet>
      <BottomSheet
        visible={showDeliverToBarPincodeBtmSheet}
        enableKeyboarAvoidingView
        onBackDropClick={closeDeliverToBarPincodeBtmSheet}
        onDrag={closeDeliverToBarPincodeBtmSheet}>
        <JMDeliverToPincodeBtmSheet
          onClose={closeDeliverToBarPincodeBtmSheet}
        />
      </BottomSheet>

      {props?.bottomSheetContent}
    </View>
  );
}

export default ScreenSlot;
