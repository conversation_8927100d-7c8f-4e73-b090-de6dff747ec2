import {AppScreens} from '../../../jiomart-common/src/JMAppScreenEntry';
import {
  navGraph,
  NavigationBean,
  navBeanObj,
  ActionType,
} from '../../../jiomart-common/src/JMNavGraphUtil';
import {HeaderType} from '../../../jiomart-common/src/JMScreenSlot.types';
import {JMSharedViewModel} from '../../../jiomart-common/src/JMSharedViewModel';

function getKeyByValue(value: string): string {
  for (const key in AppScreens) {
    if (AppScreens[key as keyof typeof AppScreens] === value) {
      return key;
    }
  }
  return '';
}

export function getDestinationFromDeeplinkUrl(
  deeplinkUrl: string | null,
  defaultRoute: string,
  childNavGraph: boolean = true,
): string {
  let deeplink = JMSharedViewModel.Instance.deeplinkUrl;
  console.debug('getDestinationFromDeeplinkUrl' + deeplink);
  if (deeplink && deeplink.length > 0) {
    try {
      const url = deeplink;
      const parts = url.split('://');
      const hostname = parts[1].split('/')[0];
      const pathname = parts[1].substring(parts[1].indexOf('/') + 1);
      const destination = childNavGraph ? pathname : navGraph.get(pathname);
      console.debug('getDestinationFromDeeplinkUrl - ' + destination);
      return destination || defaultRoute;
    } catch (e) {
      console.error(e);
      return defaultRoute;
    }
  }
  return defaultRoute;
}

export function getInitialNavBean(
  navBean: NavigationBean | null,
  defaultBean: NavigationBean | null = null,
): NavigationBean {
  if (navBean && navBean !== null) {
    return navBean;
  } else if (defaultBean && defaultBean !== null) {
    return defaultBean;
  } else {
    return navBeanObj({
      actionType: ActionType.OPEN_DEEPLINK,
      destination: AppScreens.DASHBOARD,
      actionUrl: '',
      userAuthenticationRequired: 2,
      navTitle: '',
      headerVisibility: HeaderType.HIDDEN,
    });
  }
}

export async function updatedNavBeanData(
  deeplinkUrl: string | null,
  navBean: NavigationBean,
): Promise<NavigationBean | null> {
  if (deeplinkUrl) {
    JMSharedViewModel.Instance.setDeeplinkUrlData('');
    return getDeeplinkNavBeanData(deeplinkUrl);
  } else if (
    navBean?.actionType === ActionType.OPEN_DEEPLINK &&
    navBean.actionUrl
  ) {
    return getDeeplinkNavBeanData(navBean.actionUrl);
  }
  return null;
}

export async function getDeeplinkNavBeanData(
  deeplinkUrl: string,
): Promise<NavigationBean | null> {
  let bean = navBeanObj({
    actionType: ActionType.OPEN_NATIVE,
    destination: getDestinationFromDeeplinkUrl(
      deeplinkUrl,
      AppScreens.DASHBOARD,
    ),
    actionUrl: '',
    userAuthenticationRequired: 2,
    navTitle: '',
    headerVisibility: HeaderType.VISIBLE,
  });
  return bean;
}
