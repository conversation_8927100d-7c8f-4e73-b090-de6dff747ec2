import React, {FC, useEffect} from 'react';
import Animated, {
  useAnimatedStyle,
  useSharedValue,
  withDelay,
  withTiming,
} from 'react-native-reanimated';

interface ComponentLazyProps {
  children: React.ReactNode;
  delay?: number;
  duration?: number;
}

const ComponentLazy: FC<ComponentLazyProps> = props => {
  const {children, delay, duration} = props;
  const componentOpacity = useSharedValue(0);
  const animatedStyle = useAnimatedStyle(() => {
    return {
      opacity: componentOpacity.value,
    };
  }, []);

  useEffect(() => {
    componentOpacity.value = withDelay(
      delay ?? 50,
      withTiming(1, {
        duration: duration ?? 150,
      }),
    );
  }, []);
  return <Animated.View style={animatedStyle}>{children}</Animated.View>;
};

export default ComponentLazy;
