import {Gesture} from 'react-native-gesture-handler';
import {
  useAnimatedStyle,
  useSharedValue,
  withTiming,
} from 'react-native-reanimated';

const useMicroScale = (scale: number) => {
  const pressed = useSharedValue(false);

  const tap = Gesture.Tap()
    .onBegin(() => {
      pressed.value = true;
    })
    .onFinalize(() => {
      pressed.value = false;
    });

  const animatedStyles = useAnimatedStyle(() => {
    return {
      transform: [{scale: withTiming(pressed.value ? scale : 1)}],
    };
  });

  return {tap, animatedStyles};
};

export default useMicroScale;
