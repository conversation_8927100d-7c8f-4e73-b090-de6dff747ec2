import {NativeModules, NativeEventEmitter, Platform} from 'react-native';

const {JMRNNavigatorModule} = NativeModules;

const eventEmitter =
  Platform.OS == 'ios'
    ? new NativeEventEmitter(JMRNNavigatorModule)
    : new NativeEventEmitter();

export const subscribeToEvent = (
  eventName: string,
  callback: (eventData: any) => void,
) => {
  return eventEmitter.addListener(eventName, callback);
};

export const emitEvent = (eventName: string, eventData: any) => {
  JMRNNavigatorModule.emitEvent(eventName, eventData);
};
