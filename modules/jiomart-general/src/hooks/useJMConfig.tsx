import {useQueries, useQuery, useQueryClient} from '@tanstack/react-query';
import {JMConfigFileName} from '../../../jiomart-networkmanager/src/JMConfigFileManager/JMFileName';
import {
  callVersionFileAsync,
  getConfigFileDataAsync,
} from '../../../jiomart-networkmanager/src/JMConfigFileManager/JMFileManager';

// 1. App Startup Hook (fetches multiple config files)
export const useAppStartupConfig = (configFileKeys: JMConfigFileName[]) => {
  return useQueries({
    queries: configFileKeys.map(key => ({
      queryKey: [key],
      queryFn: () => getConfigFileDataAsync(key),
      staleTime: 0,
      gcTime: Infinity,
    })),
  });
};

// 2. Per-Screen Config Fetch (fetches a single config file)
export const useConfigFile = (key: JMConfigFileName, enabled = true) => {
  return useQuery({
    queryKey: [key],
    queryFn: () => getConfigFileDataAsync(key),
    enabled,
    staleTime: 0,
    gcTime: Infinity,
  });
};

// 3. Cache-Only Config Access (does not fetch, only reads from cache)
export const useConfigFileFromCache = (key: JMConfigFileName) => {
  const queryClient = useQueryClient();
  return queryClient.getQueryData([key]);
};

// version file calling
export const useVersionFile = ({enabled = true}) => {
  return useQuery({
    queryKey: [JMConfigFileName.JMVersionFileName],
    queryFn: () => callVersionFileAsync(JMConfigFileName.JMVersionFileName),
    enabled,
    staleTime: 0,
    gcTime: Infinity,
  });
};
