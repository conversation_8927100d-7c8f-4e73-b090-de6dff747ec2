import {useQuery, useQueryClient} from '@tanstack/react-query';
import {RQKey} from '../../../jiomart-common/src/JMConstants';
import {useEffect} from 'react';
import {getUserDetails} from '../../../jiomart-common/src/JMDataBaseManager';

const useUserProfile = () => {
  const queryClient = useQueryClient();

  const saveUserData = (userData: any) => {
    queryClient.setQueryData([RQKey.USER_PROFILE], userData);
  };

  const {data: userData} = useQuery({
    queryKey: [RQKey.USER_PROFILE],
    queryFn: () => undefined, // Prevents API call
    enabled: false, // Prevents automatic fetch
    staleTime: Infinity, // Keeps data always fresh
    initialData: () => null, // Optional default
  });

  useEffect(() => {
    getUserDetails()?.then(value => {
      if (value) {
        saveUserData(JSON.parse(value));
      }
    });
  }, []);

  return {saveUserData, userData};
};

export default useUserProfile;
