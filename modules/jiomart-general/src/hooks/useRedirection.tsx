import {useNavigation} from '@react-navigation/native';
import {useCallback} from 'react';
// import {checkAndPushControllerAsPerDictionaryValueNB} from '../NativeBridge/JMRNNavigatorBridge';
import {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {navigateTo} from '../navigation/JMNavGraph';
import {
  ActionType,
  navBeanObj,
  NavigationType,
} from '../../../jiomart-common/src/JMNavGraphUtil';
import {AppScreens} from '../../../jiomart-common/src/JMAppScreenEntry';
import {HeaderType} from '../../../jiomart-common/src/JMScreenSlot.types';
import { getBaseURL } from '../../../jiomart-networkmanager/src/JMEnvironmentConfig';

interface RedirectTo {
  openInRN?: boolean;
  rnActionScreen?: string;
  webUrl?: string;
  header?: any;
  deeplink?: any;
  params?: any;
}

const useRedirection = () => {
  const navigation = useNavigation<NativeStackNavigationProp<any>>();
  const redirectTo = useCallback((item: RedirectTo) => {
    // if (item?.openInRN) {
    //   navigation.push(item?.rnActionScrren, {
    //     webViewUrl: item?.webUrl,
    //     ...item?.header,
    //     ...item?.params,
    //   });
    //   return;
    // }
    console.log('Redirecting to webview');
    // checkAndPushControllerAsPerDictionaryValueNB(item?.deeplink);
  }, []);

  const redirectToPdp = useCallback(
    ({item, verticalCode, vertical}: any) => {
      const actionUrl = `${getBaseURL()}/product/${item?.params?.slug}`;
      openPDPScreen(actionUrl);
    },
    [navigation],
  );

  const openPDPScreen = (actionUrl: string) => {
    navigateTo(
      navBeanObj({
        actionType: ActionType.OPEN_WEB_URL,
        destination: AppScreens.COMMON_WEB_VIEW,
        headerVisibility: HeaderType.CUSTOM,
        navigationType: NavigationType.PUSH,
        loginRequired: false,
        actionUrl: actionUrl,
        headerType: 9,
      }),
      navigation,
    );
  };

  return {
    redirectTo,
    redirectToPdp,
  };
};

export default useRedirection;
