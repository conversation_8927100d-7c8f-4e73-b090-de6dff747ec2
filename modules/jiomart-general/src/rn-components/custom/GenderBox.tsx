import {JioIcon, JioText} from '@jio/rn_components';
import {IconColor, JioTypography} from '@jio/rn_components/src/index.types';
import {StyleSheet, View} from 'react-native';

enum EGenderType {
  MALE = 'Male',
  FEMALE = 'Female',
}

interface iGenderBox {
  gender: 'Male' | 'Female';
  appearance: JioTypography;
  isSelected: boolean;
}

const GenderBox = ({gender, appearance, isSelected}: iGenderBox) => {
  return (
    <View style={[styles.genderbox, isSelected && styles.genderboxSelected]}>
      <JioIcon
        ic={gender == EGenderType.MALE ? 'IcProfileMale' : 'IcProfileFemale'}
        color={isSelected ? IconColor.GREY100 : IconColor.GREY80}
      />
      <JioText text={gender} appearance={appearance} />
    </View>
  );
};

export default GenderBox;

const styles = StyleSheet.create({
  genderbox: {
    width: 80,
    height: 80,
    borderColor: '#E0E0E0',
    borderWidth: 2,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
  },
  genderboxSelected: {
    borderColor: '#1E7B74',
    backgroundColor: '#E8FAF7',
  },
});
