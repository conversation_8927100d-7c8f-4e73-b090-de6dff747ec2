
import { View, useColorScheme } from "react-native";
import DateTimePickerModal from "react-native-modal-datetime-picker";
import { format } from 'date-fns';
import { dateToStringFormat } from "@jhh/jio-health-common/src/Helper";

export type CustomDataPicker = {
    showDatePicker?: boolean
    onClose: () => void;
    getDate: (option: string, date: Date) => void;
    maxDate?: Date | undefined;
    minDate?: Date | undefined;
    selectedDate?: Date | undefined;
    disableWeekends?: boolean | undefined
}


export function CustomDataPicker({ showDatePicker, onClose, getDate, maxDate, minDate, selectedDate }: CustomDataPicker): JSX.Element {

    const hideDatePicker = () => {
        onClose()
    };
    const colorScheme = useColorScheme();
    const datePickerColor = colorScheme === 'dark' ? 'red' : 'red';

    const handleConfirm = (date: any) => {
        const formatedDate: string = format(new Date(date), 'dd/MM/yyyy');
        getDate(formatedDate, date)
        hideDatePicker();
    };

    function parseDate(dateString: string | Date): Date {
        if (typeof dateString === 'string') {
            if (dateString.match(/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}.\d{3}Z$/)) {
                return new Date(dateString);
            } else {
                return new Date(dateString);
            }
        } else {
            return dateString;
        }
    }


    return (
        <View style={{
            flex: 1,
            justifyContent: 'center',
            alignItems: 'center',
        }} >
            <View style={{
                flex: 1,
                justifyContent: 'center',
                alignItems: 'center',
                backgroundColor: 'rgba(0, 0, 0, 0.5)',
            }}>
                <DateTimePickerModal
                    textColor={datePickerColor}
                    isVisible={showDatePicker}
                    display="inline"
                    mode="date"
                    date={selectedDate}
                    onConfirm={handleConfirm}
                    onCancel={hideDatePicker}
                    buttonTextColorIOS='#DF9F39'
                    style={{ backgroundColor: "#DF9F39" }}
                    accentColor="#DF9F39"
                    minimumDate={minDate}
                    maximumDate={maxDate ? parseDate(maxDate ? maxDate : '') : new Date()}
                />
            </View>

        </View>
    );
}

export const openCalenderPickerNative = async (customDatePicker: CustomDataPicker) => {
    try{
        const updatedCustomerDatePicker = {
            maxDate: customDatePicker.maxDate ? dateToStringFormat(customDatePicker.maxDate) : undefined,
            minDate: customDatePicker.minDate ? dateToStringFormat(customDatePicker.minDate) : undefined,
            disableWeekends: customDatePicker.disableWeekends,
            selectedDate : customDatePicker.selectedDate ? dateToStringFormat(customDatePicker.selectedDate) : undefined
        }
        // const nativeToDate = await openCalenderPicker(updatedCustomerDatePicker);
        // console.log(nativeToDate.selectedYear + "-" + nativeToDate.selectedMonth + "-" + nativeToDate.selectedDay);
        // if (nativeToDate && nativeToDate.selectedYear !== undefined) {
        //     customDatePicker.getDate(
        //         nativeToDate.selectedDay.toString().padStart(2, '0') + "/" + nativeToDate.selectedMonth.toString().padStart(2, '0') + "/" + nativeToDate.selectedYear,
        //         new Date(nativeToDate.selectedYear, nativeToDate.selectedMonth - 1, nativeToDate.selectedDay)
        //     )
        // }
    
    } catch(error) {
        console.log("Calender closed", error);
    }
};