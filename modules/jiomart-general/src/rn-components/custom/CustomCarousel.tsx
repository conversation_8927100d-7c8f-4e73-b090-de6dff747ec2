import { useState, useRef, useEffect } from "react";
import { Dimensions, GestureResponderEvent, Platform, ScrollView, StyleSheet, View } from "react-native"

type TOnSwipe = {
  left?: () => void;
  right?: () => void;
  up?: () => void;
  down?: () => void;
}

const useSwipeHandler = () => {
  const startX = useRef(0);
  const startY = useRef(0);

  const onTouchStart = (e: GestureResponderEvent) => {
      startX.current = e.nativeEvent.pageX
      startY.current = e.nativeEvent.pageY
  }

  const onTouchEnd = (e: GestureResponderEvent, onSwipe?: TOnSwipe) => {
      const distX = e.nativeEvent.pageX - startX.current
      const distY = e.nativeEvent.pageY - startY.current
      if (Math.abs(distX) > Math.abs(distY)) { // moved horizontally
          if (distX > 0) { // right
              if (onSwipe?.right) onSwipe.right()
          } else { // left
              if (onSwipe?.left) onSwipe.left()
          }
      } else { // moved vertically
        if (distY > 0) { // down
          if (onSwipe?.down) onSwipe.down()
        } else { // up
          if (onSwipe?.up) onSwipe.up()

        }
      }
  }

  return {onTouchStart, onTouchEnd}
}

type TCustomCarouselProps<T,> = {
  items: T[],
  RenderItem: (props: T) => JSX.Element,
  onIndexChange?: (index: number) => void,
  customStyles?: {
    indicatorList?: {[key: string]: any}
    itemList?: {[key: string]: any}
  }
}

const CustomCarousel = <T,>({items, RenderItem, onIndexChange, customStyles}: TCustomCarouselProps<T>) => {
  const swipeHandlers = useSwipeHandler();
  const [currentIndex, setCurrentIndex] = useState(0);

  useEffect(() => {
    if(onIndexChange) {
      onIndexChange(currentIndex)
    }
  }, [currentIndex])

  const _onSwipeLeft = () => {
    if (currentIndex < items.length - 1)
      setCurrentIndex(currentIndex + 1);
  }

  const _onSwipeRight = () => {
    if (currentIndex > 0)
      setCurrentIndex(currentIndex - 1);
  }

  return <View>
    <ScrollView
      style={[styles.scrollView, customStyles?.itemList ? customStyles.itemList : null]}
      horizontal={true}
      pagingEnabled
      onTouchStart={swipeHandlers.onTouchStart}
      onTouchCancel={Platform.OS == "android" ? (e) => {
        swipeHandlers.onTouchEnd(e, {
          left: _onSwipeLeft,
          right: _onSwipeRight,
        });
      } : undefined}
      onTouchEnd={Platform.OS == "ios" ? (e) => {
          swipeHandlers.onTouchEnd(e, {
            left: _onSwipeLeft,
            right: _onSwipeRight,
          });
        } : undefined}
    >
      {
        items.map((item, index) => (
          <RenderItem {...item} key={index} />
        ))
      }
    </ScrollView>
    <View
      style={[styles.carouselIndicatorsView, customStyles?.indicatorList ? customStyles?.indicatorList : null]}
    >
      {
        items.length > 1 && items.map((item, index) => (
          <View
            style={[styles.carouselIndicatorItem, index == currentIndex ? styles.carouselIndicatorItemSelected : null]}
            key={index}
          />
        ))
      }
    </View>
  </View>
}
const styles = StyleSheet.create({
  customCarouselView: {
    position: "relative",
  },
  scrollView: {
    height: "100%",
    width: "100%",
  },
  carouselIndicatorsView: {
    display: "flex",
    flexDirection: "row",
    justifyContent: "center",
    columnGap: 8,
    position: "absolute",
    bottom: Platform.OS == "android" ? 124 : 168,
    width: "100%",
  },
  carouselIndicatorItem: {
    height: 8,
    width: 8,
    borderRadius: 100,
    backgroundColor: "#E8FAF7",
    opacity: 0.3
  },
  carouselIndicatorItemSelected: {
    width: 24,
    backgroundColor: "#1E7B74",
    opacity: 1
  }
})

export default CustomCarousel;