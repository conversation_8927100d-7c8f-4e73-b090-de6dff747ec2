import {
  ErrorState,
  OTP_PIN_LENGTH,
} from '@jhh/jio-health/src/features/login/controller/JHOutsideLoginOtpController';
import React, {useRef} from 'react';
import {
  View,
  TextInput,
  StyleSheet,
  FlatList,
  NativeSyntheticEvent,
  TextInputKeyPressEventData,
  Platform,
  Keyboard,
} from 'react-native';

type OTPInputProps = {
  length?: number;
  otp: string[];
  focusedInput: number | null;
  onOTPChange: (otp: string[]) => void;
  onFocusInput: (index: number | null) => void;
  errorState: ErrorState | null;
  setErrorState: (a: ErrorState | null) => void;
};

const OTPInput: React.FC<OTPInputProps> = ({
  length = 6,
  otp,
  focusedInput,
  onOTPChange,
  onFocusInput,
  errorState,
  setErrorState,
}) => {
  const inputsRef = useRef<TextInput[]>([]);

  const handleKeyPress = (
    e: NativeSyntheticEvent<TextInputKeyPressEventData>,
    index: number,
  ) => {
    console.log({e});
    if (e.nativeEvent.key === 'Backspace') {
      setErrorState(null);
      const newOtp = [...otp];
      // If the current input field is not empty, clear it
      if (otp[index]) {
        newOtp[index] = '';
        onOTPChange && onOTPChange(newOtp);
      }
      if (index > 0) {
        // If current field is already empty, move to the previous field
        inputsRef.current[index - 1]?.focus();
      }
    }
    if (/^[0-9]*$/.test(e.nativeEvent.key)) {
      console.log({key: e.nativeEvent.key});
      const newOtp = [...otp];
      newOtp[index] = e.nativeEvent.key;

      // Notify parent about OTP change
      onOTPChange && onOTPChange(newOtp);

      // Automatically move to the next input if text is entered
      if (index < length - 1) {
        inputsRef.current[index + 1]?.focus();
      }
      if (index === length - 1) {
        Keyboard.dismiss();
      }
    }
  };

  const handleFocus = (index: number) => {
    onFocusInput(index);
    setErrorState(null);
  };

  const renderInput = ({index}: {index: number}) => (
    <TextInput
      key={index}
      value={otp[index]}
      onKeyPress={e => handleKeyPress(e, index)}
      onChangeText={text => {
        if (/^[0-9]*$/.test(text) && text.length === OTP_PIN_LENGTH) {
          onOTPChange(text.split(''));
        }
      }}
      keyboardType="number-pad"
      style={[
        styles.input,
        {
          borderBottomColor: errorState
            ? '#F50031'
            : focusedInput === index
            ? '#0A2885'
            : '#141414',
        },
      ]}
      onFocus={() => handleFocus(index)}
      ref={el => (inputsRef.current[index] = el!)}
      autoFocus={index === 0}
    />
  );

  return (
    <View style={styles.otpPinContainer}>
      <FlatList
        data={Array.from({length})}
        renderItem={renderInput}
        keyExtractor={(_, idx) => idx.toString()}
        horizontal
        scrollEnabled={false} // Disable scrolling for equal distribution
        contentContainerStyle={{justifyContent: 'space-around', flexGrow: 1}}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  otpPinContainer: {
    paddingBottom: 4,
    marginBottom: 6,
  },
  input: {
    flex: 1,
    borderBottomWidth: 2,
    height: 40,
    width: 47,
    textAlign: 'center',
    margin: 0,
    padding: 0,
    lineHeight: Platform.OS === 'ios' ? 0 : undefined,
    borderBottomColor: '#000000A6',
    color: '#141414',
    fontSize: 24,
    fontWeight: '500',
  },
});

export default OTPInput;
