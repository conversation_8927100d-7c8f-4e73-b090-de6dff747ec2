import React from 'react';
import {JioText, useColor} from '@jio/rn_components';
import {JioTypography} from '@jio/rn_components/src/index.types';
import {TouchableWithoutFeedback} from 'react-native-gesture-handler';
import type {TextStyle} from 'react-native';
import {TextChipProps} from '../TextChipType';

const TextChip = ({
  onPress,
  text,
  containerStyle,
  textStyle,
}: TextChipProps) => {
  const chipBorderColor = useColor('primary_grey_40');
  return (
    <TouchableWithoutFeedback
      style={[
        {
          borderWidth: 1,
          paddingHorizontal: 12,
          paddingVertical: 4,
          borderColor: chipBorderColor,
        },
        containerStyle,
      ]}
      onPress={onPress}>
      <JioText
        text={text}
        appearance={JioTypography.BODY_XS}
        color={'secondary_grey_100'}
        style={[textStyle as TextStyle]}
        maxLines={1}
      />
    </TouchableWithoutFeedback>
  );
};

export default TextChip;
