import React from 'react';
import {Image, StyleSheet, View} from 'react-native';

interface ISelfCheckHeader {
  navTitle: string;
}

const SelfCheckHeader = ({navTitle = ''}: ISelfCheckHeader) => {
  return (
    <>
      <View style={styles.headerContainer}>
        <View>
          <Image
            source={require('../../assets/back.jpg')}
            style={[styles.icon, styles.backbutton]}
          />
        </View>

        <View style={styles.navTitle}>{navTitle}</View>

        <View>
          <Image
            source={require('../../assets/languagechange.jpg')}
            style={[styles.icon, styles.language]}
          />
        </View>
      </View>
    </>
  );
};
const styles = StyleSheet.create({
  icon: {
    width: 48,
    height: 48,
  },
  headerContainer: {
    flexDirection: 'row',
    backgroundColor: '#FFFFFF',
  },
  navTitle: {
    flex: 1,
  },
  language: {
    marginRight: 20,
  },
  backbutton: {
    marginLeft: 12,
  },
});

export default SelfCheckHeader;