import {
    Pressable,
    StyleSheet,
    TextInput,
    View,
    Platform,
  } from "react-native";
  import React, { useState } from "react";
import Feedback, { FeedbackState } from "@jio/rn_components/src/utils/Feedback";
import { IconColor, IconSize, JioInputProps, JioSpinnerSize, JioTypography, SuffixType } from "@jio/rn_components/src/index.types";
import { JioIcon, JioSpinner, JioText, useTheme } from "@jio/rn_components";
  interface MobileNumberPrefixInterface extends JioInputProps{
    prefixComponent: React.ReactNode;
  }
import { useTypography } from '@jio/rn_components/src/typography';
  
  export function MobileNumberPrefixComponent({
    value = "",
    state = FeedbackState.CLEAR,
    readOnly = false,
    disabled = false,
    label = "",
    placeholder = "",
    maxLength,
    maxLines,
    prefixIcon,
    prefixComponent,
    suffixIcon,
    isLoading = false,
    stateText = "",
    style,
    helperText = "",
    keyboardType = "default",
    textContentType,
    returnKeyType = "default",
    autoFocus = false,
    feedbackSize,
    autoCapitalize,
    suffixText,
    suffixType = SuffixType.DEFAULT,
    onChange,
    onPrefixClick,
    onSuffixClick,
    onFocus,
    onBlur,
    onClick,
    onSubmitEditing,
    onEndEditing,
    onPressIn,
    onRef,
    
  }: MobileNumberPrefixInterface) {
    const theme = useTheme();
    const [isFocused, setFocused] = useState(false);
  
    function getDividerColor() {
      switch (state) {
        case FeedbackState.ERROR:
          return theme.feedback_error_50;
        case FeedbackState.WARNING:
          return theme.feedback_warning_50;
        default:
          return theme.feedback_success_50;
      }
    }
  
    const handleFocus = () => {
      setFocused(true);
      onFocus?.();
    };
  
    const handleBlur = () => {
      if (value === "") {
        setFocused(false);
        onBlur?.();
      }
    };
  
    const renderSuffixView = () => {
      if (isLoading) {
        return <JioSpinner style={{ marginRight: 8 }} size={JioSpinnerSize.SMALL} />;
      }
  
      if (suffixType === SuffixType.NONE) {
        return null;
      }
  
      if (suffixType === SuffixType.AUTO) {
        if (suffixIcon) {
          return (
            <Pressable onPress={onSuffixClick} style={styles.iconStyle}>
              <JioIcon
                ic={suffixIcon}
                size={IconSize.MEDIUM}
                color={IconColor.GREY80}
              />
            </Pressable>
          );
        }
        return null;
      }
  
      if (value !== "") {
        return (
          <Pressable onPress={onSuffixClick} style={styles.iconStyle}>
            <JioIcon
              ic={suffixIcon || 'IcCloseRemove'} 
              size={IconSize.MEDIUM}
              color={IconColor.GREY80}
            />
          </Pressable>
        );
      }
  
      return null;
    };
  
    return (
      <View style={[style, { opacity: disabled ? 0.4 : 1 }]}>
        <JioText
          style={{opacity:isFocused === true || value !=="" ? 1 : 0}}
          text={label}
          appearance={JioTypography.BODY_XS}
          color={"primary_grey_80"}
        />
        <Pressable style={styles.inputRow} onPress={onClick}>
          {prefixComponent ? 
            prefixComponent
          :
          prefixIcon && (
            <Pressable onPress={onPrefixClick} style={styles.iconStyle}>
              <JioIcon
                ic={prefixIcon}
                size={IconSize.MEDIUM}
                color={IconColor.GREY80}
              />
            </Pressable>
          )
          }
          <View pointerEvents={readOnly ? "none" : "auto"} style={{ flex: 1 }}>
            <TextInput
              ref={(input) => onRef?.(input)}
              style={[
                useTypography(JioTypography.BODY_S),
                {
                  backgroundColor: theme.transparent,
                  color: theme.primary_grey_100,
                },
                Platform.OS === "ios" && { lineHeight: 0 },
                Platform.OS === 'android' && {padding: 0},
              ]}
              placeholder={isFocused ? placeholder : label}
              maxLength={maxLength}
              numberOfLines={maxLines}
              placeholderTextColor={
                isFocused ? theme.primary_grey_60 : theme.primary_grey_80
              }
              selectionColor={ Platform.OS === 'ios' ? theme.primary_grey_100 : undefined} // for ios
              cursorColor={theme.primary_grey_100} // for android
              underlineColorAndroid="transparent"
              keyboardType={keyboardType}
              textContentType={textContentType}
              returnKeyType={returnKeyType}
              autoFocus={autoFocus}
              value={value}
              onChangeText={onChange}
              onFocus={handleFocus}
              onBlur={handleBlur}
              onSubmitEditing={onSubmitEditing}
              onEndEditing={onEndEditing}
              onPressIn={onPressIn}
              autoCapitalize={autoCapitalize}
            />
          </View>
          <JioText
            appearance={JioTypography.BODY_XXS}
            text={suffixText}
            color={"primary_grey_80"}
            style={{ marginHorizontal: 4}}
          />
          {renderSuffixView()}
        </Pressable>
        <View
          style={[
            { height: 2,marginTop:8, width: "100%", backgroundColor: theme.primary_grey_80 },
            state !== FeedbackState.CLEAR && {
              backgroundColor: getDividerColor(),
            },
          ]}
        />
        <Feedback state={state} stateText={stateText} feedbackSize={feedbackSize}></Feedback>
  
        <JioText
          appearance={JioTypography.BODY_XS}
          text={helperText}
          color={"primary_grey_80"}
        ></JioText>
      </View>
    );
  }
  
  const styles = StyleSheet.create({
    inputRow: {
      justifyContent: "space-between",
      flexDirection: "row",
      alignItems:'center',
      gap: 8,
    },
    iconStyle: {
      alignSelf: "center",
    },
  });
  
