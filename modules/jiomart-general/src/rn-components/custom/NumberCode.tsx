import React, { useState, useRef, useEffect } from 'react';
import { View, TextInput, StyleSheet, Text, FlatList, Button } from 'react-native';

interface StateConfig {
  errorText?: string;
}

interface PinCodeInputProps {
  stateConfig: StateConfig;
  obfuscate?: boolean; // For sensitive text like MPIN
  onPinComplete?: (pin: string) => void; // Pass the complete PIN back to parent
  error?: boolean; // Control error state from the parent component
}

const PinCodeInput: React.FC<PinCodeInputProps> = ({
  stateConfig,
  obfuscate = false,
  onPinComplete,
  error = false,
}) => {
  const [pin, setPin] = useState(['', '', '', '']); // Pin array
  const [focusedIndex, setFocusedIndex] = useState<number | null>(null); // Track the focused input
  const inputs = useRef<(TextInput | null)[]>([]); // Input refs for each digit

  // Handle changes in each input
  const handleChange = (text: string, index: number) => {
    const newPin = [...pin];
    newPin[index] = text;
    setPin(newPin);

    // Automatically jump to the next input if typing
    if (text && index < 3) {
      inputs.current[index + 1]?.focus();
    }

    // Pass the complete PIN to the parent when fully entered
    if (newPin.every((p) => p !== '')) {
      onPinComplete?.(newPin.join(''));
    }
  };

  // Handle Backspace press
  const handleBackspace = (text: string, index: number) => {
    const newPin = [...pin];

    // If current input has a value, delete the value and stay on the same input
    if (text) {
      newPin[index] = '';
      setPin(newPin);
    } 
    // If current input is empty, go to the previous input and clear that
    else if (!text && index > 0) {
      newPin[index - 1] = '';
      setPin(newPin);
      inputs.current[index - 1]?.focus(); // Move to previous input
    }
  };

  return (
    <View style={styles.container}>
      <FlatList
        data={pin}
        horizontal
        keyExtractor={(item, index) => index.toString()}
        renderItem={({ item, index }) => (
          <TextInput
            ref={(ref) => (inputs.current[index] = ref)}
            style={[
              styles.pinInput,
              focusedIndex === index && styles.focusedPinInput, // Highlight the currently focused input
              error && styles.errorPinInput, // Highlight in case of error
            ]}
            value={pin[index]}
            onFocus={() => setFocusedIndex(index)} // Track the focused input
            onChangeText={(text) => handleChange(text, index)}
            onKeyPress={({ nativeEvent }) => {
              if (nativeEvent.key === 'Backspace') {
                handleBackspace(pin[index], index);
              }
            }}
            keyboardType="number-pad"
            maxLength={1}
            secureTextEntry={obfuscate} // Obfuscation for sensitive input like MPIN
            placeholder=""
          />
        )}
        contentContainerStyle={styles.pinContainer} // Align all input fields horizontally
      />

      {/* Error Message Space */}
      <Text style={[styles.errorText, !error && styles.hidden]}>
        {stateConfig.errorText || 'Incorrect PIN. Please try again'}
      </Text>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginTop: 45,
  },
  pinContainer: {
    justifyContent: 'space-between',
    left: 23,
  },
  pinInput: {
    borderBottomWidth: 2,
    borderBottomColor: '#000',
    marginHorizontal: 6,
    width: 71,
    textAlign: 'center',
    fontSize: 24,
    paddingTop: 5,
  },
  focusedPinInput: {
    borderBottomColor: '#000093', // Highlight color for focused input
  },
  errorPinInput: {
    borderBottomColor: 'red', // Highlight color for error state
  },
  errorText: {
    left: 50,
    color: '#660014',
    marginTop: 10,
    height: 20, // Reserve space for the error text
  },
  hidden: {
    opacity: 0, // Hide the error message when there's no error
  },
});

export default PinCodeInput;

















