import React, { useState, useRef } from 'react';
import { View, TextInput, StyleSheet, Text, FlatList } from 'react-native';

interface StateConfig {
  errorText?: string;
}

interface PinCodeInputProps {
  stateConfig: StateConfig;
  obfuscate?: boolean;  // For sensitive text like MPIN
  pin: string[];
  setPin: React.Dispatch<React.SetStateAction<string[]>>;
}

const PinCodeWithoutState: React.FC<PinCodeInputProps> = ({ 
  pin,
  setPin,
  stateConfig,
   obfuscate = false }) => {
  const [focusedIndex, setFocusedIndex] = useState<number | null>(null);  // Track the focused input
  const [error, setError] = useState<boolean>(false);  // Error state
  const inputs = useRef<(TextInput | null)[]>([]);     // Input refs for each digit

  // Handle changes in each input
  const handleChange = (text: string, index: number) => {
    const newPin = [...pin];
    newPin[index] = text;
    setPin(newPin);

    // Automatically jump to the next input if typing, but stop at the last input
    if (text && index < 3) {
      inputs.current[index + 1]?.focus();
    }

    // Reset error state on change
    setError(false);

    // Do NOT auto-submit even if all fields are filled.
  };

  // Handle Backspace press
  const handleBackspace = (text: string, index: number) => {
    const newPin = [...pin];

    // If current input has a value, delete the value and stay on the same input
    if (text) {
      newPin[index] = '';
      setPin(newPin);
    }
    // If current input is empty, go to the previous input and clear that
    else if (!text && index > 0) {
      newPin[index - 1] = '';
      setPin(newPin);
      inputs.current[index - 1]?.focus();  // Move to previous input
    }
  };

  // Placeholder for each input
  const placeholder = '';

  return (
    <View style={styles.container}>
      <FlatList
        data={pin}
        horizontal
        scrollEnabled = {false}
        keyExtractor={(item, index) => index.toString()}
        renderItem={({ item, index }) => (
          <TextInput
            ref={(ref) => inputs.current[index] = ref}
            style={[
              styles.pinInput,
              focusedIndex === index && styles.focusedPinInput,  // Highlight the currently focused input
              error && styles.errorPinInput,                    // Highlight in case of error
            ]}
            value={pin[index]}
            onFocus={() => setFocusedIndex(index)}  // Track the focused input
            //onBlur={() => setFocusedIndex(null)}
            onChangeText={(text) => handleChange(text, index)}
            onKeyPress={({ nativeEvent }) => {
              if (nativeEvent.key === 'Backspace') {
                handleBackspace(pin[index], index);
              }
            }}
            keyboardType="number-pad"
            maxLength={1}
            secureTextEntry={obfuscate}  // Obfuscation for sensitive input like MPIN
            placeholder={placeholder}
            textContentType="oneTimeCode"  // Autofill for OTP
          />
        )}
        contentContainerStyle={styles.pinContainer}  // Align all input fields horizontally
      />

      {/* Display Error Message */}
      {error && <Text style={styles.errorText}>{stateConfig.errorText || 'Incorrect PIN. Please try again.'}</Text>}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
  },
  pinContainer: {
    justifyContent: 'space-between',
  },
  pinInput: {
    borderBottomWidth: 2,
    borderBottomColor: '#000',
    marginHorizontal: 10,
    width: 71,
    textAlign: 'center',
    fontSize: 24,
    paddingTop: 5,
  },
  focusedPinInput: {
    borderBottomColor: '#000093',  // Highlight color for focused input
  },
  errorPinInput: {
    borderBottomColor: 'red',  // Highlight color for error state
  },
  errorText: {
    color: 'red',
    marginTop: 10,
  },
});

export default PinCodeWithoutState;














