import React from 'react';
import {ColorValue, StatusBar, View} from 'react-native';
import {useSafeAreaInsets} from 'react-native-safe-area-context';

type CustomStatusBar = {
  color?: ColorValue;
};

export function CustomStatusBar({color}: CustomStatusBar): JSX.Element {
  const BAR_HEIGHT = useSafeAreaInsets().top;

  return (
    <View
      style={{
        backgroundColor: color ? color : '#0078AD',
        height: BAR_HEIGHT,
      }}>
      <StatusBar
        backgroundColor={color ? color : '#0078AD'}
        hidden={false}
        barStyle={'dark-content'}
      />
    </View>
  );
}
