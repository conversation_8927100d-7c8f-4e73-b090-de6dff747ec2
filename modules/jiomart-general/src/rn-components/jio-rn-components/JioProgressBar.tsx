import { useColor } from "@jio/rn_components";
import { JioColor } from "@jio/rn_components/src/index.types";
import { ColorValue, StyleProp, View, ViewStyle } from "react-native";

export interface JioProgressBarProps {
    progressColor?: JioColor;
    backgroundColor?: ColorValue;
    style?: StyleProp<ViewStyle> | undefined;
    maxValue: number | undefined;
    currentProgressValue: number | undefined;
    height?: number;
    borderRadius?: number | undefined
}

function JioProgressBar({
    progressColor = "secondary_60",
    backgroundColor = "#EBEBEB",
    maxValue = 1,
    currentProgressValue = 1,
    height = 4,
    style,
    borderRadius
}: JioProgressBarProps) {
    const progressBarColor = useColor(progressColor);
    const progress = currentProgressValue / maxValue;
    return (
        <View style={[{ backgroundColor: backgroundColor, height: height, flexDirection: "row" }, style]}>
            <View style={{ backgroundColor: progressBarColor, flex: progress, borderRadius }}>
            </View>
        </View>
    );
}

export default JioProgressBar;