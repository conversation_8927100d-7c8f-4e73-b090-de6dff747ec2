import {
  CustomJioIcon,
  Jio<PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Jio<PERSON>ex<PERSON>,
  useTheme,
} from '@jio/rn_components';
import {
  IconSize,
  IconColor,
  JioTypography,
  JioSpinnerSize,
  JioColor,
} from '@jio/rn_components/src/index.types';
import { useTypography } from '@jio/rn_components/src/typography';
import Feedback, {
  FeedbackProps,
  FeedbackState,
} from '@jio/rn_components/src/utils/Feedback';
import { IconKey } from '@jio/rn_components/src/utils/IconUtility';
import { useState } from 'react';
import {
  StyleProp,
  ViewStyle,
  TextStyle,
  KeyboardTypeOptions,
  ReturnKeyTypeOptions,
  TextInput,
  View,
  Pressable,
  Platform,
  StyleSheet,
  NativeSyntheticEvent,
  TextInputKeyPressEventData,
} from 'react-native';

export interface InputProperties extends FeedbackProps {
  textContainerStyle?: StyleProp<ViewStyle>;
  value?: string;
  maxLines?: number | undefined;
  readOnly?: boolean;
  disabled?: boolean;
  label?: string;
  multiline?: boolean;
  placeholder?: string;
  maxLength?: number;
  suffixIcon?: IconKey;
  viewStyle?: StyleProp<ViewStyle>;
  textStyle?: StyleProp<TextStyle>;
  prefixIcon?: IconKey;
  helperText?: string;
  keyboardType?: KeyboardTypeOptions;
  isLoading?: boolean;
  textContentType?: 'oneTimeCode' | undefined;
  returnKeyType?: ReturnKeyTypeOptions;
  autoFocus?: boolean;
  onChange?: (value: string) => void;
  onPrefixClick?: () => void;
  onSuffixClick?: () => void;
  onFocus?: () => void;
  onBlur?: () => void;
  onClick?: () => void;
  onSubmitEditing?: () => void;
  onRef?: (ref: TextInput | null) => void;
  onKeyPress?: (
    keyEvent: NativeSyntheticEvent<TextInputKeyPressEventData>,
  ) => void;
  suffixColor?: JioColor;
  secureTextEntry?: boolean;
  autoCapitalize?: 'none' | 'sentences' | 'words' | 'characters' | undefined;
  prefixText?: string;
  suffixText?: string;
  suffixDropDownText?: string;
  prefixPaddingBottom?: number
}

export function JioInputField({
  textContainerStyle,
  value = '',
  state = FeedbackState.CLEAR,
  readOnly = false,
  disabled = false,
  label = '',
  multiline = true,
  placeholder = '',
  maxLength,
  maxLines,
  prefixIcon,
  suffixIcon,
  prefixText,
  suffixText,
  isLoading = false,
  stateText = '',
  viewStyle,
  textStyle,
  helperText = '',
  keyboardType = 'default',
  textContentType,
  returnKeyType,
  autoFocus = false,
  onChange,
  onPrefixClick,
  onSuffixClick,
  onFocus,
  onBlur,
  onClick,
  onSubmitEditing,
  onRef,
  onKeyPress,
  suffixColor,
  secureTextEntry,
  autoCapitalize = 'none',
  suffixDropDownText,
  prefixPaddingBottom = 3
}: InputProperties) {
  const theme = useTheme();
  const [isFocused, setFocused] = useState(false);

  function getDividerColor() {
    switch (state) {
      case FeedbackState.ERROR:
        return theme.feedback_error_50;
      case FeedbackState.WARNING:
        return theme.feedback_warning_50;
      default:
        return theme.feedback_success_50;
    }
  }

  const handleTextChange = (text: string) => {
    let cleanedText = text;
    if (keyboardType === 'numeric' || keyboardType === 'number-pad') {
      cleanedText = text.replace(/[.,]|[^+\d]/g, '');
    } else if (keyboardType === 'email-address') {
      cleanedText = text.replace(/\s/g, '');
    }
    onChange?.(cleanedText);
  };

  return (
    <>
      <View style={[viewStyle, { opacity: disabled ? 0.4 : 1 }]}>
        {
          <Pressable style={[styles(prefixPaddingBottom > 0 ? 5 : 0).inputRow, { justifyContent: 'flex-start' }]} onPress={onClick}>
            {prefixIcon && (
              <Pressable onPress={onPrefixClick} style={styles(prefixPaddingBottom > 0 ? 5 : 0).iconStyle}>
                <JioIcon
                  ic={prefixIcon}
                  size={IconSize.MEDIUM}
                  color={IconColor.GREY80}></JioIcon>
              </Pressable>
            )}
            {prefixText && (
              <Pressable onPress={onPrefixClick} style={styles(prefixPaddingBottom > 0 ? 5 : 0).iconStyle}>
                <JioText
                  appearance={JioTypography.BODY_M_BOLD}
                  color='primary_grey_80'
                  text={prefixText}
                />
              </Pressable>
            )}
            <View pointerEvents={readOnly ? 'none' : 'auto'}
              style={textContainerStyle ? textContainerStyle : { flex: 1 }}>
              <TextInput
                ref={input => onRef?.(input)}
                style={[
                  textStyle,
                  textContentType !== 'oneTimeCode' &&
                  useTypography(JioTypography.BODY_S),
                  Platform.OS === 'ios' && { lineHeight: 0 },
                  { margin: 0, padding: 0, color: theme.primary_grey_100, paddingBottom: (prefixText || prefixIcon || suffixIcon || suffixText) ? prefixPaddingBottom : 0 },
                  textStyle
                ]}
                multiline={multiline}
                placeholder={label}
                maxLength={maxLength}
                numberOfLines={maxLines}
                placeholderTextColor={theme.secondary_grey_60}
                underlineColorAndroid="transparent"
                keyboardType={keyboardType}
                // textContentType={textContentType}
                returnKeyType={returnKeyType}
                autoFocus={autoFocus}
                value={value}
                onChangeText={handleTextChange}
                onFocus={onFocus}
                onBlur={onBlur}
                onSubmitEditing={() => {
                  onSubmitEditing ? onSubmitEditing() : null;
                }}
                onKeyPress={onKeyPress}
                secureTextEntry={secureTextEntry}
                autoCorrect={false}
                spellCheck={false}
                autoComplete='off'
                autoCapitalize={autoCapitalize}
              />
            </View>
            <JioText
              appearance={JioTypography.BODY_XXS}
              text={suffixDropDownText}
              color={"primary_grey_80"}
              style={{ marginHorizontal: 4 }}
            />
            {isLoading ? (
              <JioSpinner
                style={{ marginRight: 8 }}
                size={JioSpinnerSize.SMALL}></JioSpinner>
            ) : (
              (suffixIcon || suffixText) && (
                <Pressable onPress={onSuffixClick} style={styles(prefixPaddingBottom > 0 ? 5 : 0).iconStyle}>
                  {suffixIcon && (
                    <CustomJioIcon
                      ic={suffixIcon}
                      color={suffixColor ? suffixColor : IconColor.GREY80}
                      size={IconSize.MEDIUM}
                    />
                  )}
                  {suffixText && (
                    <JioText
                      style={{ paddingHorizontal: 12 }}
                      appearance={JioTypography.BODY_XXS}
                      color='primary_grey_80'
                      text={suffixText}
                    />
                  )}

                </Pressable>
              )
            )}
          </Pressable>
        }
        <Feedback state={state} stateText={stateText}></Feedback>

      </View>

    </>
  );
}


const styles = (paddingTop: number = 5) => StyleSheet.create({
  inputRow: {
    justifyContent: 'space-between',
    flexDirection: 'row',
    gap: 8,
    alignItems: 'center'
  },
  iconStyle: {
    alignSelf: 'center',
    paddingTop: Platform.OS === 'ios' ? paddingTop : 0
  },
});
