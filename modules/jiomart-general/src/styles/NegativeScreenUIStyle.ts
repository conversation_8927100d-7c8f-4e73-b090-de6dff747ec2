import {StyleSheet} from 'react-native';
import {rh, rw} from '../../../jiomart-common/src/JMResponsive';

export const styles = StyleSheet.create({
  container: {backgroundColor: '#ffffff', flex: 1},
  content: {
    marginHorizontal: rw(24),
    alignItems: 'center',
  },
  start: {marginTop: rh(34)},
  center: {justifyContent: 'center', flex: 1},
  image: {
    width: rw(164),
    height: rw(164),
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
  },
  bottom: {
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderTopWidth: 1,
    borderTopColor: '#E0E0E0',
    marginTop: 'auto',
  },
  marginBottom12: {
    marginBottom: 12,
  },
  flex: {
    flex: 1,
    backgroundColor: '#fff',
  },
});
