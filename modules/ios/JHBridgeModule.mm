#import <React/RCTBridgeModule.h>

@interface RCT_EXTERN_MODULE(JHBridgeModule, NSObject)


RCT_EXTERN_METHOD(openNativeCamera:(id)configData
                  withResolver: (RCTPromiseResolveBlock)resolve
                  withRejecter: (RCTPromiseRejectBlock)reject);

RCT_EXTERN_METHOD(convertSvgToPng:(id)data
                  withResolver: (RCTPromiseResolveBlock)resolve
                  withRejecter: (RCTPromiseRejectBlock)reject);
RCT_EXTERN_METHOD(showDatePicker:(id)data
                  withResolver: (RCTPromiseResolveBlock)resolve
                  withRejecter: (RCTPromiseRejectBlock)reject);
RCT_EXTERN_METHOD(keyboardResize:(id)data);

RCT_EXTERN_METHOD(isBiometricAvailable:(RCTPromiseResolveBlock)resolve reject:(RCTPromiseRejectBlock)reject)

RCT_EXTERN_METHOD(getBiometricType:(RCTPromiseResolveBlock)resolve reject:(RCTPromiseRejectBlock)reject)

RCT_EXTERN_METHOD(authenticateUser:(RCTPromiseResolveBlock)resolve reject:(RCTPromiseRejectBlock)reject)

+ (BOOL)requiresMainQueueSetup
{
  return NO;
}

@end
