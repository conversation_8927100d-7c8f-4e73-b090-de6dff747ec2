<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.Storyboard.XIB" version="3.0" toolsVersion="22155" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES" initialViewController="Asv-vb-Tfd">
    <device id="retina6_12" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="22131"/>
        <capability name="Named colors" minToolsVersion="9.0"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="System colors in document resources" minToolsVersion="11.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <scenes>
        <!--<PERSON>o Camera View Controller-->
        <scene sceneID="tne-QT-ifu">
            <objects>
                <viewController storyboardIdentifier="JioCameraViewController" useStoryboardIdentifierAsRestorationIdentifier="YES" id="BYZ-38-t0r" customClass="JioCameraViewController" customModule="JHBundle" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="8bC-Xf-vdC" customClass="JioView" customModule="JHBundle" customModuleProvider="target">
                        <rect key="frame" x="0.0" y="0.0" width="393" height="852"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="nS6-LS-KXe" customClass="JioTopNavigationBarView" customModule="JHBundle" customModuleProvider="target">
                                <rect key="frame" x="0.0" y="59" width="393" height="64"/>
                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="64" id="zBq-Nx-h6N"/>
                                </constraints>
                            </view>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="T39-Qh-lDf" customClass="JioView" customModule="JioDesignSystem">
                                <rect key="frame" x="0.0" y="123" width="393" height="729"/>
                                <subviews>
                                    <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" translatesAutoresizingMaskIntoConstraints="NO" id="Mxt-ne-jhq">
                                        <rect key="frame" x="0.0" y="0.0" width="393" height="729"/>
                                        <subviews>
                                            <view hidden="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="m7i-oh-e8e" userLabel="AllowPermissionView" customClass="JioView" customModule="JioDesignSystem">
                                                <rect key="frame" x="0.0" y="0.0" width="393" height="0.0"/>
                                                <subviews>
                                                    <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="32" translatesAutoresizingMaskIntoConstraints="NO" id="VeL-lh-50n">
                                                        <rect key="frame" x="60" y="-52.333333333333329" width="273" height="104.66666666666667"/>
                                                        <subviews>
                                                            <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="8" translatesAutoresizingMaskIntoConstraints="NO" id="bgx-So-dwG">
                                                                <rect key="frame" x="0.0" y="0.0" width="273" height="48.666666666666664"/>
                                                                <subviews>
                                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="center" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="lab-8w-MKo" customClass="JioLabel" customModule="JioDesignSystem">
                                                                        <rect key="frame" x="0.0" y="0.0" width="273" height="20.333333333333332"/>
                                                                        <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                                                        <nil key="textColor"/>
                                                                        <nil key="highlightedColor"/>
                                                                    </label>
                                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="center" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="ZKx-ws-vUa" customClass="JioLabel" customModule="JioDesignSystem">
                                                                        <rect key="frame" x="0.0" y="28.333333333333329" width="273" height="20.333333333333329"/>
                                                                        <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                                                        <nil key="textColor"/>
                                                                        <nil key="highlightedColor"/>
                                                                    </label>
                                                                </subviews>
                                                            </stackView>
                                                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="Kgf-ic-HwR" customClass="JioButton" customModule="JioDesignSystem">
                                                                <rect key="frame" x="0.0" y="80.666666666666671" width="273" height="24"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="height" priority="999" constant="24" id="I0T-GI-HLD"/>
                                                                </constraints>
                                                                <state key="normal" title="Button"/>
                                                                <buttonConfiguration key="configuration" style="plain" title="Button"/>
                                                            </button>
                                                        </subviews>
                                                        <constraints>
                                                            <constraint firstItem="bgx-So-dwG" firstAttribute="leading" secondItem="VeL-lh-50n" secondAttribute="leading" id="HNs-f6-fFD"/>
                                                            <constraint firstAttribute="trailing" secondItem="bgx-So-dwG" secondAttribute="trailing" id="kyk-Fc-V6o"/>
                                                        </constraints>
                                                    </stackView>
                                                </subviews>
                                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                                <constraints>
                                                    <constraint firstAttribute="trailing" secondItem="VeL-lh-50n" secondAttribute="trailing" constant="60" id="F5U-6n-tgh"/>
                                                    <constraint firstItem="VeL-lh-50n" firstAttribute="leading" secondItem="m7i-oh-e8e" secondAttribute="leading" constant="60" id="eWn-FO-1fG"/>
                                                    <constraint firstItem="VeL-lh-50n" firstAttribute="centerY" secondItem="m7i-oh-e8e" secondAttribute="centerY" id="kXq-iw-a06"/>
                                                </constraints>
                                            </view>
                                            <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" translatesAutoresizingMaskIntoConstraints="NO" id="gMJ-4T-CPY">
                                                <rect key="frame" x="0.0" y="0.0" width="393" height="633"/>
                                                <subviews>
                                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="cTN-vf-u4a" customClass="JioView" customModule="JioDesignSystem">
                                                        <rect key="frame" x="0.0" y="0.0" width="393" height="80"/>
                                                        <subviews>
                                                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="Pmc-Bk-hGO" customClass="JioLabel" customModule="JioDesignSystem">
                                                                <rect key="frame" x="24" y="18" width="345" height="44"/>
                                                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                            </view>
                                                        </subviews>
                                                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                        <constraints>
                                                            <constraint firstItem="Pmc-Bk-hGO" firstAttribute="top" secondItem="cTN-vf-u4a" secondAttribute="top" constant="18" id="h4M-le-tj1"/>
                                                            <constraint firstItem="Pmc-Bk-hGO" firstAttribute="leading" secondItem="cTN-vf-u4a" secondAttribute="leading" constant="24" id="hD6-BM-Q1L"/>
                                                            <constraint firstAttribute="height" constant="80" id="jQo-kS-jqM"/>
                                                            <constraint firstAttribute="trailing" secondItem="Pmc-Bk-hGO" secondAttribute="trailing" constant="24" id="jX1-cl-vla"/>
                                                            <constraint firstAttribute="bottom" secondItem="Pmc-Bk-hGO" secondAttribute="bottom" constant="18" id="r3u-cg-hLF"/>
                                                        </constraints>
                                                    </view>
                                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="v89-jH-1Gc" customClass="JioView" customModule="JioDesignSystem">
                                                        <rect key="frame" x="0.0" y="80" width="393" height="471"/>
                                                        <subviews>
                                                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="WKc-pC-dIy" customClass="JioImageView" customModule="JioDesignSystem">
                                                                <rect key="frame" x="24" y="80" width="345" height="311"/>
                                                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                            </view>
                                                        </subviews>
                                                        <color key="backgroundColor" name="JioColor.Warm.Gold.Light.color_grey_80"/>
                                                        <constraints>
                                                            <constraint firstItem="WKc-pC-dIy" firstAttribute="top" secondItem="v89-jH-1Gc" secondAttribute="top" constant="80" id="1Ha-wD-GZA"/>
                                                            <constraint firstAttribute="trailing" secondItem="WKc-pC-dIy" secondAttribute="trailing" constant="24" id="Fgc-Bj-xf9"/>
                                                            <constraint firstAttribute="bottom" secondItem="WKc-pC-dIy" secondAttribute="bottom" constant="80" id="nVL-ki-V76"/>
                                                            <constraint firstItem="WKc-pC-dIy" firstAttribute="leading" secondItem="v89-jH-1Gc" secondAttribute="leading" constant="24" id="zsM-8z-4kB"/>
                                                        </constraints>
                                                    </view>
                                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="IE0-r3-WAf" customClass="JioView" customModule="JioDesignSystem">
                                                        <rect key="frame" x="0.0" y="551" width="393" height="82"/>
                                                        <subviews>
                                                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="JHf-V0-t69" customClass="JioLabel" customModule="JioDesignSystem">
                                                                <rect key="frame" x="24" y="28" width="345" height="26"/>
                                                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                                            </view>
                                                        </subviews>
                                                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                        <constraints>
                                                            <constraint firstAttribute="height" constant="82" id="8cf-1o-tAb"/>
                                                            <constraint firstAttribute="trailing" secondItem="JHf-V0-t69" secondAttribute="trailing" constant="24" id="AIE-vQ-tkt"/>
                                                            <constraint firstItem="JHf-V0-t69" firstAttribute="top" secondItem="IE0-r3-WAf" secondAttribute="top" constant="28" id="aN9-cx-WaI"/>
                                                            <constraint firstAttribute="bottom" secondItem="JHf-V0-t69" secondAttribute="bottom" constant="28" id="n0E-Hi-fau"/>
                                                            <constraint firstItem="JHf-V0-t69" firstAttribute="leading" secondItem="IE0-r3-WAf" secondAttribute="leading" constant="24" id="qie-YO-vf1"/>
                                                        </constraints>
                                                    </view>
                                                </subviews>
                                            </stackView>
                                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="Af2-AJ-WSU" customClass="JioView" customModule="JioDesignSystem">
                                                <rect key="frame" x="0.0" y="633" width="393" height="96"/>
                                                <subviews>
                                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="0c8-ps-fMd" customClass="JioButton" customModule="JioDesignSystem">
                                                        <rect key="frame" x="172.66666666666666" y="24" width="48" height="48"/>
                                                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                                        <constraints>
                                                            <constraint firstAttribute="width" constant="48" id="NTV-j3-bAY"/>
                                                            <constraint firstAttribute="height" constant="48" id="qzr-Q4-VkU"/>
                                                        </constraints>
                                                    </view>
                                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="JM4-7E-XXn" customClass="JioButton" customModule="JioDesignSystem">
                                                        <rect key="frame" x="321" y="24" width="48" height="48"/>
                                                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                                        <constraints>
                                                            <constraint firstAttribute="width" constant="48" id="VpI-6N-yvC"/>
                                                            <constraint firstAttribute="height" constant="48" id="iGd-Pm-Fqc"/>
                                                        </constraints>
                                                    </view>
                                                </subviews>
                                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                <constraints>
                                                    <constraint firstAttribute="bottom" secondItem="0c8-ps-fMd" secondAttribute="bottom" constant="24" id="5YP-wW-RE5"/>
                                                    <constraint firstItem="0c8-ps-fMd" firstAttribute="top" secondItem="Af2-AJ-WSU" secondAttribute="top" constant="24" id="HGp-OL-esd"/>
                                                    <constraint firstAttribute="trailing" secondItem="JM4-7E-XXn" secondAttribute="trailing" constant="24" id="KAx-1J-5Y0"/>
                                                    <constraint firstItem="JM4-7E-XXn" firstAttribute="top" secondItem="0c8-ps-fMd" secondAttribute="top" id="Oe4-0E-Ba8"/>
                                                    <constraint firstItem="0c8-ps-fMd" firstAttribute="centerX" secondItem="Af2-AJ-WSU" secondAttribute="centerX" id="fkg-vb-Q0F"/>
                                                    <constraint firstAttribute="height" constant="96" id="jMl-7P-KSJ"/>
                                                </constraints>
                                            </view>
                                        </subviews>
                                        <constraints>
                                            <constraint firstAttribute="trailing" secondItem="m7i-oh-e8e" secondAttribute="trailing" id="J5j-AQ-rgg"/>
                                            <constraint firstAttribute="bottom" secondItem="Af2-AJ-WSU" secondAttribute="bottom" id="j3H-X0-8jG"/>
                                            <constraint firstItem="m7i-oh-e8e" firstAttribute="leading" secondItem="Mxt-ne-jhq" secondAttribute="leading" id="sPL-mE-TbO"/>
                                            <constraint firstAttribute="trailing" secondItem="Af2-AJ-WSU" secondAttribute="trailing" id="ttR-Jl-Euq"/>
                                            <constraint firstItem="Af2-AJ-WSU" firstAttribute="leading" secondItem="Mxt-ne-jhq" secondAttribute="leading" id="vqy-aW-1dx"/>
                                        </constraints>
                                    </stackView>
                                </subviews>
                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                <constraints>
                                    <constraint firstItem="Mxt-ne-jhq" firstAttribute="leading" secondItem="T39-Qh-lDf" secondAttribute="leading" id="HrA-Y5-jPP"/>
                                    <constraint firstAttribute="bottom" secondItem="Mxt-ne-jhq" secondAttribute="bottom" id="eht-L7-1VB"/>
                                    <constraint firstAttribute="trailing" secondItem="Mxt-ne-jhq" secondAttribute="trailing" id="kG8-W4-EAX"/>
                                    <constraint firstItem="Mxt-ne-jhq" firstAttribute="top" secondItem="T39-Qh-lDf" secondAttribute="top" id="txr-U1-MmU"/>
                                </constraints>
                            </view>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="6Tk-OE-BBY"/>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                        <constraints>
                            <constraint firstItem="T39-Qh-lDf" firstAttribute="top" secondItem="nS6-LS-KXe" secondAttribute="bottom" id="CKR-UX-V5f"/>
                            <constraint firstItem="T39-Qh-lDf" firstAttribute="leading" secondItem="6Tk-OE-BBY" secondAttribute="leading" id="OTm-8m-RkC"/>
                            <constraint firstItem="nS6-LS-KXe" firstAttribute="leading" secondItem="6Tk-OE-BBY" secondAttribute="leading" id="PkO-bR-9Kh"/>
                            <constraint firstItem="T39-Qh-lDf" firstAttribute="trailing" secondItem="6Tk-OE-BBY" secondAttribute="trailing" id="XyJ-LK-3J3"/>
                            <constraint firstItem="nS6-LS-KXe" firstAttribute="trailing" secondItem="6Tk-OE-BBY" secondAttribute="trailing" id="Z4o-Iw-1Fl"/>
                            <constraint firstAttribute="bottom" secondItem="T39-Qh-lDf" secondAttribute="bottom" id="dzP-yi-ns4"/>
                            <constraint firstItem="nS6-LS-KXe" firstAttribute="top" secondItem="6Tk-OE-BBY" secondAttribute="top" id="eKb-7K-kgO"/>
                        </constraints>
                    </view>
                    <navigationItem key="navigationItem" id="gOq-ek-fpY"/>
                    <nil key="simulatedTopBarMetrics"/>
                    <nil key="simulatedBottomBarMetrics"/>
                    <freeformSimulatedSizeMetrics key="simulatedDestinationMetrics"/>
                    <connections>
                        <outlet property="allowPermissionSubTitle" destination="ZKx-ws-vUa" id="jga-xP-CdG"/>
                        <outlet property="allowPermissionTitle" destination="lab-8w-MKo" id="VE7-zH-6ee"/>
                        <outlet property="allowPermissionView" destination="m7i-oh-e8e" id="8fS-gf-mGX"/>
                        <outlet property="bottomTitleLabel" destination="JHf-V0-t69" id="vdW-m7-5Hv"/>
                        <outlet property="bottomTitleView" destination="IE0-r3-WAf" id="JU1-LT-244"/>
                        <outlet property="cameraActionButton" destination="0c8-ps-fMd" id="swk-tW-Q6d"/>
                        <outlet property="cameraFocusView" destination="WKc-pC-dIy" id="8Am-NK-f9I"/>
                        <outlet property="cameraView" destination="v89-jH-1Gc" id="7bV-u4-m0R"/>
                        <outlet property="containerView" destination="T39-Qh-lDf" id="tTd-8Z-4SQ"/>
                        <outlet property="footerView" destination="Af2-AJ-WSU" id="Mif-p5-Xoi"/>
                        <outlet property="galleryActionButton" destination="JM4-7E-XXn" id="P8x-Cx-htJ"/>
                        <outlet property="headerTitle" destination="Pmc-Bk-hGO" id="iyc-j4-HJg"/>
                        <outlet property="headerTitleView" destination="cTN-vf-u4a" id="slB-C3-7tV"/>
                        <outlet property="mainStackView" destination="gMJ-4T-CPY" id="4Pg-T2-fIp"/>
                        <outlet property="mainView" destination="8bC-Xf-vdC" id="Bij-si-nNd"/>
                        <outlet property="settingRedirectionButton" destination="Kgf-ic-HwR" id="Rcv-au-7Vl"/>
                        <outlet property="topNavigationBar" destination="nS6-LS-KXe" id="coK-zc-9Un"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="dkx-z0-nzr" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="1924.4274809160304" y="3.5211267605633805"/>
        </scene>
        <!--Date Picker View Controller-->
        <scene sceneID="4ah-qY-Vk7">
            <objects>
                <viewController storyboardIdentifier="JHDatePickerViewController" useStoryboardIdentifierAsRestorationIdentifier="YES" id="JRL-YM-g6Y" customClass="JHDatePickerViewController" customModule="JHBundle" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="Q1L-82-d4K" customClass="JioView" customModule="JioDesignSystem">
                        <rect key="frame" x="0.0" y="0.0" width="393" height="852"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="7dA-8y-eLO" customClass="JioView" customModule="JioDesignSystem">
                                <rect key="frame" x="20" y="418" width="353" height="400"/>
                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="400" id="ioW-fJ-7iq"/>
                                </constraints>
                            </view>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="f40-60-rYA"/>
                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <constraints>
                            <constraint firstItem="f40-60-rYA" firstAttribute="trailing" secondItem="7dA-8y-eLO" secondAttribute="trailing" constant="20" id="2Dw-tl-Os9"/>
                            <constraint firstItem="7dA-8y-eLO" firstAttribute="leading" secondItem="f40-60-rYA" secondAttribute="leading" constant="20" id="aXJ-Kr-2Kw"/>
                            <constraint firstItem="f40-60-rYA" firstAttribute="bottom" secondItem="7dA-8y-eLO" secondAttribute="bottom" id="ymm-QG-CdC"/>
                        </constraints>
                    </view>
                    <connections>
                        <outlet property="containerView" destination="Q1L-82-d4K" id="qhh-c2-OnF"/>
                        <outlet property="datePickerBackgroundView" destination="7dA-8y-eLO" id="Ii8-ft-s7H"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="Vzy-Ab-JhE" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="1924" y="715"/>
        </scene>
        <!--Jio Preview Image View Controller-->
        <scene sceneID="67M-hx-1oh">
            <objects>
                <viewController storyboardIdentifier="JioPreviewImageViewController" useStoryboardIdentifierAsRestorationIdentifier="YES" id="PgP-ar-OFe" customClass="JioPreviewImageViewController" customModule="JHBundle" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="qFR-N3-dA2" customClass="JioView" customModule="JHBundle" customModuleProvider="target">
                        <rect key="frame" x="0.0" y="0.0" width="393" height="852"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="6OQ-Dx-18n" customClass="JioTopNavigationBarView" customModule="JHBundle" customModuleProvider="target">
                                <rect key="frame" x="0.0" y="59" width="393" height="64"/>
                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="64" id="6FO-3J-ssY"/>
                                </constraints>
                            </view>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="D7o-iz-LWn" customClass="JioView" customModule="JioDesignSystem">
                                <rect key="frame" x="0.0" y="123" width="393" height="729"/>
                                <subviews>
                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="LrM-Gj-BE4" customClass="JioView" customModule="JioDesignSystem">
                                        <rect key="frame" x="0.0" y="0.0" width="393" height="76"/>
                                        <subviews>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="1Df-m8-uUa" customClass="JioLabel" customModule="JioDesignSystem">
                                                <rect key="frame" x="24" y="18" width="345" height="40"/>
                                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                                <nil key="textColor"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                        </subviews>
                                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                        <constraints>
                                            <constraint firstAttribute="bottom" secondItem="1Df-m8-uUa" secondAttribute="bottom" constant="18" id="5WD-sX-L4v"/>
                                            <constraint firstAttribute="height" relation="greaterThanOrEqual" constant="76" id="RYx-jq-k6k"/>
                                            <constraint firstAttribute="trailing" secondItem="1Df-m8-uUa" secondAttribute="trailing" constant="24" id="U83-aP-Rey"/>
                                            <constraint firstItem="1Df-m8-uUa" firstAttribute="leading" secondItem="LrM-Gj-BE4" secondAttribute="leading" constant="24" id="ZhT-ed-9xm"/>
                                            <constraint firstItem="1Df-m8-uUa" firstAttribute="top" secondItem="LrM-Gj-BE4" secondAttribute="top" constant="18" id="blL-rn-44d"/>
                                        </constraints>
                                    </view>
                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="As3-Qx-zt7" customClass="UIImageView">
                                        <rect key="frame" x="28" y="147" width="337" height="337"/>
                                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        <constraints>
                                            <constraint firstAttribute="width" secondItem="As3-Qx-zt7" secondAttribute="height" multiplier="1:1" id="YxJ-4s-zz3"/>
                                        </constraints>
                                    </view>
                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="zmy-px-FEE" customClass="JioView" customModule="JioDesignSystem">
                                        <rect key="frame" x="0.0" y="637" width="393" height="92"/>
                                        <subviews>
                                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="qEw-iY-tUs" customClass="JioButton" customModule="JioDesignSystem">
                                                <rect key="frame" x="88" y="12" width="56" height="56"/>
                                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                                <constraints>
                                                    <constraint firstAttribute="width" constant="56" id="ApI-bH-fhd"/>
                                                    <constraint firstAttribute="height" constant="56" id="rt6-gM-F6U"/>
                                                </constraints>
                                            </view>
                                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="3fl-h2-CeF" customClass="JioButton" customModule="JioDesignSystem">
                                                <rect key="frame" x="249" y="12" width="56" height="56"/>
                                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="56" id="Nv3-jJ-Kqa"/>
                                                    <constraint firstAttribute="width" constant="56" id="YKw-1y-byE"/>
                                                </constraints>
                                            </view>
                                        </subviews>
                                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                        <constraints>
                                            <constraint firstAttribute="bottom" secondItem="3fl-h2-CeF" secondAttribute="bottom" constant="24" id="I9p-f6-dRm"/>
                                            <constraint firstItem="qEw-iY-tUs" firstAttribute="top" secondItem="zmy-px-FEE" secondAttribute="top" constant="12" id="KSO-i6-PnR"/>
                                            <constraint firstAttribute="bottom" secondItem="qEw-iY-tUs" secondAttribute="bottom" constant="24" id="XU4-2V-5A4"/>
                                            <constraint firstAttribute="trailing" secondItem="3fl-h2-CeF" secondAttribute="trailing" constant="88" id="c5H-0G-FLE"/>
                                            <constraint firstItem="qEw-iY-tUs" firstAttribute="leading" secondItem="zmy-px-FEE" secondAttribute="leading" constant="88" id="spK-zj-uQt"/>
                                            <constraint firstItem="3fl-h2-CeF" firstAttribute="top" secondItem="zmy-px-FEE" secondAttribute="top" constant="12" id="uAC-n9-AYt"/>
                                            <constraint firstAttribute="height" constant="92" id="wof-7u-FQ8"/>
                                        </constraints>
                                    </view>
                                </subviews>
                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <constraints>
                                    <constraint firstAttribute="trailing" secondItem="LrM-Gj-BE4" secondAttribute="trailing" id="9fc-TY-Nhw"/>
                                    <constraint firstAttribute="trailing" secondItem="zmy-px-FEE" secondAttribute="trailing" id="DOO-tI-Kwa"/>
                                    <constraint firstAttribute="trailing" secondItem="As3-Qx-zt7" secondAttribute="trailing" constant="28" id="Kyo-tg-IHB"/>
                                    <constraint firstItem="zmy-px-FEE" firstAttribute="leading" secondItem="D7o-iz-LWn" secondAttribute="leading" id="e9x-jr-QVp"/>
                                    <constraint firstItem="As3-Qx-zt7" firstAttribute="leading" secondItem="D7o-iz-LWn" secondAttribute="leading" constant="28" id="gRV-lC-k31"/>
                                    <constraint firstAttribute="bottom" secondItem="zmy-px-FEE" secondAttribute="bottom" id="gRb-zk-Xur"/>
                                    <constraint firstItem="LrM-Gj-BE4" firstAttribute="top" secondItem="D7o-iz-LWn" secondAttribute="top" id="uLN-xP-efP"/>
                                    <constraint firstItem="LrM-Gj-BE4" firstAttribute="leading" secondItem="D7o-iz-LWn" secondAttribute="leading" id="yRf-fX-HoD"/>
                                </constraints>
                            </view>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="EC7-sA-3Gr"/>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                        <constraints>
                            <constraint firstItem="6OQ-Dx-18n" firstAttribute="top" secondItem="EC7-sA-3Gr" secondAttribute="top" id="3af-UN-wzU"/>
                            <constraint firstAttribute="trailing" secondItem="6OQ-Dx-18n" secondAttribute="trailing" id="DDw-3U-kJH"/>
                            <constraint firstItem="As3-Qx-zt7" firstAttribute="centerY" secondItem="EC7-sA-3Gr" secondAttribute="centerY" id="DXf-IF-Bed"/>
                            <constraint firstAttribute="bottom" secondItem="D7o-iz-LWn" secondAttribute="bottom" id="Eg9-se-zDs"/>
                            <constraint firstItem="D7o-iz-LWn" firstAttribute="leading" secondItem="EC7-sA-3Gr" secondAttribute="leading" id="L5J-CC-XTr"/>
                            <constraint firstItem="D7o-iz-LWn" firstAttribute="trailing" secondItem="EC7-sA-3Gr" secondAttribute="trailing" id="YLy-ox-za4"/>
                            <constraint firstItem="6OQ-Dx-18n" firstAttribute="leading" secondItem="EC7-sA-3Gr" secondAttribute="leading" id="asN-Tk-EAw"/>
                            <constraint firstItem="D7o-iz-LWn" firstAttribute="top" secondItem="6OQ-Dx-18n" secondAttribute="bottom" id="guN-Ql-jZ0"/>
                        </constraints>
                    </view>
                    <freeformSimulatedSizeMetrics key="simulatedDestinationMetrics"/>
                    <connections>
                        <outlet property="bottomImageView" destination="zmy-px-FEE" id="dBW-Gi-aZs"/>
                        <outlet property="bottomLeftButton" destination="qEw-iY-tUs" id="okj-iW-zgh"/>
                        <outlet property="bottomRightButton" destination="3fl-h2-CeF" id="V67-gw-Kce"/>
                        <outlet property="containerView" destination="D7o-iz-LWn" id="t1d-RT-Ai0"/>
                        <outlet property="headerTitleLabel" destination="1Df-m8-uUa" id="RbW-qk-Nh4"/>
                        <outlet property="headerTitleView" destination="LrM-Gj-BE4" id="hgB-O3-Xyw"/>
                        <outlet property="imageView" destination="As3-Qx-zt7" id="FUz-gp-qPJ"/>
                        <outlet property="mainView" destination="qFR-N3-dA2" id="DY8-SN-fFA"/>
                        <outlet property="topNavigationBarView" destination="6OQ-Dx-18n" id="IxG-Ud-pVA"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="x9g-05-vZf" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="2780.9160305343512" y="3.5211267605633805"/>
        </scene>
        <!--Navigation Controller-->
        <scene sceneID="ql9-ha-hMp">
            <objects>
                <navigationController automaticallyAdjustsScrollViewInsets="NO" id="Asv-vb-Tfd" sceneMemberID="viewController">
                    <toolbarItems/>
                    <navigationBar key="navigationBar" contentMode="scaleToFill" id="X3Y-nW-e2P">
                        <rect key="frame" x="0.0" y="59" width="393" height="44"/>
                        <autoresizingMask key="autoresizingMask"/>
                    </navigationBar>
                    <nil name="viewControllers"/>
                    <connections>
                        <segue destination="BYZ-38-t0r" kind="relationship" relationship="rootViewController" id="Kvt-PA-SbH"/>
                    </connections>
                </navigationController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="UwT-lG-o83" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="997.70992366412213" y="3.5211267605633805"/>
        </scene>
    </scenes>
    <resources>
        <namedColor name="JioColor.Warm.Gold.Light.color_grey_80">
            <color red="0.0" green="0.0" blue="0.0" alpha="0.64999997615814209" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <systemColor name="systemBackgroundColor">
            <color white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
        </systemColor>
    </resources>
</document>
