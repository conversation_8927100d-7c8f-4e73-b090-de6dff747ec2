//
//  JHDatePickerViewController.swift
//  jio-black-rock-JHBundle
//
//

import Foundation
import UIKit
import JioDesignSystem

class JHDatePickerViewController: UIViewController {
    @IBOutlet weak var containerView: JioView!
    @IBOutlet weak var datePickerBackgroundView: JioView!
    
    var currentRNController: UIViewController? = nil
    var onDateSelected: ((Date) -> Void)?
    var onClose: (() -> Void)?
    var maxDate: Date?
    var minDate: Date?
    var selectedDate: Date?
    var disableWeekends: Bool = false
    private var datePicker: UIDatePicker!
    private var toolbar: UIToolbar?
    
    override func viewDidLoad() {
        super.viewDidLoad()
        view.backgroundColor = .black.withAlphaComponent(0.5)
        setUpDatePickerView()
    }
    
    func setUpDatePickerView() {
        updateBackgroundColor()
        datePickerBackgroundView.layer.cornerRadius = 18
        self.datePicker = UIDatePicker()
        
        datePicker.datePickerMode = .date
        if #available(iOS 14.0, *) {
            datePicker.preferredDatePickerStyle = .inline
        }
        if let maxDate = maxDate { datePicker.maximumDate = maxDate }
        if let minDate = minDate { datePicker.minimumDate = minDate }
        if let selectedDate = selectedDate { datePicker.date = selectedDate }
        
        datePicker.addTarget(self, action: #selector(dateChanged(_:)), for: .valueChanged)
        datePicker.frame = datePickerBackgroundView.bounds
        setUpToolBar(datePicker: datePicker)
    }
    
    func setUpToolBar(datePicker: UIDatePicker) {
        toolbar = UIToolbar()
        toolbar?.sizeToFit()
        let cancelButton = UIBarButtonItem(title: "Cancel", style: .plain, target: self, action: #selector(cancelButtonTapped))
        let flexSpace = UIBarButtonItem(barButtonSystemItem: .flexibleSpace, target: nil, action: nil)
        let doneButton = UIBarButtonItem(title: "Done", style: .plain, target: self, action: #selector(doneButtonTapped))
        toolbar?.setItems([cancelButton, flexSpace, doneButton], animated: false)
        updateToolbarColors()
        
        if let toolbar = toolbar {
            datePickerBackgroundView.addSubview(toolbar)
            datePickerBackgroundView.addSubview(datePicker)
            setUpConstriaints(toolbar , datePicker)
        }
    }
    
    func setUpConstriaints(_ toolbar: UIToolbar, _ datePicker: UIDatePicker) {
        toolbar.translatesAutoresizingMaskIntoConstraints = false
        datePicker.translatesAutoresizingMaskIntoConstraints = false
        toolbar.backgroundColor = .white
        toolbar.layer.cornerRadius = 18
        toolbar.layer.maskedCorners = [.layerMinXMinYCorner, .layerMaxXMinYCorner]
        toolbar.clipsToBounds = true
        NSLayoutConstraint.activate([
            toolbar.leadingAnchor.constraint(equalTo: datePickerBackgroundView.leadingAnchor),
            toolbar.trailingAnchor.constraint(equalTo: datePickerBackgroundView.trailingAnchor),
            toolbar.heightAnchor.constraint(equalToConstant: 50),
            toolbar.topAnchor.constraint(equalTo: datePickerBackgroundView.topAnchor),
            
            datePicker.leadingAnchor.constraint(equalTo: datePickerBackgroundView.leadingAnchor),
            datePicker.trailingAnchor.constraint(equalTo: datePickerBackgroundView.trailingAnchor),
            datePicker.topAnchor.constraint(equalTo: toolbar.bottomAnchor),
            datePicker.bottomAnchor.constraint(equalTo: datePickerBackgroundView.bottomAnchor)
        ])
    }
    
    @objc func dateChanged(_ datePicker: UIDatePicker) {
        let selectedDate = datePicker.date
        if disableWeekends && !isWeekday(date: selectedDate) {
            datePicker.date = nextWeekday(from: selectedDate)
            showToast(message: "Weekends are not allowed.")
        }
        self.datePicker.date = datePicker.date
    }
    
    @objc func doneButtonTapped() {
        let selectedDate = datePicker.date
        onDateSelected?(selectedDate)
        
        if let _ = currentRNController {
            UIView.animate(withDuration: 0.1, animations: {
                self.view.alpha = 0
            }, completion: { _ in
                self.willMove(toParent: nil)
                self.view.removeFromSuperview()
                self.removeFromParent()
            })
        } else {
            dismiss(animated: true, completion: nil)
        }
    }
    
    @objc func cancelButtonTapped() {
        onClose?()
        
        if let _ = currentRNController {
            UIView.animate(withDuration: 0.1, animations: {
                self.view.alpha = 0
            }, completion: { _ in
                self.willMove(toParent: nil)
                self.view.removeFromSuperview()
                self.removeFromParent()
            })
        } else {
            dismiss(animated: true, completion: nil)
        }
    }
    func isWeekday(date: Date) -> Bool {
        let calendar = Calendar.current
        let components = calendar.dateComponents([.weekday], from: date)
        if let weekday = components.weekday {
            return weekday != 1 && weekday != 7         }
        return false
    }
    
    func nextWeekday(from date: Date) -> Date {
        var nextDate = date
        let calendar = Calendar.current
        while !isWeekday(date: nextDate) {
            nextDate = calendar.date(byAdding: .day, value: 1, to: nextDate)!
        }
        return nextDate
    }
    
    func showToast(title: String = "", message: String, showClose: Bool = true, toastExtraBottomPadding: CGFloat = 0) {
        let notificationProps = NotificationProps(
            title: title,
            description: message,
            showClose: showClose
        )
        _ = ToastService.errorToast(notificationProps: notificationProps)
    }
    
    override func traitCollectionDidChange(_ previousTraitCollection: UITraitCollection?) {
        super.traitCollectionDidChange(previousTraitCollection)
        if traitCollection.hasDifferentColorAppearance(comparedTo: previousTraitCollection) {
            updateBackgroundColor()
            updateToolbarColors()
        }
    }
    
    private func updateBackgroundColor() {
        if traitCollection.userInterfaceStyle == .dark {
            datePickerBackgroundView.backgroundColor = .black
            datePicker?.tintColor = .white
        } else {
            datePickerBackgroundView.backgroundColor = .white
        }
    }
    
    private func updateToolbarColors() {
        if let toolbar = toolbar {
            if traitCollection.userInterfaceStyle == .dark {
                toolbar.barTintColor = .black
                toolbar.tintColor = .white
            }
        }
    }
}
