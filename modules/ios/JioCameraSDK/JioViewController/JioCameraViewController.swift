//
//  JioCameraViewController.swift
//  JioCameraSDK
//
//  Created by <PERSON><PERSON> on 23/04/24.
//

import UIKit
import JioDesignSystem
import AVFoundation
import Photos

public typealias ThemeDetailData = (theme: Theme, modeType: ModeType, scheme: Scheme)

class JioCameraViewController: UIViewController {
    
    @IBOutlet var mainView: JioView!
    @IBOutlet weak var containerView: JioView!
    @IBOutlet weak var headerTitleView: JioView!
    @IBOutlet weak var cameraFocusView: JioImageView!
    @IBOutlet weak var bottomTitleView: JioView!
    @IBOutlet weak var cameraView: JioView!
    @IBOutlet weak var headerTitle: JioLabel!
    @IBOutlet weak var topNavigationBar: JioTopNavigationBarView!
    @IBOutlet weak var bottomTitleLabel: JioLabel!
    @IBOutlet weak var cameraActionButton: JioButton!
    @IBOutlet weak var galleryActionButton: JioButton!
    @IBOutlet weak var footerView: JioView!
    @IBOutlet weak var allowPermissionView: JioView!
    @IBOutlet weak var mainStackView: UIStackView!
    @IBOutlet weak var allowPermissionTitle: JioLabel!
    @IBOutlet weak var allowPermissionSubTitle: JioLabel!
    @IBOutlet weak var settingRedirectionButton: JioButton!
    
    let captureSession = AVCaptureSession()
    var backCamera: AVCaptureDevice?
    var frontCamera: AVCaptureDevice?
    var currentCamera: AVCaptureDevice?
    var photoOutput: AVCapturePhotoOutput?
    var cameraPreviewLayer: AVCaptureVideoPreviewLayer?
    var image: UIImage?
    var focusSquare: UIView?
    var themeDetails = JioCameraSDKModel.shared.themeDetail
    var configData = JioCameraSDKModel.shared.configData
    var isAuthorized: Bool {
        get async {
            let status = AVCaptureDevice.authorizationStatus(for: .video)
            
            // Determine if the user previously authorized camera access.
            var isAuthorized = status == .authorized
            
            // If the system hasn't determined the user's authorization status,
            // explicitly prompt them for approval.
            if status == .notDetermined {
                isAuthorized = await AVCaptureDevice.requestAccess(for: .video)
            }
            
            return isAuthorized
        }
    }
    
    override func viewDidLoad() {
        super.viewDidLoad()
        setThemeDetails(themeDetails: self.themeDetails)
        navigationController?.setNavigationBarHidden(true, animated: true)
        navigationController?.navigationBar.barTintColor = themeDetails.scheme.color20
        self.mainView.backgroundColor = themeDetails.scheme.color20
        setUpAllViews()
        setUpGallery()
        setUpCaptureSession()
        setUpDevice()
        capturePhoto()
        setUpTopNavBar()
        if let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene {
            if let statusBarManager = windowScene.statusBarManager {
                let statusBarFrame = statusBarManager.statusBarFrame
                let statusBarView = UIView(frame: statusBarFrame)
                statusBarView.backgroundColor = themeDetails.scheme.color20
                view.addSubview(statusBarView)
            }
        }
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        cameraActionButton.isUserInteractionEnabled = true
    }
    
    func setThemeDetails(themeDetails: ThemeDetailData) {
        self.themeDetails = themeDetails
        self.containerView.theme = themeDetails.theme
        self.containerView.modeType = themeDetails.modeType
        self.headerTitleView.theme = themeDetails.theme
        self.headerTitleView.modeType = themeDetails.modeType
        self.cameraView.theme = themeDetails.theme
        self.cameraView.modeType = themeDetails.modeType
        self.footerView.theme = themeDetails.theme
        self.footerView.modeType = themeDetails.modeType
        self.cameraActionButton.theme = themeDetails.theme
        self.cameraActionButton.modeType = themeDetails.modeType
        self.galleryActionButton.theme = themeDetails.theme
        self.galleryActionButton.modeType = themeDetails.modeType
        self.bottomTitleLabel.theme = themeDetails.theme
        self.bottomTitleLabel.modeType = themeDetails.modeType
        self.headerTitle.theme = themeDetails.theme
        self.headerTitle.modeType = themeDetails.modeType
    }
    
    func setUpTopNavBar() {
        self.topNavigationBar.topNavigationInterstitialTitlColorAndFont(titleString: configData["subTitleImageCaptureScreen"] as? String ?? "")
        let tapRecognizerForLeftButton = UITapGestureRecognizer(target: self, action: #selector(leftBarButtonAction))
        self.topNavigationBar.leftBarButton.addGestureRecognizer(tapRecognizerForLeftButton)
    }
    
    @objc func leftBarButtonAction() {
        self.navigationController?.dismiss(animated: true)
    }
    
    func setUpAllViews() {
        setUpHeaderView()
        setUpFooterView()
        setUpBottomViewView()
        if let cameraFocusImage = UIImage(named: "cameraFocus") {
            self.cameraFocusView.imageView.image = cameraFocusImage
        } else {
            return
        }
    }
    
    func setUpHeaderView() {
        self.headerTitle.numberOfLines = 0
        self.headerTitle.lineBreakMode = .byWordWrapping
        self.headerTitle.textAlignment = .center
        self.headerTitle.setText(configData["titleImageCaptureScreen"] as? String, style: .bodyXS)
        self.headerTitleView.backgroundColor = themeDetails.scheme.colorGrey80
        headerTitle.textColor = themeDetails.scheme.colorBackground
    }
    
    func setUpFooterView() {
        self.footerView.backgroundColor = themeDetails.scheme.color20
        self.cameraActionButton.setIcon(JioImageAsset(named: JioIcon.Basic.icCamera.name))
        self.galleryActionButton.setIcon(JioImageAsset(named: JioIcon.Media.icPhotoMediaFiles.name))
        self.galleryActionButton.setKind(.tertiary)
        self.cameraActionButton.setKind(.primary)
        self.cameraActionButton.setState(.normal)
        
    }
    
    func setUpBottomViewView() {
        self.bottomTitleLabel.setText(configData["desctiptionImageCaptureScreen"] as? String, style: .bodyXS)
        self.bottomTitleLabel.textAlignment = .center
        self.bottomTitleView.backgroundColor = themeDetails.scheme.colorGrey80
        self.bottomTitleLabel.textColor = themeDetails.scheme.colorBackground
    }
    
    func opasityView() {
        let overlayView = UIView(frame: cameraView.bounds)
        overlayView.backgroundColor = UIColor.black.withAlphaComponent(0.8)
        cameraView.addSubview(overlayView)
        let maskLayer = CAShapeLayer()
        let path = UIBezierPath(rect: overlayView.bounds)
        path.append(UIBezierPath(rect: cameraFocusView.frame).reversing())
        maskLayer.path = path.cgPath
        overlayView.layer.mask = maskLayer
    }
    
    
    // Mark: Camera setUp
    func setUpCaptureSession() {
        captureSession.sessionPreset = AVCaptureSession.Preset.photo
    }
    
    func setUpDevice() {
        checkForCameraPermission(completion: { status in
            DispatchQueue.main.async { [weak self] in
                if status {
                    let deviceDiscoverySession = AVCaptureDevice.DiscoverySession(deviceTypes: [AVCaptureDevice.DeviceType.builtInWideAngleCamera], mediaType: AVMediaType.video, position: AVCaptureDevice.Position.unspecified)
                    let devices = deviceDiscoverySession.devices
                    
                    for device in devices {
                        if device.position == .back{
                            self?.backCamera = device
                        } else if device.position == .front {
                            self?.frontCamera = device
                        }
                    }
                    self?.currentCamera = self?.backCamera ?? self?.frontCamera
                    if self?.currentCamera == nil {
                        return
                    }
                    self?.setUpInputOutput()
                    self?.setUpPreviewLayer()
                    self?.startRunningCaptureSession()
                    self?.footerView.isUserInteractionEnabled = true
                    self?.mainStackView.isHidden = false
                } else {
                    // show alert
                    self?.showAlert()
                }
            }
        })
    }
    
    func showAlert() {
        mainStackView.isHidden = true
        let cancelTitle = configData["txtCancel"] as? String ?? "A"
        let settingButtonTitle = configData["txtSetting"] as? String ?? ""
        let proceedTitle = configData["txtDescryptionMessage"] as? String ?? ""
        allowPermissionView.isHidden = false
        allowPermissionView.backgroundColor = themeDetails.scheme.colorGrey80
        allowPermissionTitle.setText(cancelTitle, style: .headingXXS)
        allowPermissionTitle.textColor = themeDetails.scheme.colorBackground
        allowPermissionSubTitle.textColor = themeDetails.scheme.colorBackground
        allowPermissionTitle.textAlignment = .center
        allowPermissionSubTitle.setText(proceedTitle, style: .bodyXS)
        allowPermissionSubTitle.textAlignment = .center
        settingRedirectionButton.theme = themeDetails.theme
        settingRedirectionButton.addTarget(self, action: #selector(openDeviceSettings), for: .touchUpInside)
        settingRedirectionButton.setText(settingButtonTitle)
        footerView.isUserInteractionEnabled = false
    }
    
    @objc
    func openDeviceSettings() {
        guard let settingsUrl = URL(string: UIApplication.openSettingsURLString) else { return }
        if UIApplication.shared.canOpenURL(settingsUrl) {
            UIApplication.shared.open(settingsUrl, completionHandler: { (success) in
                print("Settings opened: \(success)")
            })
        }
    }
    
    func checkForCameraPermission(completion: @escaping (Bool) -> Void) {
        let status = AVCaptureDevice.authorizationStatus(for: .video)
        if status == .authorized {
            completion(true)
        } else if status == .notDetermined {
            AVCaptureDevice.requestAccess(for: .video) { status in
                completion(status)
            }
        } else {
            completion(false)
        }
    }
    
    func setUpInputOutput() {
        do {
            guard let camera = currentCamera else {
                return
            }
            let captureDeviceInput = try AVCaptureDeviceInput(device: camera)
            photoOutput = AVCapturePhotoOutput()
            captureSession.addInput(captureDeviceInput )
            let photoSettings = AVCapturePhotoSettings(format: [AVVideoCodecKey: AVVideoCodecType.jpeg])
            let preparedPhotoSettingsArray = [photoSettings]
            photoOutput?.setPreparedPhotoSettingsArray(preparedPhotoSettingsArray, completionHandler: nil)
            captureSession.addOutput(photoOutput!)
        } catch {
            print(error)
        }
    }
    
    func setUpPreviewLayer() {
        cameraPreviewLayer = AVCaptureVideoPreviewLayer(session: captureSession)
        cameraPreviewLayer?.videoGravity = AVLayerVideoGravity.resizeAspectFill
        cameraPreviewLayer?.connection?.videoOrientation = AVCaptureVideoOrientation.portrait
        cameraPreviewLayer?.frame = self.cameraView.bounds
        self.cameraView.layer.insertSublayer(cameraPreviewLayer!, at: 0)
    }
    
    func startRunningCaptureSession() {
        DispatchQueue.global(qos: .background).async {
            self.captureSession.startRunning()
        }
    }
    
    func capturePhoto() {
        let tapRecognizer = UITapGestureRecognizer(target: self, action: #selector(cameraClickAction))
        tapRecognizer.numberOfTapsRequired = 1
        tapRecognizer.numberOfTouchesRequired = 1
        self.cameraActionButton.addGestureRecognizer(tapRecognizer)
    }
    
    @objc func cameraClickAction() {
        cameraActionButton.isUserInteractionEnabled = false
        photoOutput?.capturePhoto(with: AVCapturePhotoSettings(), delegate: self)
    }
    
    // Mark: Image Picker
    func setUpGallery() {
        let tapRecognizer = UITapGestureRecognizer(target: self, action: #selector(showImagePicker))
        tapRecognizer.numberOfTapsRequired = 1
        tapRecognizer.numberOfTouchesRequired = 1
        self.galleryActionButton.addGestureRecognizer(tapRecognizer)
    }
    
    func imagePicker(sourceType: UIImagePickerController.SourceType) -> UIImagePickerController {
        let imagePicker = UIImagePickerController()
        imagePicker.sourceType = sourceType
        imagePicker.delegate = self
        return imagePicker
    }
    
    @objc func showImagePicker() {
        let libraryImagePicker = self.imagePicker(sourceType: .photoLibrary)
        self.present(libraryImagePicker, animated: true, completion: nil)
    }
}

extension JioCameraViewController: UIImagePickerControllerDelegate, UINavigationControllerDelegate {
    
    func imagePickerController(_ picker: UIImagePickerController, didFinishPickingMediaWithInfo info: [UIImagePickerController.InfoKey: Any]) {
        picker.dismiss(animated: true, completion: nil)
        print(info)
        
        if let image = info[UIImagePickerController.InfoKey.originalImage] as? UIImage {
            print(image)
            
            // Compress the image if needed
            if let compressedData = compressImage(image) {
                if compressedData.count > 3_000_000 {
                    let errorMessage = configData["errorMessage"] as? String
                    navigateToPreviewController(with: image, errorMessage: errorMessage)
                } else {
                    if let compressedImage = UIImage(data: compressedData) {
                        navigateToPreviewController(with: compressedImage, errorMessage: nil)
                    } else {
                        let errorMessage = configData["errorMessage"] as? String
                        navigateToPreviewController(with: image, errorMessage: errorMessage)
                    }
                }
            } else {
                let errorMessage = configData["errorMessage"] as? String
                navigateToPreviewController(with: image, errorMessage: errorMessage)
            }
        }
    }
}
extension JioCameraViewController: AVCapturePhotoCaptureDelegate {
    func photoOutput(_ output: AVCapturePhotoOutput, didFinishProcessingPhoto photo: AVCapturePhoto, error: Error?) {
        guard error == nil else {
            print("Error capturing photo: \(error!.localizedDescription)")
            return
        }
        
        guard let imageData = photo.fileDataRepresentation() else {
            print("Failed to get image data")
            return
        }
        // Check if the image size is greater than 3MB (3,000,000 bytes)
        if imageData.count > 3_000_000 {
            if let compressedData = compressImage(UIImage(data: imageData)) {
                if compressedData.count > 3_000_000 {
                    let errorMessage = configData["errorMessage"] as? String
                    navigateToPreviewController(with: nil, errorMessage: errorMessage)
                } else {
                    if let compressedImage = UIImage(data: compressedData) {
                        navigateToPreviewController(with: compressedImage, errorMessage: nil)
                    } else {
                        let errorMessage = configData["errorMessage"] as? String
                        navigateToPreviewController(with: nil, errorMessage: errorMessage)
                    }
                }
            } else {
                let errorMessage = configData["errorMessage"] as? String
                navigateToPreviewController(with: nil, errorMessage: errorMessage)
            }
        } else {
            if let originalImage = UIImage(data: imageData) {
                navigateToPreviewController(with: originalImage, errorMessage: nil)
            }
        }
    }

    func navigateToPreviewController(with image: UIImage?, errorMessage: String?) {
        guard let controller = UIStoryboard(name: "JioCamera", bundle: JioCameraSDKModel.shared.bundle).instantiateViewController(identifier: "JioPreviewImageViewController") as? JioPreviewImageViewController else { return }
        controller.image = image
        controller.themeDetails = self.themeDetails
        controller.errorMessage = errorMessage
        self.navigationController?.pushViewController(controller, animated: true)
    }
    
    // Function to compress image data
    func compressImage(_ image: UIImage?) -> Data? {
        guard let image = image else { return nil }
        let desiredFileSize: Int = 3_000_000
        var compressionQuality: CGFloat = 1.0
        var compressedImageData: Data? = image.jpegData(compressionQuality: compressionQuality)
        
        while let data = compressedImageData, data.count > desiredFileSize, compressionQuality > 0.1 {
            compressionQuality -= 0.1
            compressedImageData = image.jpegData(compressionQuality: compressionQuality)
        }
        
        return compressedImageData
    }
}
