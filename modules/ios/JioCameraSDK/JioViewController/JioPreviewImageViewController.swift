//
//  JioPreviewImageViewController.swift
//
//

import UIKit
import JioDesignSystem

class JioPreviewImageViewController: UIViewController {

    @IBOutlet weak var containerView: JioView!
    
    @IBOutlet var mainView: JioView!
    @IBOutlet weak var topNavigationBarView: JioTopNavigationBarView!
    @IBOutlet weak var bottomLeftButton: JioButton!
    @IBOutlet weak var imageView: UIImageView!
    @IBOutlet weak var bottomImageView: JioView!
    @IBOutlet weak var bottomRightButton: JioButton!
    @IBOutlet weak var headerTitleLabel: JioLabel!
    @IBOutlet weak var headerTitleView: JioView!

    var image: UIImage?
    var themeDetails: ThemeDetailData?
    var errorMessage: String?
    var configData = JioCameraSDKModel.shared.configData
    
    override func viewDidLoad() {
        super.viewDidLoad()
        navigationController?.setNavigationBarHidden(true, animated: true)
        self.mainView.backgroundColor = themeDetails?.scheme.color20
        imageView.image = image
        imageView.contentMode = .scaleAspectFill
        setUpViews()
        setUpTheme(themeDetails: themeDetails)
    }
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        if let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene {
            if let statusBarManager = windowScene.statusBarManager {
                let statusBarFrame = statusBarManager.statusBarFrame
                let statusBarView = UIView(frame: statusBarFrame)
                statusBarView.backgroundColor = themeDetails?.scheme.color20
                view.addSubview(statusBarView)
            }
        }
    }

    override func viewWillDisappear(_ animated: Bool) {
        super.viewWillDisappear(animated)
        view.subviews.forEach { subview in
            if subview.backgroundColor == themeDetails?.scheme.color20 {
                subview.removeFromSuperview()
            }
        }
    }

    init(themeDetails: ThemeDetailData?) {
        self.themeDetails = themeDetails
        super.init(nibName: nil, bundle: nil)
    }
    
    required init?(coder aDecoder: NSCoder) {
        super.init(coder: aDecoder)
    }
    
    func setUpTheme(themeDetails: ThemeDetailData?) {
        self.themeDetails = themeDetails
        guard let themeDetails = themeDetails else { return }
        self.containerView.theme = themeDetails.theme
        self.containerView.modeType = themeDetails.modeType
        self.headerTitleLabel.theme = themeDetails.theme
        self.headerTitleLabel.modeType = themeDetails.modeType
        self.headerTitleView.theme = themeDetails.theme
        self.headerTitleView.modeType = themeDetails.modeType
        self.bottomImageView.theme = themeDetails.theme
        self.bottomImageView.modeType = themeDetails.modeType
    }
    
    func setUpViews() {
        self.containerView.backgroundColor = themeDetails?.scheme.colorBackground
        self.imageView.layer.cornerRadius = 24.0
        self.imageView.layer.masksToBounds = true
        setUpHeaderView()
        setUpBottomView()
        setUpTopNavBar()
    }
    
    func setUpTopNavBar() {
        self.topNavigationBarView.topNavigationInterstitialTitlColorAndFont(titleString: configData["subTitleReviewScreen"] as? String ?? "")
        let tapRecognizerForLeftButton = UITapGestureRecognizer(target: self, action: #selector(leftBarButtonAction))
        self.topNavigationBarView.leftBarButton.addGestureRecognizer(tapRecognizerForLeftButton)
    }
    
    @objc func leftBarButtonAction() {
        self.navigationController?.popViewController(animated: true)
    }

    func setUpHeaderView() {
        self.headerTitleLabel.setText(configData["desctiptionReviewScreen"] as? String, style: .bodyXS)
        self.headerTitleLabel.numberOfLines = 0
        self.headerTitleLabel.lineBreakMode = .byWordWrapping
        self.headerTitleLabel.textAlignment = .center
        self.headerTitleLabel.textColor = themeDetails?.scheme.colorBackground
    }
    
    func setUpBottomView() {
        self.bottomImageView.backgroundColor = themeDetails?.scheme.color20
        let leftImageAsset = JioImageAsset(named: JioIcon.Basic.icClose.name, bundle: nil)
        bottomLeftButton.setIcon(leftImageAsset)
        bottomLeftButton.setKind(.secondary)
        
        let rightImageAsset = JioImageAsset(named: JioIcon.Basic.icConfirm.name, bundle: nil)
        bottomRightButton.setIcon(rightImageAsset)
        bottomRightButton.setKind(.secondary)
        
        let tapRecognizerForLeftButton = UITapGestureRecognizer(target: self, action: #selector(bottomLeftButtonTapped))
        self.bottomLeftButton.addGestureRecognizer(tapRecognizerForLeftButton)
        let tapRecognizerForRightButton = UITapGestureRecognizer(target: self, action: #selector(bottomRightButtonTapped))
        self.bottomRightButton.addGestureRecognizer(tapRecognizerForRightButton)
        
    }
    
    @objc func bottomLeftButtonTapped() {
        navigationController?.popViewController(animated: true)
    }
    
    @objc func bottomRightButtonTapped() {
        if let selectedImage = image {
            if errorMessage == nil {
                // Convert the image to base64
                if let imageData = selectedImage.jpegData(compressionQuality: 1.0) {
                    // Compress the image data
                    if let compressedData = compressImage(imageData) {
                        let base64String = compressedData.base64EncodedString()
                        JioCameraSDKModel.shared.selectedImageCompletion?(selectedImage, base64String)
                        self.navigationController?.dismiss(animated: true)
                    } else {
                        print("Failed to compress image data")
                    }
                } else {
                    // Handle failure to get image data
                    print("Failed to get image data")
                }
            } else {
                showToast(message: errorMessage ?? "")
                DispatchQueue.main.asyncAfter(deadline: .now() + 3.0) {
                    self.navigationController?.dismiss(animated: true)
                }
            }
        }
    }

    
    func compressImage(_ imageData: Data) -> Data? {
        let desiredFileSize: Int = 3_000_000
        var compressionQuality: CGFloat = 1.0
        var compressedImage: Data? = imageData
        while let compressed = compressedImage, compressed.count > desiredFileSize {
            guard compressionQuality > 0.1 else {
                return nil
            }
            guard let image = UIImage(data: compressed) else {
                return nil
            }
            guard let newImageData = image.jpegData(compressionQuality: compressionQuality) else {
                return nil
            }
            compressedImage = newImageData
            compressionQuality -= 0.1
        }
        return compressedImage
    }


    
    func showToast(title: String = "", message: String, showClose: Bool = true, toastExtraBottomPadding: CGFloat = 0) {
        let notificationProps = NotificationProps(
            title: title,
            description: message,
            showClose: showClose
        )
        _ = ToastService.errorToast(notificationProps: notificationProps)
    }
}
