//
//  JioTopNavigationBarView.swift
//  JioCameraSDK
//
//

import UIKit
import JioDesignSystem

class JioTopNavigationBarView: JioView {

    @IBOutlet weak var contentView: JioView!
    @IBOutlet weak var rightIconView: JioButton!
    @IBOutlet weak var topBarTitle: JioLabel!
    @IBOutlet weak var leftBarButton: JioIconView!
    @IBOutlet weak var leftButtonView: JioView!

    var themeDetails = JioCameraSDKModel.shared.themeDetail
    var configData = JioCameraSDKModel.shared.configData

    override init(frame: CGRect) {
        super.init(frame: frame)
        initView()
    }

    required init?(coder aDecoder: NSCoder) {
        super.init(coder: aDecoder)
        initView()
    }

    private func initView() {
        let nib = UINib(nibName: "JioTopNavigationBarView", bundle: JioCameraSDKModel.shared.bundle)
        nib.instantiate(withOwner: self, options: nil)
        contentView.fixInView(self)
    }

    func setThemeDetails(themeDetails: ThemeDetailData) {
        self.themeDetails = themeDetails
        self.contentView.theme = themeDetails.theme
        self.contentView.modeType = themeDetails.modeType
        self.rightIconView.theme = themeDetails.theme
        self.rightIconView.modeType = themeDetails.modeType
        self.topBarTitle.theme = themeDetails.theme
        self.topBarTitle.modeType = themeDetails.modeType
        self.leftBarButton.theme = themeDetails.theme
        self.leftBarButton.modeType = themeDetails.modeType
        self.leftButtonView.theme = themeDetails.theme
        self.leftButtonView.modeType = themeDetails.modeType
    }
    
    @objc
    func topNavigationInterstitialTitlColorAndFont(titleString: String) {
        self.contentView.backgroundColor = themeDetails.scheme.color20
        self.leftBarButton.image = JioIcon.Basic.icBack.image
        self.leftBarButton.color = .secondaryInverse
        self.leftBarButton.kind = .iconOnly
        self.topBarTitle.numberOfLines = 0
        self.topBarTitle.lineBreakMode = .byTruncatingTail
        self.topBarTitle.setText(titleString, style: .bodyS)
        self.topBarTitle.textColor = themeDetails.scheme.colorInverse
        self.rightIconView.isHidden = true
        self.rightIconView.setIcon(JioImageAsset(named: JioIcon.Basic.icFlipCamera.name, bundle: nil))
    }
}

extension JioView {
    func fixInView(_ container: JioView!) {
            self.translatesAutoresizingMaskIntoConstraints = false
            self.frame = container.frame
            container.addSubview(self)
            NSLayoutConstraint(item: self, attribute: .leading, relatedBy: .equal, toItem: container, attribute: .leading, multiplier: 1.0, constant: 0).isActive = true
            NSLayoutConstraint(item: self, attribute: .trailing, relatedBy: .equal, toItem: container, attribute: .trailing, multiplier: 1.0, constant: 0).isActive = true
            NSLayoutConstraint(item: self, attribute: .top, relatedBy: .equal, toItem: container, attribute: .top, multiplier: 1.0, constant: 0).isActive = true
            NSLayoutConstraint(item: self, attribute: .bottom, relatedBy: .equal, toItem: container, attribute: .bottom, multiplier: 1.0, constant: 0).isActive = true
        }
}
