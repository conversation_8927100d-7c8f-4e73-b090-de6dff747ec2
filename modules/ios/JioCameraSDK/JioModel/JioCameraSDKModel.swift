//
//  JioCameraViewModel.swift
//  JioCameraSDK
//
//  Created by <PERSON><PERSON> on 23/04/24.
//

import UIKit
import JioDesignSystem

public class JioCameraSDKModel: NSObject {
    
    public static let shared = JioCameraSDKModel()
    var selectedImageCompletion: ((_ selectedImage: UIImage?, _ base64String: String?) -> Void )?
    private override init() {}
    var bundle: Bundle?
    
    public var themeDetail : ThemeDetailData = (JioDesign.Color.globalTheme, .light, JioDesign.Color.globalTheme.light.primary)
    public var channelId = ""
    public var configData: [String: Any] = [:]
    
    public func initialiseTheSDK(themeDetail: ThemeDetailData, channelId: String, configData:[String:Any] ) {
        self.themeDetail = themeDetail
        self.channelId = channelId
        self.configData = configData
        let bundle = Bundle(for: Self.self)
        if let resourceBundleURL = bundle.url(forResource: "JHBundle", withExtension: "bundle") {
            self.bundle = Bundle(url: resourceBundleURL)
        }
    }
    
    public func openCameraControllerFromController(controller: UIViewController, completion: @escaping (UIImage?, String?) -> Void) {
        selectedImageCompletion = completion
        let cameraController = UIStoryboard(name: "JioCamera", bundle: self.bundle).instantiateViewController(identifier: "JioCameraViewController")
        let navigationController = UINavigationController(rootViewController: cameraController)
        navigationController.modalPresentationStyle = .overFullScreen
        controller.present(navigationController, animated: true)
    }
}
