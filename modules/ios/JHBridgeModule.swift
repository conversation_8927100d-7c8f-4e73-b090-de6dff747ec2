import JioDesignSystem
import UIKit
// import SVGKit
import IQKeyboardManagerSwift
import LocalAuthentication
import Foundation

@objc(JHBridgeModule)
class JHBridgeModule: NSObject {
    let commonUtil = JHCommonExtension()
    override init() {
        super.init()
    }
    

    @objc(openNativeCamera:withResolver:withRejecter:)
    func openNativeCamera(jsonData: String, resolve: @escaping RCTPromiseResolveBlock, reject: @escaping RCTPromiseRejectBlock) -> Void {
        // Open camera
        NSLog("Function called swift");
        DispatchQueue.main.async {
            guard let currentViewController = UIApplication.shared.keyWindow?.rootViewController else {
                reject("Error", "Failed to access current view controller", nil)
                return
            }
            if let data = jsonData.data(using: .utf8) {
                if let jsonData = try? JSONSerialization.jsonObject(with: data, options : .allowFragments) as? [String: Any] {
                    let theme = JioTheme(primary: .warmGold, secondary: .orangeMidnight, sparkle: .skyMidnight)
                    let themeObject: ThemeDetailData = (theme, .light, theme.light.primary)
                    JioCameraSDKModel.shared.initialiseTheSDK(themeDetail: themeObject, channelId: "", configData: jsonData)
                    JioCameraSDKModel.shared.openCameraControllerFromController(controller: currentViewController) { _, base64String in
                        if let base64String = base64String {
                            resolve(base64String)
                        } else {
                            reject("Error", "Either image or base64String (or both) is nil", NSError(domain: "error", code: 0, userInfo: nil))
                        }
                    }
                }
            }
        }
    }
    
    @objc(convertSvgToPng:withResolver:withRejecter:)
    func convertSvgToPng(data: Any, resolve: @escaping RCTPromiseResolveBlock, reject: @escaping RCTPromiseRejectBlock) {
    //     guard let svgString = data as? String else {
    //         reject("SVG_ERROR", "Invalid SVG data", nil)
    //         return
    //     }
    //     if let imageValue = SVGKImage(data: svgString.data(using: .utf8)), let finalImage = imageValue.uiImage {
    //         let data = finalImage.pngData()
    //         let base64String = data?.base64EncodedString()
    //         resolve(base64String)
    //     } else {
    //         reject("SVG_ERROR", "Invalid SVG data", nil)
    //     }
    }
    
    fileprivate func setUpDateInDatePickerController(_ self: JHBridgeModule,_ params: [String: Any], _ datePickerViewController: JHDatePickerViewController, reject: @escaping RCTPromiseRejectBlock) {
        if let maxDateString = params["maxDate"] as? String {
            guard let maxDate = commonUtil.getDateFromString(for: maxDateString) else {
                reject("INVALID_MAX_DATE", "Max date format is invalid", nil)
                return
            }
            datePickerViewController.maxDate = maxDate
        }
        if let minDateString = params["minDate"] as? String {
            guard let minDate = commonUtil.getDateFromString(for: minDateString) else {
                reject("INVALID_MIN_DATE", "Min date format is invalid", nil)
                return
            }
            datePickerViewController.minDate = minDate
        }
        if let selectedDateString = params["selectedDate"] as? String {
            guard let selectedDate = commonUtil.getDateFromString(for: selectedDateString) else {
                reject("INVALID_SELECTED_DATE", "Selected date format is invalid", nil)
                return
            }
            datePickerViewController.selectedDate = selectedDate
        }
        let disableWeekends = params["disableWeekends"] as? Bool ?? false
        datePickerViewController.disableWeekends = disableWeekends
    }
    
    fileprivate func setUpDatePickerClickActions(_ datePickerViewController: JHDatePickerViewController, resolve: @escaping RCTPromiseResolveBlock, reject: @escaping RCTPromiseRejectBlock) {
        datePickerViewController.onDateSelected = { selectedDate in
            let calendar = Calendar.current
            let selectedDateDict: [String: Any] = [
                "selectedYear": calendar.component(.year, from: selectedDate),
                "selectedMonth": calendar.component(.month, from: selectedDate),
                "selectedDay": calendar.component(.day, from: selectedDate)
            ]
            resolve(selectedDateDict)
        }
        datePickerViewController.onClose = {
            reject("E_CANCELLED", "User cancelled date selection", nil)
        }
    }
    
    @objc(showDatePicker:withResolver:withRejecter:)
    func showDatePicker(params: Any, resolve: @escaping RCTPromiseResolveBlock, reject: @escaping RCTPromiseRejectBlock) {
        let params = commonUtil.convertAnyToJSON(value: params as? String ?? "") ?? [:]
        DispatchQueue.main.async { [weak self] in
            guard let self = self else { return }
            guard let currentViewController = UIApplication.shared.keyWindow?.rootViewController else {
                reject("Error", "Failed to access current view controller", nil)
                return
            }
            let bundle = Bundle(for: Self.self)
            var sdkBundle: Bundle? = nil
            if let resourceBundleURL = bundle.url(forResource: "JHBundle", withExtension: "bundle") {
                sdkBundle = Bundle(url: resourceBundleURL)
            }
            guard let datePickerViewController = UIStoryboard(name: "JioCamera", bundle: sdkBundle).instantiateViewController(identifier: "JHDatePickerViewController") as? JHDatePickerViewController else { return }
            setUpDateInDatePickerController(self, params, datePickerViewController, reject: reject)
            setUpDatePickerClickActions(datePickerViewController, resolve: resolve, reject: reject)
            if let presentedViewController = currentViewController.presentedViewController {
                // Embed date picker in the existing modal's view
                self.embedDatePickerViewController(datePickerViewController, in: presentedViewController)
            } else {
                // Present the date picker normally
                datePickerViewController.modalPresentationStyle = .overCurrentContext
                currentViewController.present(datePickerViewController, animated: true, completion: nil)
            }
        }
    }

    private func embedDatePickerViewController(_ datePickerViewController: JHDatePickerViewController, in parentViewController: UIViewController) {
        datePickerViewController.currentRNController = parentViewController
        parentViewController.addChild(datePickerViewController)
        datePickerViewController.view.frame = parentViewController.view.bounds
        parentViewController.view.addSubview(datePickerViewController.view)
        datePickerViewController.didMove(toParent: parentViewController)
    }
    @objc
        func keyboardResize(_ flag: Any) {
            DispatchQueue.main.async {
                IQKeyboardManager.shared.isEnabled = flag as? Bool ?? false
            }
        }
    
  @objc
  public func isBiometricAvailable(_ resolve: @escaping RCTPromiseResolveBlock, reject: @escaping RCTPromiseRejectBlock) {
    let context = LAContext()
    var error: NSError?
      
    if context.canEvaluatePolicy(.deviceOwnerAuthenticationWithBiometrics, error: &error) {
      resolve([
        "available": "true",
        "error": ""
      ]);
    } else {
      if let err = error {
        resolve(
            [
              "available": "false",
              "error": err.localizedDescription
            ]
        )
      } else {
        resolve(
            [
              "available": "false",
              "error": "Biometric authentication not available"
            ]
        )
      }
    }
  }
    
    @objc
    public func getBiometricType(_ resolve: @escaping RCTPromiseResolveBlock, reject: @escaping RCTPromiseRejectBlock) {
      var returnString = "Not available"
      if #available(iOS 11.0, *) {
        let context = LAContext()
        var error: NSError?

        if context.canEvaluatePolicy(.deviceOwnerAuthenticationWithBiometrics, error: &error) {
          let biometryType = context.biometryType
          switch biometryType {
          case .faceID:
            returnString = "Face ID"
          case .touchID:
            returnString = "Touch ID"
          case .none:
            returnString = "Not available"
          case .opticID:
            returnString = "Optic ID"
          @unknown default:
            returnString = "Unknown"
          }
          resolve(returnString)
        } else {
          resolve("Biometric authentication not available")
        }
      } else {
        resolve("Not available on iOS versions below 11.0")
      }
    }
    
    @objc
    public func authenticateUser(_ resolve: @escaping RCTPromiseResolveBlock, reject: @escaping RCTPromiseRejectBlock) {
      let context = LAContext()
        
      //var error: NSError?

      let reason = "Unlock Jio Health Hub"
      context.evaluatePolicy(.deviceOwnerAuthenticationWithBiometrics, localizedReason: reason) { success, evaluateError in
        DispatchQueue.main.async {
          if success {
            resolve(
                [
                  "success": true,
                  "error": ""
                ]
            )
          } else {
            if let evalError = evaluateError {
              resolve(
                [
                  "success": false,
                  "error": evalError.localizedDescription
                ]
              )
            } else {
              resolve(
                [
                  "success": false,
                  "error": "Authentication failed"
                ]
              )
            }
          }
        }
      }
    }
    
    
}
