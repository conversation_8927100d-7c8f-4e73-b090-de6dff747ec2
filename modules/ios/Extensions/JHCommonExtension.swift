//
//  JHCommonExtension.swift
//  jio-black-rock
//
//

import Foundation

public class JHCommonExtension {
    func convertAnyToJSON(value: String) -> [String: Any]? {
        guard let data = value.data(using: .utf8) else { return nil }
        do {
            if let json = try JSONSerialization.jsonObject(with: data, options: .allowFragments) as? [String: Any] {
                return json
            }
        } catch let error as NSError {
            print(error)
        }
        return nil
    }
    
    func getDateFromString(withFormat: String = "dd/MM/yyyy", for value: String) -> Date? {
        let dateFormatter = DateFormatter.defaultLocale
        dateFormatter.dateFormat = withFormat
        let date = dateFormatter.date(from: value)
        return date
    }
}

extension DateFormatter {
    static var defaultLocale: DateFormatter {
        let formatter = DateFormatter()
        formatter.locale = Locale(identifier: "en_US_POSIX")
        return formatter
    }
}
