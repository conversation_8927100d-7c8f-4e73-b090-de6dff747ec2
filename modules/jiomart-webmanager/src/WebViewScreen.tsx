import React, {useCallback} from 'react';
import WebViewUtility from './WebViewUtility';
import useWebViewController from './useWebViewController';
import ScreenSlot, {
  DeeplinkHandler,
} from '../../jiomart-general/src/ui/JMScreenSlot';
import {
  AppScreens,
  type ScreenProps,
} from '@jm/jiomart-common/src/JMAppScreenEntry';
import {getBaseURL} from '../../jiomart-networkmanager/src/JMEnvironmentConfig';
import GlobalBottomSheet from '../../jiomart-main/src/features/StartUp/GlobalBottomSheet';
import {useFocusEffect} from '@react-navigation/native';
import useGlobalBottomSheetController from '../../jiomart-main/src/features/StartUp/controllers/useGlobalBottomSheetController';

export type JProps = ScreenProps<typeof AppScreens.COMMON_WEB_VIEW>;

const CommonWebViewScreen = (props: JProps) => {
  const {
    navigation,
    navigationBean,
    webState,
    webViewRef,
    handleJSEvents,
    onUrlOverride,
    onError,
    handleBackPress,
    handleNavigationStateChange,
  } = useWebViewController(props);
  const {checkAndOpenNextSheet} = useGlobalBottomSheetController();

  const webViewMainUi = () => {
    console.log('webState.content', webState.content);
    return (
      <WebViewUtility
        source={{uri: webState.content || getBaseURL()}}
        onMessage={handleJSEvents}
        onRef={ref => {
          webViewRef.current = ref;
        }}
        onError={onError}
        javaScriptEnabled={true}
        onNavigationStateChange={handleNavigationStateChange}
        onShouldStartLoadWithRequest={onUrlOverride}
      />
    );
  };

  const isHomeUrl = (url: string) => {
    const base = getBaseURL();

    const normalizedUrl = url.split('?')[0].replace(/\/+$/, '');
    const normalizedBase = base.replace(/\/+$/, '');

    return normalizedUrl === normalizedBase;
  };

  useFocusEffect(
    useCallback(() => {
      if (isHomeUrl(webState.content ?? '')) {
        checkAndOpenNextSheet();
      }
    }, []),
  );

  return (
    <DeeplinkHandler
      navigationBean={navigationBean}
      navigation={navigation}
      children={bean => (
        <ScreenSlot
          navigationBean={navigationBean}
          navigation={navigation}
          onCustomBackPress={handleBackPress}
          bottomSheetContent={<GlobalBottomSheet />}
          children={_ => {
            return webViewMainUi();
          }}
        />
      )}
    />
  );
};

export default CommonWebViewScreen;
