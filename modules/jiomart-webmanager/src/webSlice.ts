import {PayloadAction, createSlice} from '@reduxjs/toolkit';
import {resetStore} from '../../jiomart-main/src/store/JMGlobalReduxAction';

export interface WebCallBack {
  identifier: string | undefined;
  event: string | undefined;
}

export interface WebState {
  WEB_CALLBACK: WebCallBack | null;
}

const initialState: WebState = {
  WEB_CALLBACK: null,
};

const webSlice = createSlice({
  name: 'web',
  initialState,
  extraReducers: builder => builder.addCase(resetStore, () => initialState),
  reducers: {
    setWebCallback: (state, action: PayloadAction<WebCallBack | null>) => {
      state.WEB_CALLBACK = action.payload;
    },
  },
});

export const {setWebCallback} = webSlice.actions;
export default webSlice.reducer;
