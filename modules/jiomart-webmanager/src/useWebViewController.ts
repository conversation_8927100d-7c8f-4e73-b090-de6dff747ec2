import {useEffect, useRef, useState} from 'react';
import {BackHandler, Platform} from 'react-native';
import WebView, {
  WebViewMessageEvent,
  WebViewNavigation,
} from 'react-native-webview';
import {base64Decode} from '../../jiomart-common/src/Helper';
import {
  ActionType,
  navBeanObj,
  NavigationBean,
  NavigationType,
} from '../../jiomart-common/src/JMNavGraphUtil';
import {EventEmitterKeys} from '../../jiomart-common/src/JMConstants';
import {JMSharedViewModel} from '../../jiomart-common/src/JMSharedViewModel';
import {isNullOrUndefinedOrEmpty} from '../../jiomart-common/src/JMObjectUtility';
import {JMLogger} from '../../jiomart-common/src/utils/JMLogger';
import {
  navigateTo,
  openLogin,
} from '../../jiomart-general/src/navigation/JMNavGraph';
import {useGlobalState} from '../../jiomart-general/src/utils/JMGlobalStateProvider';
import {addQueryParamsToURL} from './util/JHWebAppUtil';
import {WebViewEventConstant} from './WebEventConstants';
import {JProps} from './WebViewScreen';
import {getBaseURL} from '../../jiomart-networkmanager/src/JMEnvironmentConfig';
import {HeaderType} from '../../jiomart-common/src/JMScreenSlot.types';
import {AppScreens} from '@jm/jiomart-common/src/JMAppScreenEntry';

interface PageParams {
  slug?: string[];
  group?: string[];
}

interface PageQuery {
  url?: string[];
  dept?: string[];
  l2?: string[];
  l3_category_names?: string[];
  tab?: string[];
  [key: string]: any;
}

interface OpenActionPage {
  type: string;
  params: PageParams;
  query: PageQuery;
}

interface OpenActionValue {
  page: OpenActionPage;
}

const useWebViewController = (props: JProps) => {
  const {route, navigation} = props;
  const navigationBean = route.params;
  const webViewRef = useRef<WebView | null>(null);
  const [bean, setBean] = useState<NavigationBean>(navigationBean);
  const [webState, setWebState] = useState<WebViewState>(setInitialState());
  const [webCanGoBack, setWebCanGoBack] = useState(false);
  const {event, setEvent} = useGlobalState();

  useEffect(() => {
    if (navigationBean) {
      setBean(navigationBean);
    }
  }, [navigationBean]);

  const enum OpenActionEventConstant {
    COLLECTION = 'collection',
    PRODUCTS = 'products',
    PRODUCT = 'product',
    SECTIONS = 'sections',
    CUSTOM = 'custom',
    ORDERS = 'orders',
    HOME = 'home',
    BRAND = 'brand',
  }

  useEffect(() => {
    if (!isNullOrUndefinedOrEmpty(event)) {
      const key = Object.keys(event)[0];
      switch (key) {
        case EventEmitterKeys.WEB_VIEW_EVENT_EMITT:
          const value = event[key];
          handleWebViewEvent(value);
          setEvent([]);
          break;

        default:
          break;
      }
    }
  }, [event]);

  const handleWebViewEvent = (data: {[key: string]: any}) => {
    Object.entries(data).forEach(([key, value]) => {
      try {
        const parsedValue =
          typeof value === 'string' ? JSON.parse(value) : value;
        const eventFunctionCall = `(function(param) {
        try {
          if (typeof ${key} === 'function') {
            ${key}(param);
          } else {
            console.error('Function ${key} not found in WebView');
          }
        } catch (e) {
          console.error('WebView execution error:', e);
        }
      })(${JSON.stringify(parsedValue)})`;

        if (webViewRef?.current?.injectJavaScript) {
          webViewRef.current.injectJavaScript(eventFunctionCall);
        } else {
          JMLogger.log('WebView reference not available');
        }
      } catch (error) {}
    });
  };

  function setInitialState(): WebViewState {
    if (bean) {
      switch (bean.actionType) {
        case ActionType.OPEN_WEB_HTML:
          return {state: WebStateType.HTML, content: addQueryParamsToURL(bean)};
        case ActionType.OPEN_WEB_URL:
          return {
            state: WebStateType.URL,
            content: addQueryParamsToURL(bean),
          };
        case ActionType.OPEN_WEB_URL_WITH_TOKEN: {
          return {state: WebStateType.LOADING};
        }
      }
    }
    return {state: WebStateType.ERROR};
  }

  const handleNavigationStateChange = (navState: WebViewNavigation) => {
    const {url, canGoBack} = navState;

    // if (isNullOrUndefinedOrEmpty(mainWebUrl)) {
    //   setMainWebUrl(url);
    // }
    // setCurrentWebUrl(url);
    setWebCanGoBack(canGoBack);
  };

  const presentHeaderHandler = (headerType: number) => {
    try {
      setBean(prev => {
        return {
          ...prev,
          headerType,
        };
      });
    } catch (error) {}
  };

  const setMenuTitle = (navTitle: string) => {
    try {
      setBean(prev => {
        return {
          ...prev,
          navTitle,
        };
      });
    } catch (error) {}
  };

  const setShouldShowDeliverToBar = (val: boolean) => {
    try {
      setBean(prev => {
        return {
          ...prev,
          shouldShowDeliverToBar: val,
        };
      });
    } catch (error) {}
  };

  const handleBackPress = () => {
    // if (JMSharedViewModel.Instance.previousPageURL !== '') {
    //   console.log(
    //     'back pressed url -- ',
    //     JMSharedViewModel.Instance.previousPageURL,
    //   );
    //   JMSharedViewModel.Instance.setPreviousPageURL('');
    // } else
    if (webCanGoBack) {
      webViewRef.current?.goBack();
      JMSharedViewModel.Instance.setPreviousPageURL('');
    } else if (navigation.canGoBack()) {
      navigation.goBack();
    } else {
      BackHandler.exitApp();
    }
  };

  const navigateToWebView = (url: string) => {
    navigateTo(
      navBeanObj({
        actionType: ActionType.OPEN_WEB_URL,
        destination: AppScreens.COMMON_WEB_VIEW,
        headerVisibility: HeaderType.CUSTOM,
        navigationType: NavigationType.PUSH,
        loginRequired: false,
        actionUrl: url,
        headerType: 9,
      }),
      navigation,
    );
  };

  const buildURLWithQueryParams = (
    basePath: string,
    query: PageQuery,
  ): string => {
    const baseURL = String(getBaseURL());
    let url = `${baseURL}/${basePath}`;
    if (Object.keys(query).length > 0) {
      url += '?';
      for (const key in query) {
        if (Array.isArray(query[key])) {
          url += `${key}=${query[key][0]}&`;
        }
      }
    }
    return url;
  };

  const handleCollectionPage = (slug: string, query: PageQuery) => {
    const baseURL = String(getBaseURL());
    let pageURL = `${baseURL}/collection/${slug}`;

    if (query.l3_category_names?.length) {
      pageURL += '?';
      query.l3_category_names.forEach((category: string) => {
        pageURL += `l3_category_names=${category}&`;
      });
    }

    navigateToWebView(pageURL);
  };

  const handleProductsPage = (slug: string, query: PageQuery) => {
    const pageURL = buildURLWithQueryParams('products', query);
    navigateToWebView(pageURL);
  };

  const handleProductPage = (slug: string) => {
    const baseURL = String(getBaseURL());
    const productPageURL = `${baseURL}/product/${slug}`;
    navigateToWebView(productPageURL);
  };

  const handleSectionsPage = (slug: string) => {
    const baseURL = String(getBaseURL());
    const pageURL = `${baseURL}/sections/${slug}`;
    navigateToWebView(pageURL);
  };

  const handleCustomPage = (query: PageQuery) => {
    const baseURL = String(getBaseURL());
    let pageURL = baseURL;
    if (query.url) {
      pageURL += query.url[0];
      if (!pageURL.includes('/collection/')) {
        navigateToWebView(pageURL);
        return;
      }
    }

    const urlParts = query.url?.[0].split('/') || [];
    const slug = urlParts[urlParts.length - 1];
    const newQuery = {
      department: query.dept?.[0],
      category: query.l2?.[0],
      type: 'products',
    };

    handleProductsPage(slug, newQuery);
  };

  const handleHomePage = (query: PageQuery) => {
    if (query.tab?.[0] === 'fashion') {
      const baseURL = String(getBaseURL());
      const pageURL = `${baseURL}/?tab=${query.tab[0]}`;
      navigateToWebView(pageURL);
    } else {
      // navigation.navigate(bottomTabRoute.Home);
    }
  };

  const handleBrandPage = (slug: string) => {
    const baseURL = String(getBaseURL());
    const pageURL = `${baseURL}/brand/${slug}`;
    navigateToWebView(pageURL);
  };

  const handleOpenActionEvent = (openActionValue: OpenActionValue) => {
    const {page} = openActionValue;
    const slug = page.params.slug?.[0] || page.params.group?.[0] || '';

    switch (page.type) {
      case OpenActionEventConstant.COLLECTION:
        handleCollectionPage(slug, page.query);
        break;
      case OpenActionEventConstant.PRODUCTS:
        handleProductsPage(slug, page.query);
        break;
      case OpenActionEventConstant.PRODUCT:
        handleProductPage(slug);
        break;
      case OpenActionEventConstant.SECTIONS:
        handleSectionsPage(slug);
        break;
      case OpenActionEventConstant.CUSTOM:
        handleCustomPage(page.query);
        break;
      case OpenActionEventConstant.HOME:
        handleHomePage(page.query);
        break;
      case OpenActionEventConstant.ORDERS:
        navigation.popToTop();
        break;
      case OpenActionEventConstant.BRAND:
        handleBrandPage(slug);
        break;
    }
  };

  const handleJSEvents = async (e: WebViewMessageEvent) => {
    try {
      let events;
      try {
        events = JSON.parse(e.nativeEvent.data);
      } catch (jsonError) {
        events = base64Decode(e.nativeEvent.data);
      }
      JMLogger.log('BRIDGE EVENT RECEIVED => ', events);
      switch (events.type) {
        case WebViewEventConstant.OPEN_ACTION:
          handleOpenActionEvent(events.value);
          break;

        case WebViewEventConstant.PRESENT_HEADER:
          presentHeaderHandler(events.value);
          break;
        case WebViewEventConstant.UPDATE_NAVIGATION_DETAILS:
          setMenuTitle(events.value.menuTitle);
          JMSharedViewModel.Instance.setPreviousPageURL(
            events.value.previousPageURL,
          );
          break;
        case WebViewEventConstant.IS_DELIVER_TO_BAR_VISIBLE:
          setShouldShowDeliverToBar(!!events.value);
          break;
        case WebViewEventConstant.HANDLE_DEEP_LINK:
          break;
        case WebViewEventConstant.INIT_CRA:
          openLogin(navigation);
          break;
        case WebViewEventConstant.UPDATED_CART_COUNT:
          break;
        case WebViewEventConstant.WISHLIST_UPDATED:
          break;
        case WebViewEventConstant.LOADING_COMPLETED:
          break;
        case WebViewEventConstant.GET_PSP_APP_LIST:
          break;
        case WebViewEventConstant.STATUS_BAR_COLOR:
          break;
        case WebViewEventConstant.ORDER_CONFIRMED:
          break;
        case WebViewEventConstant.SAVE_SESSION_DETAILS:
          break;
        case WebViewEventConstant.SAVEDETAILSFORSEARCHENDPOINT:
          break;
        case WebViewEventConstant.ISBNBVISIBLE:
          break;
        case WebViewEventConstant.SHOW_TOAST:
          break;
        case WebViewEventConstant.APPSFLYER_EVENT_TRIGGER:
          break;
        case WebViewEventConstant.SCROLL_POSITION:
          break;
        case WebViewEventConstant.lAUNCH_PSP_APP_FOR_UPI_PAYMENT:
          break;
        case WebViewEventConstant.SHARE:
          break;
        case WebViewEventConstant.DISABLESORTANDFILTER:
          break;
      }
    } catch (error) {
      JMLogger.log('Error in handleJSEvents - ', error);
    }
  };

  const onUrlOverride = (event: {navigationType: string; url: string}) => {
    if (
      Platform.OS === 'android' ||
      event.navigationType === 'click' ||
      event.navigationType === 'other'
    ) {
      return true;
    }
    return true;
  };

  const onError = () => {
    setWebState(prev => ({...prev, state: WebStateType.ERROR}));
  };

  return {
    webState,
    webViewRef,
    setWebState,
    handleJSEvents,
    presentHeaderHandler,
    onUrlOverride,
    onError,
    handleBackPress,
    setMenuTitle,
    handleNavigationStateChange,
    navigationBean: bean,
    ...props,
  };
};

export interface WebViewState {
  state: WebStateType;
  content?: string;
}

export enum WebStateType {
  LOADING,
  URL,
  HTML,
  ERROR,
}

export default useWebViewController;
