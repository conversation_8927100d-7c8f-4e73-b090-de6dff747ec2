import React from 'react';
import JMNavigationStack from './utils/JMNavigationStack';
import useJioMartMainUIController from './controllers/useJioMartMainUIController';
import {JioMartMainUIProps} from './types/JioMartMainUIType';
import {QueryClient, QueryClientProvider} from '@tanstack/react-query';
import {Provider} from 'react-redux';
import store from './store/store';
import {CustomTokenProvider} from '@jio/rn_components';
import {JIOMART_THEME_SETTING} from './config/theme';
import {GestureHandlerRootView} from 'react-native-gesture-handler';
import {GlobalStateProvider} from '@jm/jiomart-general/src/utils/JMGlobalStateProvider';
import {Platform} from 'react-native';
import Config from 'react-native-config';
import {initializeGooglePlacesSdk} from '@jm/jiomart-common/src/GoogleSdkUtility';

export interface ParamsData {
  phoneNumber?: string;
  deeplink: string;
  data?: {
    token: string;
    webIosClientId: string;
    iosClientId: string;
  };
}

const queryClient = new QueryClient();
const googleMapKey =
  Platform.OS === 'ios'
    ? Config.IOS_GOOGLE_MAP_KEY_DEBUG
    : 'AIzaSyBKUOZtIHL2zbbmA9mMizy0Nv0iPvQfPLo';

initializeGooglePlacesSdk(`${googleMapKey}`);

export function JioMartMainUI(props: JioMartMainUIProps) {
  useJioMartMainUIController(props);

  return (
    <QueryClientProvider client={queryClient}>
      <Provider store={store}>
        <CustomTokenProvider value={JIOMART_THEME_SETTING}>
          <GestureHandlerRootView>
            <GlobalStateProvider>
              <JMNavigationStack />
            </GlobalStateProvider>
          </GestureHandlerRootView>
        </CustomTokenProvider>
      </Provider>
    </QueryClientProvider>
  );
}
