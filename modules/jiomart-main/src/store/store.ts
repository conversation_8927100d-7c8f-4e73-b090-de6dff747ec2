import {Action, ThunkAction, configureStore} from '@reduxjs/toolkit';
import webSlice from '../../../jiomart-webmanager/src/webSlice';
import JMFileSlice from '../../../jiomart-networkmanager/src/JMConfigFileManager/JMFileSlice';
import cartReducer from '@jm/jiomart-cart/src/slices/cartSlice';
import wishlistReducer from '@jm/jiomart-wishlist/src/slices/wishlistSlice';

const store = configureStore({
  reducer: {
    fileVersion: JMFileSlice,
    web: webSlice,
    cart: cartReducer,
    wishlist: wishlistReducer,
  },
});

export default store;
export type StoreState = ReturnType<typeof store.getState>;
export type AppThunk = ThunkAction<void, StoreState, null, Action<string>>;
export type AppDispatch = typeof store.dispatch;
