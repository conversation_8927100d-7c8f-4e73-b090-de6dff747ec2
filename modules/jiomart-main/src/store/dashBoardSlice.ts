import {PayloadAction, createSlice} from '@reduxjs/toolkit';
import {resetStore} from './JMGlobalReduxAction';
// import { JHStoreModify_KYC } from '../models/JHDashboardModel';
// import { JHCancelMandateRequestModel } from '../features/dashboard/serviceRequest/manageMandates/model/JHCancelMandateViewModel';

export interface DashboardState {
  // modify_kyc?: JHStoreModify_KYC;
  // otmCancel?: JHCancelMandateRequestModel
}

const initialState: DashboardState = {};

const dashBoardSlice = createSlice({
  name: 'dashboard',
  initialState: initialState,
  extraReducers: builder => builder.addCase(resetStore, () => initialState),
  reducers: {
    // setKYCDetails: (state, action: PayloadAction<JHStoreModify_KYC>) => {
    //   state.modify_kyc = action.payload;
    //   return state;
    // },
    // setOtmCancelRequestModel: (state, action: PayloadAction<JHCancelMandateRequestModel>) => {
    //   state.otmCancel = action.payload
    //   return state;
    // }
  },
});

export const {
  // setKYCDetails,
  // setOtmCancelRequestModel
} = dashBoardSlice.actions;

export default dashBoardSlice.reducer;
