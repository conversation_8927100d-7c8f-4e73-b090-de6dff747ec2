import {da} from 'date-fns/locale';
import {Alert, NativeModules} from 'react-native';
import {
  IsSensorAvailableResult,
  SimplePromptOptions,
  SimplePromptResult,
} from './JMBridge';
import {ItelephoneData} from './JMBridge';

const {JHBridgeModule, CallModule} = NativeModules;

export const handleIosBackPress = async () => {
  console.log('dismissRootViewController');
  return await JHBridgeModule.dismissRootViewController();
};

export const redirectToNativeiOS = (bean: any) => {
  return JHBridgeModule.redirectDeeplink(bean);
};

export const cameraImageiOS = async (data: string) => {
  return await JHBridgeModule.openNativeCamera(data);
};

export const getBase64StringFromSvgiOS = async (data: string) => {
  return await JHBridgeModule.convertSvgToPng(data);
};

export const openCalenderPickeriOS = async (calendarPickerData: any) => {
  return await JHBridgeModule.showDatePicker(calendarPickerData);
};
export const getIosPspAppList = () => {
  return JHBridgeModule.getPspAppList();
};

export const launchUpiAppInIos = (upiUrl: string) => {
  return JHBridgeModule.launchUpiApp(upiUrl);
};
export const keyboardResizeInIos = (flag: boolean) => {
  return JHBridgeModule.keyboardResize(flag);
};

export function isBiometricAvailableIos(): Promise<IsSensorAvailableResult> {
  return JHBridgeModule.isBiometricAvailable();
}

export function authenticateUser(): Promise<SimplePromptResult> {
  return JHBridgeModule.authenticateUser();
}

export const getBiometricTypeIos = async () => {
  try {
    const result = await JHBridgeModule.getBiometricType();
    console.log('Biometric result ios : ' + result);
    return result;
  } catch (error) {
    console.error('Error checking biometric availability', error);
    return false;
  }
};
export const handleCallActionIos = (dataMap: ItelephoneData) => {
  const phoneNumber = dataMap.value;

  if (phoneNumber && phoneNumber.length > 0) {
    CallModule.handleCallAction(phoneNumber);
  } else {
    Alert.alert('Number not available');
  }
};
