import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>ing, NativeModules, Platform, ToastAndroid } from "react-native";
import { redirectToNativeAndroid, cameraImageAndroid, openCameraScreenAndroid, getBase64StringFromSvg, openCalenderPickerAndroid, getAndroidPspAppList, launchUpiAppInAndroid, getAndroidReadOtp,
  keyboardResizeInAndroid, handleCallActionAndroid,
  showPhoneNumberHintAndroid,
  isSensorAvailableAndroid,
  makeSimpleBiometricPrompt
  } from "./JMBridgeAndroid";
import { handleIosBackPress, redirectToNativeiOS, cameraImageiOS, getBase64StringFromSvgiOS, openCalenderPickeriOS, getIosPspAppList, launchUpiAppInIos, keyboardResizeInIos, handleCallActionIos,isBiometricAvailableIos, authenticateUser } from "./JMBridgeiOS";
interface IMailData {
  subject?:string,
  body?:string,
  email_id?:string
}
export interface ItelephoneData {
  value?:string
}

interface DataMap {
  value: string;
}

export const openCameraScreen = async (cameraData: any) => {
  const data = JSON.stringify(cameraData);
  console.log('JSon Data', data)
  if (Platform.OS === "android") {
    return openCameraScreenAndroid(data)
  } else {
    return cameraImageiOS(data)
  }

}

export const openCalenderPicker = async (calendarPickerData: any) => {
  const data = JSON.stringify(calendarPickerData);
  console.log('JSon Data', data)
  if (Platform.OS === "android") {
    return openCalenderPickerAndroid(data)
  } else {
    return openCalenderPickeriOS(data)
  }

}

export const redirectToNativeDeeplink = (deeplinkObject: any) => {
  console.log("redirectDeepLinkObj", deeplinkObject)
  const data = JSON.stringify(deeplinkObject);
  if (Platform.OS === 'android') {
    return redirectToNativeAndroid(data);
  } else {
    return redirectToNativeiOS(data);
  }
};

export const handleSystemBackPress = () => {
  if (Platform.OS === 'android') {
    BackHandler.exitApp();
  } else {
    handleIosBackPress();
  }
};

export const getBase64FromSvg = async (svgString: string) => {
  if (Platform.OS === "android") {
    return getBase64StringFromSvg(svgString)
  } else {
    return getBase64StringFromSvgiOS(svgString)
  }
}


export const getPspAppList = async () => {
  if (Platform.OS === 'android') {
    return getAndroidPspAppList();
  } else {
    return getIosPspAppList();
  }
};
export const readOtp = async (isSmsRetrieverMethod: boolean) => {
  if (Platform.OS === 'android') {
    return getAndroidReadOtp(isSmsRetrieverMethod);
  }
};

export const launchUpiApp = (upiUrl: string) => {
  if (Platform.OS === 'android') {
    return launchUpiAppInAndroid(upiUrl);
  } else {
    return launchUpiAppInIos(upiUrl);
  }
};

export const keyboardResize = (flag: boolean) => {
  console.log("keyboardResize " + flag)
  if (Platform.OS === 'ios') {
    return keyboardResizeInIos(flag);
  }
  else {
    return keyboardResizeInAndroid(flag);
  }
};

export const showPhoneNumberHint = async() => {
  console.log('JHBridge-showPhoneNumberHint');
  if (Platform.OS === 'android') {
    return await showPhoneNumberHintAndroid()
  }else{
    console.log('not android device')
  }
};

/* 
* This function tells us if a device has biometric authencation ability or not  
*/
export const isBioMetricAvailable = async() => {
  if (Platform.OS === 'android') {
    console.log('isBioMetricAvailable android')
    return await isSensorAvailableAndroid();
  } else {
    console.log('isBioMetricAvailable ios')
    return await isBiometricAvailableIos();
  }
}

/* 
* This function will trigger popup for biometric authencation. Generally android : fingerprint , ios : faceId. 
*/
export const makeBiometricPrompt = async(simplePromptOptions: SimplePromptOptions) => {

  // First we are checking if bio metric capabilities are there for the deivice.
  let result = (await isBioMetricAvailable()) as IsSensorAvailableResult;

  if(result.available){

    // We show the prompt
    if (Platform.OS === 'android') {
      console.log('makeBiometricPrompt : android ')
      return await makeSimpleBiometricPrompt(simplePromptOptions);
    } else {
      console.log('makeBiometricPrompt : ios')
      return await authenticateUser();
    }
  }else{
    // In case biometrics are not available, return a result with success: false and an error message
    return {
      success: false,
      error: 'Biometric authentication not available',
    };
  }

}

export interface IsSensorAvailableResult {
  available: boolean;
  biometryType?: string;
  error?: string;
}
export interface SimplePromptOptions {
  promptTitle?: string;
  promptSubTitle?: string;
  cancelButtonText?: string;
  payload?: string;
}
export interface SimplePromptResult {
  success: boolean;
  error?: string;
}export const handleCallAction = (data:ItelephoneData)=>{
  console.log('callHandler '+ data);
  if(Platform.OS ==='ios'){
    return handleCallActionIos(data);
  }
  else{
    return handleCallActionAndroid(data);
  }
};
export const handleMailAction = (dataMap:IMailData) => {
  const subject = dataMap.subject || '';
  const body = dataMap.body || '';
  const emailId = dataMap.email_id || '';

  if (!emailId) {
    Alert.alert('Error', 'Email ID is not available.');
    return;
  }
  const emailUrl = `mailto:${emailId}?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(body)}`;
  Linking.canOpenURL(emailUrl)
    .then((supported) => {
      if (!supported) {
        Alert.alert('Error', 'No email app is available to send the mail.');
      } else {
        return Linking.openURL(emailUrl);
      }
    })
    .catch((err) => {
      Alert.alert('Error', 'Something went wrong while trying to open the email app.');
      console.error(err);
    });
};

export const handleWhatsAppAction = (dataMap: DataMap) => {
  const url = dataMap.value;

  if (!url) {
    Alert.alert('Error', 'WhatsApp URL is not available.');
    return;
  }

  Linking.canOpenURL(url)
    .then((supported) => {
      if (!supported) {
        Alert.alert('Error', 'WhatsApp is not available.');
      } else {
        return Linking.openURL(url);
      }
    })
    .catch((err) => {
      Alert.alert('Error', 'Something went wrong while trying to open WhatsApp.');
      console.error(err);
    });
};
