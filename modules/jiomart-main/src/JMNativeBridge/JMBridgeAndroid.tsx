import {NativeModules, ToastAndroid} from 'react-native';
import {
  IsSensorAvailableResult,
  ItelephoneData,
  openCalenderPicker,
  SimplePromptOptions,
  SimplePromptResult,
} from './JMBridge';
const JHInvestAndroid = NativeModules.JHReactNativeModule;

const {
  CameraModule,
  CallModule,
  CalendarModule,
  SvgToPng,
  PaymentApps,
  SmsRetrieverModule,
  GeneralPropertiesModule,
  PhoneNumberHintModule,
  AndroidBiometrics,
} = NativeModules;
export const handleAndroidBackPress = async (popCount: number) => {
  return await JHInvestAndroid.dismissReactScreen(popCount);
};

export const redirectToNativeAndroid = (bean: any) => {
  console.log(bean);
  return CameraModule.openCameraScreen(bean);
};

export const openCameraScreenAndroid = (data: string) => {
  console.log('openCameraScreenAndroid');
  return CameraModule.openCameraScreen(data);
};

export const openCalenderPickerAndroid = (calendarPickerData: any) => {
  console.log('openCameraScreenAndroid');
  return CalendarModule.getDateFromAndroid(calendarPickerData);
};

export const getJFSHomeRuleEngineRequestParameterAndroid = async () => {
  return await JHInvestAndroid.getJFSHomeRuleEngineRequestParameter();
};

export const refreshRulesEngineTokenAndroid = async () => {
  return await JHInvestAndroid.refreshRulesEngineToken('');
};

export const cameraImageAndroid = async () => {
  return await CameraModule.sendCameraImage();
};

export const getBase64StringFromSvg = (data: string) => {
  console.log('getBase64String');
  return SvgToPng.convertSvgToPng(data);
};

export const getAndroidPspAppList = () => {
  return PaymentApps.getPspAppList();
};

export const launchUpiAppInAndroid = (upiUrl: string) => {
  return PaymentApps.launchUpiApp(upiUrl);
};
export const getAndroidReadOtp = async (isSmsRetrieverMethod: boolean) => {
  return await SmsRetrieverModule.startAutoRead(isSmsRetrieverMethod);
};
export const keyboardResizeInAndroid = (flag: boolean) => {
  return GeneralPropertiesModule.setResizeMode(flag);
};

export const showPhoneNumberHintAndroid = async () => {
  return await PhoneNumberHintModule.showPhoneHint();
};

export function isSensorAvailableAndroid(): Promise<IsSensorAvailableResult> {
  return AndroidBiometrics.isSensorAvailable();
}

export function makeSimpleBiometricPrompt(
  simplePromptOptions: SimplePromptOptions,
): Promise<SimplePromptResult> {
  return AndroidBiometrics.simplePrompt(simplePromptOptions);
}

export const handleCallActionAndroid = (dataMap: ItelephoneData) => {
  const phoneNumber = dataMap.value;

  if (phoneNumber && phoneNumber.length > 0) {
    CallModule.handleCallAction(phoneNumber);
  } else {
    ToastAndroid.show('Number not available', ToastAndroid.SHORT);
  }
};
