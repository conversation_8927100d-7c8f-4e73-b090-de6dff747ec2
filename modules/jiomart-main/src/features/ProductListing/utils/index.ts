export const selectedL3Category = (category: any[], l3Slug: string) => {
  if (!category) {
    return null;
  }
  // category.
  let CategoryRes = category
    ?.map(l1Category => {
      let l2CategoryRes = l1Category.childs
        ?.map((l2Category: any) => {
          let l3CategoryRes = l2Category.childs
            ?.map((l3Category: any) => {
              return l3Category.slug === l3Slug ? l3Category : null;
            })
            .filter((removeNull: any) => removeNull !== null);
          if (l3CategoryRes) {
            return l3CategoryRes[0];
          }
        })
        ?.filter((removeUndefined: any) => removeUndefined !== undefined);

      if (l2CategoryRes) {
        return l2CategoryRes[0];
      }
    })
    ?.filter(removeUndefined => removeUndefined !== undefined);
  return CategoryRes?.[0];
};
export const selectedL1Category = (category: any[], l1Slug: string) => {
  if (!category) {
    return null;
  }
  for (let l1category of category) {
    if (l1category?.slug === l1Slug) {
      return l1category;
    }

    if (l1category?.childs) {
      for (let l2category of l1category.childs) {
        if (l2category?.slug === l1Slug) {
          return l2category;
        }
      }
    }
  }
  return null;
};

export const fetchL3Category = (category: any[], slug: string) => {
  if (!category) {
    return [];
  }
  for (let l1category of category) {
    if (l1category?.slug === slug) {
      return l1category?.childs ?? [];
    }

    if (l1category?.childs) {
      for (let l2category of l1category.childs) {
        if (l2category?.slug === slug) {
          return l2category?.childs ?? [];
        }

        if (l2category?.childs) {
          for (let l3category of l2category.childs) {
            if (l3category?.slug === slug) {
              return l2category?.childs ?? [];
            }
          }
        }
      }
    }
  }
  return [];
};

export const mapFilterKeyValue = (filter: any) => {
  return filter.reduce((acc: any, curr: any) => {
    const selected = curr.values
      ?.filter((v: any) => v.is_selected)
      ?.map((v: any) => {
        if (v?.query_format) {
          return `[${v?.min} TO ${v?.max}]`;
        }
        return v.value;
      });
    if (selected?.length) {
      acc[curr.key.name] = selected;
    }
    return acc;
  }, {} as {[key: string]: string[]});
};
export const countSelectedFilterTabs = (filter: any): number => {
  if (!filter) {
    return 0;
  }
  return filter.reduce((count: number, curr: any) => {
    const selected = curr.values?.filter((v: any) => v.is_selected);
    if (selected?.length) {
      count += 1;
    }
    return count;
  }, 0);
};