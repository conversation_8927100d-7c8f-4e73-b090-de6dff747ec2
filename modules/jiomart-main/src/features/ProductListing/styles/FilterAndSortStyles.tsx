import { rw } from '../../../../../jiomart-common/src/JMResponsive';
import {StyleSheet} from 'react-native';

export const styles = StyleSheet.create({
  container: {
    paddingHorizontal: 24,
    paddingVertical: 12,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  badge: {
    alignSelf: 'center',
    paddingHorizontal: rw(4),
    borderRadius: rw(8),
  },
  animateContainer: {
    backgroundColor: 'white',
    position: 'absolute',
    left: 0,
    right: 0,
    zIndex: 1,
  },
  shadowBox: {
    backgroundColor: '#fff',
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 4},
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 5,
  },
  button: {
    flexDirection: 'row',
    borderWidth: 1,
    paddingHorizontal: 10,
    paddingVertical: 6,
    justifyContent: 'center',
    alignItems: 'center',
    columnGap: 2,
    borderRadius: 100,
    borderColor: '#E0E0E0',
  },
  badge: {
    width: rw(14),
    height: rw(14),
    borderRadius: 8,
    backgroundColor: '#E53535',
    marginHorizontal: 2,
    alignSelf: 'center',
  },
  option: {flexDirection: 'row', columnGap: 8, marginLeft: 'auto'},
  onText: {
    flexDirection: 'row',
    alignItems: 'center',
    maxWidth: '40%',
  },
  sortBadge: {
    width: rw(8),
    height: rw(8),
    borderRadius: 8,
    backgroundColor: '#E53535',

    marginHorizontal: 2,
    alignSelf: 'center',
  },
});
