import {Platform, StyleSheet} from 'react-native';

export const styles = StyleSheet.create({
  filterOption: {
    flexDirection: 'row',
    columnGap: 8,
    paddingVertical: 12,
    alignItems: 'center',
  },
  unSelectedCheckbox: {
    borderWidth: 0.45,
    width: 16,
    height: 16,
    borderRadius: 4,
  },
  checkbox: {
    width: 16,
    height: 16,
    borderRadius: 4,
    alignItems: 'center',
    justifyContent: 'center',
  },
  unSelectedRadio: {
    borderWidth: 1,
    width: 16,
    height: 16,
    borderRadius: 100,
  },
  selectedRadio: {
    borderWidth: 4,
    width: 16,
    height: 16,
    borderRadius: 100,
  },
  title: {marginRight: 'auto', flexShrink: 1},
  rightIcon: {paddingHorizontal: 4},
  category: {paddingLeft: 16},
  bottom: {paddingBottom: 24},
  search: {
    paddingHorizontal: 12,
    paddingVertical: Platform.OS === 'android' ? 0 : 8,
    borderRadius: 100,
    columnGap: 12,
    flexDirection: 'row',
    marginBottom: 8,
    alignItems: 'center',
  },
  leftPanel: {flex: 11},
  rightPanel: {
    flex: 19,
    paddingRight: 8,
  },
  rightPanelContent: {
    paddingTop: 8,
  },
  filterContainer: {
    flexDirection: 'row',
    marginTop: 8,
    columnGap: 8,
    marginRight: 16,
    flex: 1,
  },
  container: {flex: 1},
  filterFooter: {
    paddingHorizontal: 24,
    paddingVertical: 12,
    flexDirection: 'row',
    columnGap: 16,
    backgroundColor: '#ffff',
  },
  clear: {
    borderWidth: 1,
    paddingVertical: 4,
    paddingHorizontal: 16,
    alignSelf: 'stretch',
    borderRadius: 100,
    alignItems: 'center',
    flex: 1,
  },
  apply: {
    paddingVertical: 4,
    paddingHorizontal: 16,
    alignSelf: 'stretch',
    borderRadius: 100,
    alignItems: 'center',
    flex: 1,
  },
  tab: {
    padding: 12,
    flexDirection: 'row',
    alignItems: 'center',
    columnGap: 7,
    // borderRadius: 4,
    // marginHorizontal: 4,
  },
  disabled: {opacity: 0.5},
  textinput: {
    fontSize: 16,
    flex: 1,
    height: Platform.OS === 'android' ? 40 : undefined,
    lineHeight: 24,
  },
  badge: {
    marginLeft: 'auto',
    backgroundColor: '#FC6770',
    paddingHorizontal: 4,
    borderRadius: 100,
  },
});
