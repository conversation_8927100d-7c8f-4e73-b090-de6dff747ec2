import { rw } from '../../../../../jiomart-common/src/JMResponsive';
import {StyleSheet} from 'react-native';

export const styles = StyleSheet.create({
  container: {
    backgroundColor: 'white',
    paddingHorizontal: rw(24),
    paddingBottom: rw(24),
  },
  radio: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: rw(12),
    columnGap: 8,
  },
  selected: {
    width: rw(24),
    height: rw(24),
    borderRadius: 100,
    borderWidth: 6,
  },
  unselected: {
    width: rw(24),
    height: rw(24),
    borderRadius: 100,
    borderWidth: 1,
  },
});
