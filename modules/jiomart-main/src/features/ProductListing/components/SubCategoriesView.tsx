import {View, StyleSheet, ScrollView, Platform} from 'react-native';
import React, {useCallback, useRef, useState, useMemo, useEffect} from 'react';
import {JioText} from '@jio/rn_components';
import {JioTypography} from '@jio/rn_components/src/index.types';
import {Pressable} from 'react-native-gesture-handler';
import Animated, {
  useAnimatedStyle,
  useSharedValue,
  withTiming,
} from 'react-native-reanimated';
import {rh, rw} from '@jm/jiomart-common/src/JMResponsive';
import handleHapticFeedback from '@jm/jiomart-common/src/utils/JMHapticFeedback';

interface SubCategoriesViewProps {
  data: any[];
  onCategorySelect?: (category: any) => void;
  allImage?: String;
  resetCategoryAnimation: any;
  setResetCategoryAnimation: any;
}
const SubCategoriesView = ({
  data,
  onCategorySelect,
  allImage,
  resetCategoryAnimation,
  setResetCategoryAnimation,
}: SubCategoriesViewProps) => {
  const [selectedIndex, setselectedIndex] = useState<number>(0);
  useEffect(() => {
    categoryAnimation(0);
  }, [data]);
  useEffect(() => {
    if (resetCategoryAnimation) {
      categoryAnimation(0);
      setResetCategoryAnimation(false);
    }
  }, [resetCategoryAnimation]);
  const scrollViewRef = useRef<ScrollView>(null);
  const animation = useSharedValue(0);
  const allItem = useMemo(
    () => ({
      display: 'All',
      value: '',
      logo:
        allImage ??
        'https://cdn.pixelbin.io/v2/catalog-cloud-non-prod/original/BizbZdLqn-logo.png',
    }),
    [data],
  );

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{translateY: animation.value}],
  }));

  const dataWithAll = useMemo(() => {
    return [allItem, ...(data || [])];
  }, [allItem, data]);

  const categoryAnimation = (index: number) => {
    animation.value = withTiming(index * rh(112), {duration: 600});
    setselectedIndex(index);
    const bottomPosition = index * rh(108);

    if (scrollViewRef.current) {
      scrollViewRef.current?.scrollTo({
        x: 0,
        y: bottomPosition - 3 * rh(108),
        animated: true,
      });
    }
  };
  const handlePress = useCallback(
    (index: number, item: any) => {
      if (onCategorySelect) {
        onCategorySelect(item);
      }
      categoryAnimation(index);
    },
    [onCategorySelect],
  );

  return (
    <View style={[styles.container]}>
      <ScrollView
        ref={scrollViewRef}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.contentStyle}
        scrollEventThrottle={16}>
        <Animated.View
          style={[
            {
              width: '100%',
              height: rh(108),
              position: 'absolute',
              backgroundColor: '#E5F1F7',
              flexDirection: 'row',
              borderBottomRightRadius: 12,
              borderTopRightRadius: 12,
            },
            {backgroundColor: undefined, height: rh(100)},
            animatedStyle,
          ]}>
          <View
            style={[
              {
                width: '4%',
                height: '100%',
                alignSelf: 'center',
                borderTopRightRadius: 4,
                borderBottomRightRadius: 4,
                backgroundColor: '#0078AD',
              },
              {position: 'absolute', right: 0},
            ]}
          />
        </Animated.View>
        {dataWithAll.map((item, index) => (
          <CategoryBlock
            key={`${index}-${item.display}`}
            image={item?.logo}
            title={item?.display ?? ''}
            onPress={() => {
              if (Platform.OS == 'ios') handleHapticFeedback('impactLight');
              handlePress(index, item);
            }}
            selected={index === selectedIndex}
          />
        ))}
      </ScrollView>
    </View>
  );
};

interface CategoryBlockProps {
  image?: string;
  title?: string;
  selected?: boolean;
  disabled?: boolean;
  onPress: () => void;
}

const CategoryBlock = (props: CategoryBlockProps) => {
  const {onPress, selected, disabled, image, title} = props;

  const imageTranslateY = useSharedValue(selected ? -rh(4) : rh(7));

  React.useEffect(() => {
    imageTranslateY.value = withTiming(selected ? -rh(4) : rh(7), {
      duration: 600,
    });
  }, [selected]);

  const imageStyle = useAnimatedStyle(() => ({
    alignSelf: 'center',
    transform: [{translateY: imageTranslateY.value}],
  }));

  return (
    <Pressable
      onPress={onPress}
      style={styles.catContainer}
      disabled={selected}>
      <View style={styles.catImage}>
        <View
          style={{
            backgroundColor: selected ? '#89DCFF' : '#F5F5F5',
            width: rw(50),
            height: rw(50),
            borderRadius: 100,
          }}>
          <Animated.Image
            source={{uri: image}}
            style={[imageStyle, {height: rw(46), width: rw(46)}]}
          />
        </View>
        <JioText
          text={title ?? ''}
          appearance={
            selected ? JioTypography.BODY_XXS_BOLD : JioTypography.BODY_XXS
          }
          color={selected ? 'primary_60' : 'primary_grey_80'}
          textAlign={'center'}
          maxLines={2}
          style={styles.text}
        />
      </View>
    </Pressable>
  );
};

export default SubCategoriesView;

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#ffffff',
    width: rw(78),
    borderTopRightRadius: 4,
    marginTop: 4,
  },
  contentStyle: {
    rowGap: 12,
    paddingBottom: 30,
  },
  catContainer: {
    paddingTop: rh(8),
    flexDirection: 'row',
    height: rh(100),
    paddingHorizontal: rw(4),
  },
  catImage: {
    alignItems: 'center',
    alignSelf: 'center',
    flex: 1,
    marginRight: 3,
  },
  verticalDivider: {
    width: rw(3.04),
    height: '100%',
    backgroundColor: '#0078AD',
    borderTopLeftRadius: 4,
    borderBottomLeftRadius: 4,
  },
  text: {paddingVertical: rh(8)},
});
