import React from 'react';
import {
  View,
  StyleSheet,
  useWindowDimensions,
  StyleProp,
  ViewStyle,
  FlatList,
  ScrollView,
} from 'react-native';
import {ShimmerKind} from '@jio/rn_components/src/index.types';
import JMShimmer from '../../../../../../jiomart-general/src/ui/JMShimmer';
import {rh, rw} from '../../../../../../jiomart-common/src/JMResponsive';
import FashionItemShimmer from './FashionItemShimmer';

const ProductCardShimmer = React.memo(() => (
  <View style={styles.cardContainer}>
    <JMShimmer
      width={rw(112)}
      kind={ShimmerKind.RECTANGLE}
      style={styles.cardImage}
      height={rw(112)}
    />
    <View style={styles.cardContent}>
      <View style={styles.textContainer}>
        <JMShimmer
          width="100%"
          kind={ShimmerKind.RECTANGLE}
          style={styles.textLine}
          height={rh(16)}
        />
        <JMShimmer
          width="80%"
          kind={ShimmerKind.RECTANGLE}
          height={rh(16)}
          style={[styles.textLine, styles.marginTop4]}
        />
        <JMShimmer
          width="40%"
          kind={ShimmerKind.RECTANGLE}
          height={rh(16)}
          style={[styles.textLine, styles.marginTop4]}
        />
      </View>
      <JMShimmer
        width="30%"
        kind={ShimmerKind.RECTANGLE}
        height={rh(16)}
        style={styles.priceTag}
      />
    </View>
  </View>
));

export const PlpFilterTopShimmer = React.memo(() => (
  <View style={styles.topBar}>
    <JMShimmer
      width={rw(130)}
      height={rh(10)}
      style={styles.roundedShimmer}
      kind={ShimmerKind.RECTANGLE}
    />
    <View style={styles.filterButtons}>
      <JMShimmer
        width={rw(63)}
        height={rh(28)}
        style={styles.roundedShimmer}
        kind={ShimmerKind.RECTANGLE}
      />
      <JMShimmer
        width={rw(83)}
        height={rh(28)}
        style={styles.roundedShimmer}
        kind={ShimmerKind.RECTANGLE}
      />
    </View>
  </View>
));

export const PlpListShimmer = React.memo(
  ({style}: {style?: StyleProp<ViewStyle>}) => {
    return (
      <ScrollView
        style={[styles.container, style]}
        showsHorizontalScrollIndicator={false}
        showsVerticalScrollIndicator={false}
        bounces={false}>
        {Array.from({length: 10}).map((_, index) => (
          <ProductCardShimmer key={`card-${index}`} />
        ))}
      </ScrollView>
    );
  },
);

const renderGridItemShimmer = item => {
  return <FashionItemShimmer />;
};

export const PlpGridShimmer = React.memo(
  ({style}: {style?: StyleProp<ViewStyle>}) => {
    const {height} = useWindowDimensions();
    const numCards = Math.ceil((height - rh(281)) / rh(150));

    return (
      <View style={[styles.container, style]}>
        <FlatList
          showsVerticalScrollIndicator={false}
          scrollEnabled={false}
          data={Array.from({length: numCards})}
          renderItem={renderGridItemShimmer}
          numColumns={2}
          columnWrapperStyle={{
            justifyContent: 'space-between',
            marginBottom: rh(32),
          }}
        />
      </View>
    );
  },
);

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: 'white',
    paddingHorizontal: rw(16),
  },
  cardContainer: {
    flexDirection: 'row',
    marginBottom: rh(25),
    paddingBottom: rh(12),
  },
  cardImage: {
    borderRadius: rw(24),
  },
  cardContent: {
    flexDirection: 'column',
    justifyContent: 'space-between',
    marginLeft: rw(12),
    flex: 1,
  },
  textContainer: {
    width: '100%',
  },
  textLine: {
    borderRadius: rw(24),
  },
  marginTop4: {
    marginTop: rh(4),
  },
  priceTag: {
    marginBottom: rh(8),
    marginLeft: 'auto',
    borderRadius: rw(24),
  },
  topBar: {
    flexDirection: 'row',
    paddingHorizontal: rw(24),
    paddingVertical: rh(16),
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  filterButtons: {
    flexDirection: 'row',
    gap: rw(8),
  },
  roundedShimmer: {
    borderRadius: rw(24),
  },
});

export default PlpListShimmer;
