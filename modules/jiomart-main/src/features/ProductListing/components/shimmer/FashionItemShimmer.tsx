import React from 'react';
import {StyleSheet, View} from 'react-native';
import {ShimmerKind} from '@jio/rn_components/src/components/JioShimmer/JioShimmer.types';
import JMShimmer from '../../../../../../jiomart-general/src/ui/JMShimmer';
import {rw} from '../../../../../../jiomart-common/src/JMResponsive';

const FashionItemShimmer = () => {
  return (
    <View style={styles.container}>
      <View style={styles.content}>
        <JMShimmer
          width={rw(132)}
          height={rw(132)}
          style={{borderRadius: 24}}
          kind={ShimmerKind.RECTANGLE}
        />
        <JMShimmer
          width={rw(132)}
          height={16}
          kind={ShimmerKind.RECTANGLE}
          style={{marginTop: 12, borderRadius: 24}}
        />

        <JMShimmer
          width={rw(132)}
          height={16}
          kind={ShimmerKind.RECTANGLE}
          style={{marginTop: 12, borderRadius: 24}}
        />

        <JMShimmer
          width={rw(132)}
          height={16}
          kind={ShimmerKind.RECTANGLE}
          style={{marginTop: 12, borderRadius: 24}}
        />

        <JMShimmer
          width={rw(66)}
          height={16}
          kind={ShimmerKind.RECTANGLE}
          style={{marginTop: 28, borderRadius: 24}}
        />
      </View>
    </View>
  );
};

export default FashionItemShimmer;

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
  },
  content: {paddingHorizontal: 8},
});
