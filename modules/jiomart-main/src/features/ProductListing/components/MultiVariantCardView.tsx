import React from 'react';
import {
  View,
  Image,
  StyleSheet,
  ActivityIndicator,
  Pressable,
} from 'react-native';
import {JioTypography} from '@jio/rn_components/src/index.types';
import {JioText} from '@jio/rn_components';
import JMTag from '../../../../../jiomart-general/src/ui/JMTag';
import AddCtaView from '../../../../../jiomart-general/src/ui/AddCta/AddCtaView';
import {rh} from '../../../../../jiomart-common/src/JMResponsive';

interface MultiVariantCardViewProps {
  imageUrl: string;
  title: string;
  price?: number;
  originalPrice?: number;
  verticalCode?: any;
  discount?: string;
  outOfStock?: boolean;
  isLoading?: boolean;
  slug?: string;
  itemId?: number;
  itemSize?: string;
  handleImagePress?: () => void;
}

const MultiVariantCardView: React.FC<MultiVariantCardViewProps> = ({
  imageUrl,
  title,
  price,
  originalPrice,
  verticalCode,
  discount,
  outOfStock,
  isLoading,
  slug,
  itemId,
  itemSize,
  handleImagePress,
}) => {
  if (isLoading) {
    return (
      <View
        style={{
          height: rh(75),
          width: '100%',
          justifyContent: 'center',
          alignItems: 'center',
        }}>
        <ActivityIndicator />
      </View>
    );
  }

  const outOfStockLocal = outOfStock;

  return (
    <View style={styles.card}>
      <Pressable onPress={handleImagePress}>
        <Image source={{uri: imageUrl}} style={styles.image} />
      </Pressable>
      <View style={styles.contentContainer}>
        <View style={styles.titleContainer}>
          <JioText
            text={title}
            appearance={JioTypography.BODY_XS_BOLD}
            color={'primary_grey_100'}
            style={{opacity: outOfStockLocal ? 0.3 : 1}}
          />
          {discount && !outOfStock && (
            <JMTag color={'#E5F7EE'} text={discount} />
          )}
        </View>
        <View style={styles.priceContainer}>
          <JioText
            text={`₹${price?.toFixed(2) || ''}`}
            appearance={JioTypography.BODY_XS}
            color={'primary_grey_80'}
            style={{opacity: outOfStockLocal ? 0.3 : 1}}
          />
          {/* {true && (
            <JioText
              text={`₹${30}`}
              appearance={JioTypography.BODY_XXS}
              color={'primary_grey_60'}
              style={{opacity: outOfStockLocal ? 0.3 : 1}}
            />
          )} */}
        </View>
        {originalPrice && !outOfStock ? (
          <JioText
            text={`₹${originalPrice.toFixed(2)}`}
            appearance={JioTypography.BODY_XS}
            color={'primary_grey_60'}
            style={{textDecorationLine: 'line-through'}}
          />
        ) : (
          <JioText
            text={`Out of Stock`}
            appearance={JioTypography.BODY_XXS}
            color={'feedback_error_50'}
          />
        )}
      </View>
      {!outOfStock && (
        <AddCtaView
          request={{
            uid: itemId,
            slug: slug,
            size: `${itemSize}`,
            meta: {vertical_code: verticalCode},
          }}
        />
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  card: {
    padding: 8,
    alignItems: 'center',
    justifyContent: 'center',
    flexDirection: 'row',
    gap: 12,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  image: {
    width: 60,
    height: 60,
    resizeMode: 'contain',
    backgroundColor: '#f5f5f5',
  },
  contentContainer: {
    flex: 1,
    gap: 4,
  },
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  priceContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
});

export default MultiVariantCardView;
