import React, {FC, useState} from 'react';
import {View, TextInput, StyleSheet} from 'react-native';
import {JioIcon} from '@jio/rn_components';
import {IconColor} from '@jio/rn_components/src/index.types';
import {rh, rw} from '../../../../../jiomart-common/src/JMResponsive';

interface SearchBarProps {
  onSubmit: (data: string) => void;
}

const SearchBar: FC<SearchBarProps> = ({onSubmit}) => {
  const [searchText, setSearchText] = useState<string>('');
  const [hideIcon, setHideIcon] = useState(false);

  return (
    <View style={styles.container}>
      {!hideIcon ? <JioIcon ic={'IcSearch'} color={IconColor.GREY100} /> : null}
      <TextInput
        style={styles.input}
        placeholder="Search..."
        placeholderTextColor={!hideIcon ? '#141414' : '#000000A6'}
        value={searchText}
        onChangeText={text => {
          onSubmit(text);
          setSearchText(text);
        }}
        keyboardType="default"
        onFocus={e => {
          setHideIcon(true);
        }}
        onEndEditing={e => {
          setHideIcon(false);
        }}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    borderRadius: 24,
    backgroundColor: '#F5F5F5',
    paddingHorizontal: rw(12),
    columnGap: rw(8),
    marginBottom: rh(8),
  },
  searchIcon: {
    marginRight: rw(12),
    marginBottom: rw(3),
  },
  input: {
    flex: 1,
    height: 40,
    fontSize: 16,
    textAlignVertical: 'center',
  },
});

export default SearchBar;
