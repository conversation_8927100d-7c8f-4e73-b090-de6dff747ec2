import {SafeAreaView, View, TouchableOpacity} from 'react-native';
import React from 'react';
import {JioText, useColor} from '@jio/rn_components';
import {JioTypography} from '@jio/rn_components/src/index.types';
import {styles} from '../styles/SortBtmSheetStyles';
import JMBtmSheetHeader from '../../../../../jiomart-general/src/ui/JMBtmSheetHeader';
import type {BottomSheetChildren} from '@jm/jiomart-general/src/ui/BottomSheet/types/BottomSheetType';

interface SortOption {
  name: string;
  value: string;
  logo: string;
  display: string;
  is_selected: boolean;
  priority?: number;
}

interface SortBtmSheetProps extends BottomSheetChildren {
  onClose: () => void;
  data: SortOption[];
  title: string;
  defaultSelected: string;
  onSelectedSort: (value: SortOption) => void;
}

const SortBtmSheet = (props: SortBtmSheetProps) => {
  const {onClose, data, title, onSelectedSort, defaultSelected, close} = props;
  const unSelectedRadioColor = useColor('primary_grey_80');
  const selectedRadioColor = useColor('primary_50');
  const handleClose = () => {
    close?.(onClose);
  };
  return (
    <SafeAreaView>
      <JMBtmSheetHeader title={title} onPress={handleClose} />
      <View style={styles.container}>
        {data?.map((item, index) => {
          return (
            <TouchableOpacity
              key={`sort-${index}`}
              style={styles.radio}
              disabled={defaultSelected === item?.display}
              onPress={() => {
                onSelectedSort(item);
                close?.(onClose);
              }}>
              <View
                style={[
                  defaultSelected === item.display
                    ? {
                        ...styles.selected,
                        borderColor: selectedRadioColor,
                      }
                    : {
                        ...styles.unselected,
                        borderColor: unSelectedRadioColor,
                      },
                ]}
              />
              <JioText
                text={item.display}
                appearance={
                  defaultSelected === item.display
                    ? JioTypography.BODY_S_LINK
                    : JioTypography.BODY_S
                }
                color={
                  defaultSelected === item.display ? 'black' : 'primary_grey_80'
                }
              />
            </TouchableOpacity>
          );
        })}
      </View>
    </SafeAreaView>
  );
};

export default SortBtmSheet;
