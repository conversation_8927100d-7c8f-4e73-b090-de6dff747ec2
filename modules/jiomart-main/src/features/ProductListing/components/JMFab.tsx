import {
  Platform,
  StyleSheet,
  TouchableHighlight,
  type StyleProp,
  type ViewStyle,
} from 'react-native';
import React from 'react';
import {JioIcon} from '@jio/rn_components';
import {IconColor} from '@jio/rn_components/src/components/JioIcon/JioIcon.types';
import {rh, rw} from '../../../../../jiomart-common/src/JMResponsive';

interface FabProps {
  onPress?: () => void;
  style?: StyleProp<ViewStyle>;
}

const JMFab = (props: FabProps) => {
  const {onPress, style} = props;
  return (
    <TouchableHighlight
      style={[styles.fab, style]}
      onPress={onPress}
      underlayColor={'white'}>
      <JioIcon ic={'IcArrowUp'} color={IconColor.PRIMARY60} />
    </TouchableHighlight>
  );
};

const styles = StyleSheet.create({
  fab: {
    right: rw(24),
    bottom: rh(85),
    position: 'absolute',
    backgroundColor: '#fff',
    borderRadius: 50,
    borderColor: '#E0E0E0',
    borderWidth: 1,
    ...Platform.select({
      ios: {
        shadowColor: '#000000',
        shadowOffset: {width: 0, height: 4},
        shadowOpacity: 0.07,
        shadowRadius: 4,
      },
      android: {
        elevation: 4.2,
      },
    }),
    padding: 10,
  },
  fabIcon: {
    width: 14,
    height: 14,
    marginHorizontal: 13,
    marginVertical: 13,
    borderColor: 'black',
    // borderWidth: 1,
  },
});

export default JMFab;
