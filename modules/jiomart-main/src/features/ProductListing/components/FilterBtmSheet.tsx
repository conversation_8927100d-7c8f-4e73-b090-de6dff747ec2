import {TouchableOpacity, View} from 'react-native';
import React, {useMemo, useState} from 'react';
import {JioIcon, JioText, useColor} from '@jio/rn_components';
import {
  IconColor,
  IconSize,
  JioTypography,
} from '@jio/rn_components/src/index.types';
import type {IconKey} from '@jio/rn_components/src/utils/IconUtility';
import {FlatList, TapGestureHandler} from 'react-native-gesture-handler';
import {
  FilterOptionType,
  FilterOptionValue,
  FilterOptionProps,
  FilterBtmSheetProps,
} from '../types/FilterBtmSheetType.d';
import {styles} from '../styles/FilterBtmSheetStyles';
import JMBtmSheetHeader from '../../../../../jiomart-general/src/ui/JMBtmSheetHeader';
import Divider, {
  DividerType,
} from '../../../../../jiomart-general/src/ui/Divider';
import {useMutation} from '@tanstack/react-query';
import {AppScreens} from '../../../../../jiomart-common/src/JMAppScreenEntry';
import JMProductNetworkController from '../../../../../jiomart-networkmanager/src/JMNetworkController/JMProductNetworkController';
import {mapFilterKeyValue} from '../utils';
import {capitalizeFirstLetter} from '../../../../../jiomart-common/src/utils/JMStringUtility';
import SearchBar from './SearchBar';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import {rw} from '../../../../../jiomart-common/src/JMResponsive';
import {
  formatCategoryName,
  generateFilterReq,
} from '../../../../../jiomart-common/src/utils/JMCommonFunctions';

const productController = new JMProductNetworkController();

const FilterBtmSheet = (props: FilterBtmSheetProps) => {
  const {
    config,
    screen,
    slug,
    params,
    selectedTab,
    data,
    onClose,
    onApply,
    onClearAll,
    onFilterTab,
    close,
  } = props;
  const white = useColor('primary_inverse');
  const grey20 = useColor('primary_grey_20');
  const grey40 = useColor('primary_grey_40');
  const primary50 = useColor('primary_50');
  const insets = useSafeAreaInsets();
  const [filter, setFilter] = useState(
    data ? JSON.parse(JSON.stringify(data)) : [],
  );
  const [searchQuery, setSearchQuery] = useState('');

  const fetchLatestFilter = useMutation({
    mutationFn: (req: any) => {
      if (AppScreens.PRODUCT_LISTING_SCREEN === screen) {
        return productController.fetchProductList(req);
      } else {
        return productController.fetchSearchProductList(req);
      }
    },
    onSuccess: res => {
      setFilter(res?.filters);
    },
  });

  const renderFilterTab = ({item}: {item: any; index: number}) => {
    if (
      item?.key?.kind === 'range' &&
      item?.key?.name === 'min_price_effective'
    ) {
      return null;
    }

    if (config?.removeFilterValues?.includes(item?.key?.name)) {
      return null;
    }
    if (
      config?.showFilterValues?.length > 0 &&
      !config?.showFilterValues?.includes(item?.key?.name)
    ) {
      return null;
    }

    const selectedCount = item?.values?.filter(
      (v: any) => v?.is_selected,
    )?.length;

    return (
      <TouchableOpacity
        style={[
          styles.tab,
          selectedTab === item.key.name
            ? {
                backgroundColor: white,
              }
            : null,
        ]}
        onPress={() => {
          onFilterTab?.(item.key.name);
          // setSearchQuery('');
        }}>
        <JioText
          text={item.key.display ?? ''}
          appearance={
            selectedTab === item.key.name
              ? JioTypography.BODY_XXS_BOLD
              : JioTypography.BODY_XXS
          }
          maxLines={2}
          color={selectedTab === item.key.name ? 'black' : 'primary_grey_80'}
          style={{flexShrink: 1}}
        />
        {selectedCount > 0 && <FilterBadge count={selectedCount} />}
      </TouchableOpacity>
    );
  };

  // const getFirstSelectedFilter = useMemo(() => {
  //   var index = 0;
  //   var indexCheck = true;
  //   var selectedFilter = null;
  //   while (indexCheck && index < data?.length) {
  //     if (removeFilterValues?.includes?.(data?.[index]?.key?.name)) {
  //       index = index + 1;
  //     } else {
  //       selectedFilter = data?.[index]?.key?.name;
  //       indexCheck = false;
  //     }
  //   }
  //   return selectedFilter;
  // }, [data, removeFilterValues]);

  // const firstSelectedFilter = getFirstSelectedFilter;

  // const currentSelectionsRef = useRef<{[key: string]: string[]}>({});

  // const [filter, setFilter] = useState(() => {
  //   const initialState = data?.map(f => ({
  //     ...f,
  //     values: f?.values?.map(v => ({
  //       ...v,
  //       is_selected: initialFilters[f.key.name]?.includes(v.value) || false,
  //     })),
  //   }));
  //   currentSelectionsRef.current = initialFilters;
  //   return initialState;
  // });

  const createDepartmentTree = data => {
    const departments =
      data?.find(f => f?.key?.name === config?.filterKey?.DEPARTMENT)?.values ||
      [];

    const categoryL1 =
      data?.find(f => f?.key?.name === config?.filterKey?.CATEGORY_L1)
        ?.values || [];

    if (!departments) {
      return;
    }

    const departmentTree = [];
    departments?.map(dept => {
      const tempDept = dept;
      tempDept.childs = [];
      categoryL1?.map(catL1 => {
        if (
          catL1?.value?.toLowerCase()?.startsWith(dept?.display?.toLowerCase())
        ) {
          tempDept.childs?.push({
            ...catL1,
            name: config?.filterKey?.CATEGORY_L1,
          });
        }
      });
      departmentTree?.push(tempDept);
    });

    return departmentTree;
  };

  // useEffect(() => {
  //   var tempData = data?.map(f => ({
  //     ...f,
  //     values: f?.values?.map(v => ({
  //       ...v,
  //       is_selected:
  //         currentSelectionsRef.current[f.key.name]?.includes(v.value) || false,
  //     })),
  //   }));

  //   if (isSLP) {
  //     const newDepartmentData = createDepartmentTree(tempData);

  //     setFilter(
  //       tempData?.map(f => {
  //         if (f?.key?.name === config?.filterKey?.DEPARTMENT) {
  //           return {
  //             ...f,
  //             values: newDepartmentData,
  //           };
  //         }

  //         return {
  //           ...f,
  //           values: f?.values,
  //         };
  //       }),
  //     );
  //   } else {
  //     setFilter(tempData);
  //   }
  // }, [data]);

  // useEffect(() => {
  //   currentSelectionsRef.current = initialFilters;
  // }, [initialFilters]);

  // const [selectedFilterTab, setSelectedFilterTab] = useState<string | null>(
  //   null,
  // );

  // useEffect(() => {
  //   const getSavedTab = async () => {
  //     try {
  //       const savedTab = await AsyncStorage.getItem('selectedFilterTab');
  //       if (savedTab) {
  //         const {key} = JSON.parse(savedTab);
  //         setSelectedFilterTab(key);
  //       } else {
  //         if (isBrandBtmSheet) {
  //           const brandIndex = data?.findIndex(
  //             f => f.key.name === config?.filterKey?.BRAND,
  //           );
  //           setSelectedFilterTab(
  //             brandIndex !== -1
  //               ? data?.[brandIndex]?.key?.name
  //               : firstSelectedFilter,
  //           );
  //         } else if (leftFilterValues.length > 0) {
  //           const brandIndex = data?.findIndex(
  //             f => f.key.name === leftFilterValues[0],
  //           );
  //           setSelectedFilterTab(
  //             brandIndex !== -1
  //               ? data?.[brandIndex]?.key?.name
  //               : firstSelectedFilter,
  //           );
  //         } else {
  //           setSelectedFilterTab(firstSelectedFilter);
  //         }
  //       }
  //     } catch (e) {
  //       console.error('Error loading selected filter tab:', e);
  //     }
  //   };

  //   getSavedTab();
  // }, []);

  //
  //
  // const selectedFilter = filter?.find(f => {
  //   // if (selectedFilterTab === 'Category' && isL3CategoryNames) {
  //   //   return f.key.name === 'l3_category_names';
  //   // }
  //   return f.key.name === selectedFilterTab;
  // });

  const selectedFilterTabValue = filter?.find(f => f.key.name === selectedTab);

  const filteredValues = useMemo(() => {
    let values = selectedFilterTabValue?.values;

    switch (selectedTab) {
      case 'departments':
        return createDepartmentTree(filter);
      default:
        // Then sort the filtered results
        return values.sort((a: any, b: any) => {
          // If both items have same selection status, sort alphabetically by display value
          if (a?.is_selected === b?.is_selected) {
            return capitalizeFirstLetter(
              formatCategoryName(a?.display),
            ).localeCompare(
              capitalizeFirstLetter(formatCategoryName(b?.display)),
            );
          }
          // If selection status differs, selected items come first
          return a.is_selected ? -1 : 1;
        });
    }

    // const values = selectedFilter?.values || [];

    // First apply the search filter if there's a query
    // const filteredBySearch = !searchQuery.trim()
    //   ? values
    //   : values.filter((value: any) =>
    //       value.display?.toLowerCase()?.includes(searchQuery?.toLowerCase()),
    //     );
  }, [
    createDepartmentTree,
    filter,
    selectedFilterTabValue?.values,
    selectedTab,
  ]);

  const handleSearch = (text: string) => {
    setSearchQuery(text);
  };

  const handleFilterSelect = (
    keyName: string,
    value: string,
    isAccordion: boolean = false,
  ) => {
    setFilter(prev => {
      const updatedFilters = [...prev];
      const filterIndex = updatedFilters?.findIndex(
        f => f.key.name === keyName,
      );

      if (filterIndex > -1) {
        const filter = updatedFilters[filterIndex];
        const valueIndex = filter?.values?.findIndex(v => v.value === value);

        if (isAccordion) {
          filter?.values?.map((val, index) => {
            if (index != valueIndex) {
              val.is_selected = false;
            }
          });
        }

        if (valueIndex > -1) {
          filter.values[valueIndex].is_selected =
            !filter.values[valueIndex].is_selected;
        }

        if (
          Object?.keys(filter?.values?.[0])?.includes('childs') &&
          !filter?.values?.[valueIndex]?.is_selected
        ) {
          const childKeyName = filter?.values?.[0]?.childs?.[0]?.name;
          const childFilterIndex = updatedFilters?.findIndex(
            f => f?.key?.name === childKeyName,
          );
          if (childFilterIndex > -1) {
            var childFilter = updatedFilters?.[childFilterIndex];
            childFilter?.values?.map((val, index) => {
              val.is_selected = false;
            });
          }
        }
      }

      const f = generateFilterReq(mapFilterKeyValue(updatedFilters));

      fetchLatestFilter.mutate({
        slug: slug,
        params: {
          ...params,
          f,
        },
      });
      return updatedFilters;
    });
  };

  const renderFilterOption = ({
    item: value,
    index,
  }: {
    item: any;
    index: number;
  }) => {
    switch (selectedTab) {
      case 'departments':
        return (
          <AccordionSection
            key={`accordion-${index}`}
            title={value?.display}
            data={value?.childs}
            onPress={() => {
              handleFilterSelect(`${selectedTab}`, value?.value, true);
            }}
            onChildPress={val => {
              handleFilterSelect(val?.name, val?.value, true); //l2
            }}
            isSelected={value?.is_selected && value?.childs?.length > 0}
          />
        );
      default:
        if (!value?.display) {
          return null;
        }
        return (
          <FilterOption
            key={`value-${index}`}
            title={capitalizeFirstLetter(formatCategoryName(value?.display))}
            type={FilterOptionType.CHECKBOX}
            value={
              value.is_selected
                ? FilterOptionValue.SELECTED
                : FilterOptionValue.UNSELECTED
            }
            onPress={() => {
              handleFilterSelect(`${selectedTab}`, value.value);
            }}
          />
        );
    }
  };
  const handleClearAll = () => {
    fetchLatestFilter.mutate({
      slug: slug,
      params: {
        ...params,
        f: '',
      },
    });
    onClearAll?.('');
  };
  const handleApply = () => {
    close?.(() => onApply?.(generateFilterReq(mapFilterKeyValue(filter))));
  };

  return (
    <View style={{flex: 1, paddingBottom: insets.bottom}}>
      <JMBtmSheetHeader
        title={config?.headerTitle ?? ''}
        onPress={() => {
          close?.(onClose);
        }}
      />
      <View style={styles.filterContainer}>
        <View style={[styles.leftPanel, {backgroundColor: grey20}]}>
          <FlatList
            data={filter}
            renderItem={renderFilterTab}
            keyExtractor={(_, index) => `filter-tab-${index}`}
            showsVerticalScrollIndicator={false}
            bounces={false}
            initialNumToRender={20}
          />
        </View>
        <View style={styles.rightPanel}>
          {selectedFilterTabValue?.values?.length >
            config?.shouldShowSearchBar && (
            <SearchBar
              onSubmit={() => {
                handleSearch();
              }}
            />
          )}
          {filteredValues.length > 0 ? (
            <FlatList
              data={filteredValues}
              renderItem={renderFilterOption}
              keyExtractor={(_, index) => `filter-option-${index}`}
              showsVerticalScrollIndicator={false}
              bounces={false}
              initialNumToRender={20}
              contentContainerStyle={styles.rightPanelContent}
            />
          ) : (
            <JioText
              text={config?.notFound}
              appearance={JioTypography.BODY_XS}
              color="black"
              style={[styles.rightPanelContent]}
            />
          )}
        </View>
      </View>
      <Divider type={DividerType.THIN} />
      <View style={styles.filterFooter}>
        {config?.button?.clearAll?.isVisible ? (
          <TouchableOpacity
            style={[styles.clear, {borderColor: grey40}]}
            onPress={handleClearAll}>
            <JioText
              text={config?.button?.clearAll?.title ?? ''}
              appearance={JioTypography.BUTTON}
              color="primary_60"
            />
          </TouchableOpacity>
        ) : null}
        {config?.button?.apply?.isVisible ? (
          <TouchableOpacity
            style={[styles.apply, {backgroundColor: primary50}]}
            onPress={handleApply}>
            <JioText
              text={config?.button?.apply?.title ?? ''}
              appearance={JioTypography.BUTTON}
              color="primary_inverse"
            />
          </TouchableOpacity>
        ) : null}
      </View>
    </View>
  );
};

const AccordionSection = ({
  title,
  data,
  onPress,
  onChildPress,
  isSelected,
}: {
  title: string;
  data: any[];
  onPress: () => void;
  onChildPress: (val: any) => void;
  isSelected: boolean;
}) => {
  return (
    <>
      <TouchableOpacity
        onPress={onPress}
        style={{
          flexDirection: 'row',
          justifyContent: 'space-between',
          padding: 10,
          borderBottomWidth: 1,
          borderBottomColor: 'rgba(224, 224, 224, 1)',
        }}>
        <JioText
          text={title}
          appearance={JioTypography.BODY_XS}
          color="primary_grey_100"
        />
        <JioIcon
          ic={isSelected ? 'IcChevronUp' : 'IcChevronDown'}
          size={IconSize.MEDIUM}
          color={IconColor.PRIMARY60}
        />
      </TouchableOpacity>

      {isSelected && (
        <View style={{paddingLeft: rw(16)}}>
          {data?.map((item, idx) => {
            return (
              <TouchableOpacity
                onPress={() => {
                  onChildPress?.(item);
                }}
                style={{padding: 2}}>
                <JioText
                  text={item?.display?.split('::')?.[1]}
                  appearance={JioTypography.BODY_XS}
                  color={'primary_grey_80'}
                  style={{fontWeight: item?.is_selected ? 'bold' : 'normal'}}
                />
              </TouchableOpacity>
            );
          })}
        </View>
      )}
    </>
  );
};

const FilterOption = (props: FilterOptionProps) => {
  const {
    title,
    rightIcon,
    showRightIcon,
    type = FilterOptionType.CHECKBOX,
    value,
    style,
    disabled,
    onPress,
  } = props;

  const primary50 = useColor('primary_50');
  const grey80 = useColor('primary_grey_80');

  return title ? (
    <>
      <TapGestureHandler onActivated={onPress} disabled={disabled}>
        <View style={[styles.filterOption, disabled ? styles.disabled : null]}>
          {(() => {
            switch (type) {
              case FilterOptionType.CHECKBOX:
                switch (value) {
                  case FilterOptionValue.SELECTED:
                    return (
                      <View
                        style={[styles.checkbox, {backgroundColor: primary50}]}>
                        <JioIcon
                          ic="IcConfirm"
                          size={IconSize.XS}
                          color={IconColor.INVERSE}
                        />
                      </View>
                    );
                  case FilterOptionValue.UNSELECTED:
                    return <View style={styles.unSelectedCheckbox} />;
                  case FilterOptionValue.PARTIAL:
                    return (
                      <View
                        style={[styles.checkbox, {backgroundColor: primary50}]}>
                        <JioIcon
                          ic="IcMinus"
                          size={IconSize.XS}
                          color={IconColor.INVERSE}
                        />
                      </View>
                    );
                  default:
                    return null;
                }
              case FilterOptionType.RADIO:
                switch (value) {
                  case FilterOptionValue.SELECTED:
                    return (
                      <View
                        style={[styles.selectedRadio, {borderColor: primary50}]}
                      />
                    );
                  case FilterOptionValue.UNSELECTED:
                    return (
                      <View
                        style={[styles.unSelectedRadio, {borderColor: grey80}]}
                      />
                    );
                  default:
                    return null;
                }
              default:
                return null;
            }
          })()}

          <JioText
            text={title ?? ''}
            appearance={JioTypography.BODY_XXS}
            color={
              value === FilterOptionValue.SELECTED ||
              value === FilterOptionValue.PARTIAL
                ? 'primary_grey_100'
                : 'primary_grey_80'
            }
            style={styles.title}
            maxLines={1}
          />

          {showRightIcon && rightIcon ? (
            <JioIcon
              ic={rightIcon.ic as IconKey}
              color={rightIcon.color as IconColor}
              size={rightIcon.size as IconSize}
              style={styles.rightIcon}
            />
          ) : null}
        </View>
      </TapGestureHandler>
      <Divider type={DividerType.THIN} />
    </>
  ) : null;
};

const FilterBadge = ({count}: {count: number}) => {
  return (
    <View style={styles.badge}>
      <JioText
        text={count.toString()}
        appearance={JioTypography.BODY_XXS}
        color={'primary_inverse'}
      />
    </View>
  );
};

export default FilterBtmSheet;
