import React from 'react';
import type {ProductSearchListingScreenProps} from '../types/ProductSearchListingScreenType';
import useProductSearchListingScreenController from '../controllers/useProductSearchListingScreenController';
import ScreenSlot, {
  DeeplinkHandler,
} from '../../../../../jiomart-general/src/ui/JMScreenSlot';
import ProductListingView from '../views/ProductListingView';
import BottomSheet from '../../../../../jiomart-general/src/ui/BottomSheet/BottomSheet';
import SortBtmSheet from '../components/SortBtmSheet';
import FilterBtmSheet from '../components/FilterBtmSheet';
import {rh} from '../../../../../jiomart-common/src/JMResponsive';
import {AppScreens} from '../../../../../jiomart-common/src/JMAppScreenEntry';
import {countSelectedFilterTabs} from '../utils';
import MultiVariantView from '../components/MultiVariantView';

const ProductSearchListingScreen = (props: ProductSearchListingScreenProps) => {
  const {
    navigation,
    navigationBean,
    params,
    productList,
    isTransitionComplete,
    sortOptions,
    filterOption,
    productListRes,
    config,
    selectedSort,
    sortBtmSheet,
    openSortBtmSheet,
    closeSortBtmSheet,
    filterBtmSheet,
    openFilterBtmSheet,
    closeFilterBtmSheet,
    multiVariantBtmSheet,
    openMultiVariantBtmSheet,
    closeMultiVariantBtmSheet,
    selectedVariants,
    handleSelectedSort,
    l4CategoryData,
    handleLoadMore,
    listRef,
    handleMultiSearchQuery,
    sortedMultiSearchQuery,
    handleApplyFilter,
    handleClearAllFilter,
    filterTab,
    setFilterTab,
    handleRedirectProductPage,
  } = useProductSearchListingScreenController(props);

  return (
    <DeeplinkHandler
      navigationBean={navigationBean}
      navigation={navigation}
      children={_ => {
        return (
          <ScreenSlot
            navigationBean={navigationBean}
            navigation={navigation}
            bottomSheetContent={
              <>
                <BottomSheet
                  visible={sortBtmSheet}
                  onBackDropClick={closeSortBtmSheet}
                  onDrag={closeSortBtmSheet}>
                  <SortBtmSheet
                    onClose={closeSortBtmSheet}
                    data={sortOptions}
                    title={config?.bottomSheet?.sort?.headerTitle ?? ''}
                    defaultSelected={selectedSort?.display}
                    onSelectedSort={handleSelectedSort}
                  />
                </BottomSheet>
                <BottomSheet
                  visible={filterBtmSheet}
                  maxHeightPercent={95}
                  isStatic
                  staticHeight={rh(650)}
                  onBackDropClick={closeFilterBtmSheet}
                  onDrag={closeFilterBtmSheet}>
                  <FilterBtmSheet
                    data={filterOption}
                    screen={AppScreens.PRODUCT_SEARCH_LISTING_SCREEN}
                    params={params}
                    selectedTab={filterTab}
                    onFilterTab={val => {
                      setFilterTab(val);
                    }}
                    onClose={closeFilterBtmSheet}
                    onApply={handleApplyFilter}
                    onClearAll={handleClearAllFilter}
                    config={config?.bottomSheet?.filter}
                  />
                </BottomSheet>
                <BottomSheet
                  visible={multiVariantBtmSheet}
                  onBackDropClick={closeMultiVariantBtmSheet}
                  onDrag={closeMultiVariantBtmSheet}>
                  <MultiVariantView
                    onClose={closeMultiVariantBtmSheet}
                    verticalCode={'GROCERIES'}
                    config={config?.bottomSheet?.multiVariant}
                    handleImagePress={data => {
                      handleRedirectProductPage(
                        data?.packetItem?.slug,
                        config?.bottomSheet?.multiVariant?.cta,
                      );
                    }}
                    packets={selectedVariants}
                  />
                </BottomSheet>
              </>
            }
            children={() => {
              return (
                <ProductListingView
                  onListRef={ref => {
                    listRef.current = ref;
                  }}
                  loading={productListRes.isLoading || !isTransitionComplete}
                  config={config}
                  filterAndSortBar={{
                    config,
                    text: config?.search?.text,
                    isSortApplied: selectedSort?.value !== 'popular',
                    onSortPress: openSortBtmSheet,
                    onFilterPress: openFilterBtmSheet,
                    appliedFilters: countSelectedFilterTabs(filterOption),
                  }}
                  data={productList}
                  l4CategoryData={l4CategoryData}
                  onLoadMore={handleLoadMore}
                  hasNextPageDataIsLoading={productListRes.isFetchingNextPage}
                  multiSearchQuery={sortedMultiSearchQuery()}
                  onMultiVariantPress={openMultiVariantBtmSheet}
                  onProductCardPress={data => {
                    handleRedirectProductPage(
                      data?.slug,
                      config?.gridCard?.cta,
                    );
                  }}
                  onPressMultiSearchQuery={handleMultiSearchQuery}
                />
              );
            }}
          />
        );
      }}
    />
  );
};

export default ProductSearchListingScreen;
