import React from 'react';
import type {ProductListingScreenProps} from '../types/ProductListingScreenType';
import useProductListingScreenController from '../controllers/useProductListingScreenController';
import ScreenSlot, {
  DeeplinkHandler,
} from '../../../../../jiomart-general/src/ui/JMScreenSlot';
import ProductListingView from '../views/ProductListingView';
import ProductGridListingView from '../views/ProductGridListingView';
import BottomSheet from '../../../../../jiomart-general/src/ui/BottomSheet/BottomSheet';
import JMBtmSheetHeader from '../../../../../jiomart-general/src/ui/JMBtmSheetHeader';
import CategoryBottomSheet from '../../../components/Category/CategoryBottomSheet';
import {countSelectedFilterTabs} from '../utils';
import SortBtmSheet from '../components/SortBtmSheet';
import {navigateTo} from '../../../../../jiomart-general/src/navigation/JMNavGraph';
import {navBeanObj} from '../../../../../jiomart-common/src/JMNavGraphUtil';
import {rh} from '../../../../../jiomart-common/src/JMResponsive';
import FilterBtmSheet from '../components/FilterBtmSheet';
import {AppScreens} from '../../../../../jiomart-common/src/JMAppScreenEntry';
import {
  formatCategoryName,
  generateFilterReq,
} from '../../../../../jiomart-common/src/utils/JMCommonFunctions';
import MultiVariantView from '../components/MultiVariantView';

const ProductListingScreen = (props: ProductListingScreenProps) => {
  const {
    navigation,
    navigationBean,
    slug,
    params,
    page,
    productList,
    isTransitionComplete,
    sortOptions,
    filterOption,
    productListRes,
    config,
    isGrid,
    selectedSort,
    category,
    categoryBtmSheet,
    openCategoryBtmSheet,
    closeCategoryBtmSheet,
    sortBtmSheet,
    openSortBtmSheet,
    closeSortBtmSheet,
    filterBtmSheet,
    openFilterBtmSheet,
    closeFilterBtmSheet,
    multiVariantBtmSheet,
    openMultiVariantBtmSheet,
    closeMultiVariantBtmSheet,
    selectedVariants,
    getCategoryTitle,
    selectedL1CategoryData,
    selectedL3CategoryData,
    fetchL3CategoryData,
    handleSelectedSort,
    l4CategoryData,
    handleLoadMore,
    listRef,
    scrollToTop,
    handleApplyFilter,
    handleClearAllFilter,
    filterTab,
    setFilterTab,
    handleRedirectProductPage,
  } = useProductListingScreenController(props);
  return (
    <DeeplinkHandler
      navigationBean={navigationBean}
      navigation={navigation}
      children={_ => {
        return (
          <ScreenSlot
            navigationBean={navigationBean}
            navigation={navigation}
            bottomSheetContent={
              <>
                <BottomSheet
                  maxHeightPercent={90}
                  visible={categoryBtmSheet}
                  onBackDropClick={closeCategoryBtmSheet}
                  onDrag={closeCategoryBtmSheet}
                  enableScroll
                  isStretchEnabled
                  headerComponent={
                    <JMBtmSheetHeader
                      title={
                        getCategoryTitle(category.data, page?.department) ?? ''
                      }
                      onPress={closeCategoryBtmSheet}
                    />
                  }>
                  <CategoryBottomSheet
                    allCategory={category.data}
                    selectedL1Category={page?.department}
                    selectedL3Category={page?.category}
                    closeCatBtmSheet={closeCategoryBtmSheet}
                    onPressL3Category={data => {
                      navigateTo(
                        navBeanObj({
                          ...config?.bottomSheet?.category?.cta,
                          params: {
                            slug: data?.slug,
                            page: {
                              department: data?.department?.[0],
                              category: data?.slug,
                            },
                          },
                        }),
                        navigation,
                      );
                      scrollToTop();
                    }}
                  />
                </BottomSheet>
                <BottomSheet
                  visible={sortBtmSheet}
                  onBackDropClick={closeSortBtmSheet}
                  onDrag={closeSortBtmSheet}>
                  <SortBtmSheet
                    onClose={closeSortBtmSheet}
                    data={sortOptions}
                    title={config?.bottomSheet?.sort?.headerTitle ?? ''}
                    defaultSelected={selectedSort?.display}
                    onSelectedSort={handleSelectedSort}
                  />
                </BottomSheet>
                <BottomSheet
                  visible={filterBtmSheet}
                  maxHeightPercent={95}
                  isStatic
                  staticHeight={rh(650)}
                  onBackDropClick={closeFilterBtmSheet}
                  onDrag={closeFilterBtmSheet}>
                  <FilterBtmSheet
                    data={filterOption}
                    screen={AppScreens.PRODUCT_LISTING_SCREEN}
                    slug={slug}
                    params={params}
                    selectedTab={filterTab}
                    onFilterTab={val => {
                      setFilterTab(val);
                    }}
                    onClose={closeFilterBtmSheet}
                    onApply={handleApplyFilter}
                    onClearAll={handleClearAllFilter}
                    config={config?.bottomSheet?.filter}
                  />
                </BottomSheet>
                <BottomSheet
                  visible={multiVariantBtmSheet}
                  onBackDropClick={closeMultiVariantBtmSheet}
                  onDrag={closeMultiVariantBtmSheet}>
                  <MultiVariantView
                    onClose={closeMultiVariantBtmSheet}
                    verticalCode={'GROCERIES'}
                    config={config?.bottomSheet?.multiVariant}
                    handleImagePress={data => {
                      handleRedirectProductPage(
                        data?.packetItem?.slug,
                        config?.bottomSheet?.multiVariant?.cta,
                      );
                    }}
                    packets={selectedVariants}
                  />
                </BottomSheet>
              </>
            }
            children={() => {
              return isGrid ? (
                <ProductGridListingView
                  onListRef={ref => {
                    listRef.current = ref;
                  }}
                  loading={productListRes.isLoading}
                  config={config}
                  filterAndSortBar={{
                    config,
                    text:
                      formatCategoryName(selectedL3CategoryData?.name) ||
                      formatCategoryName(selectedL1CategoryData?.name),
                    isSortApplied: selectedSort?.value !== 'popular',
                    onTextPress: openCategoryBtmSheet,
                    onSortPress: openSortBtmSheet,
                    onFilterPress: openFilterBtmSheet,
                    appliedFilters: countSelectedFilterTabs(filterOption),
                  }}
                  data={productList}
                  department={page?.department}
                  l3CategoryData={fetchL3CategoryData}
                  l4CategoryData={l4CategoryData}
                  selectedL3Category={selectedL3CategoryData}
                  slug={slug}
                  onLoadMore={handleLoadMore}
                  onl3CategoryPress={data => {
                    navigateTo(
                      navBeanObj({
                        ...config?.categoryList?.cta,
                        params: {
                          slug: data?.slug,
                          page: {
                            department: data?.department?.[0],
                            category: data?.slug,
                          },
                        },
                      }),
                      navigation,
                    );
                  }}
                  onl4CategoryPress={data => {
                    handleApplyFilter(
                      generateFilterReq({
                        l3_category_names: [data?.value],
                      }),
                    );
                  }}
                  onMultiVariantPress={openMultiVariantBtmSheet}
                  onProductCardPress={data => {
                    handleRedirectProductPage(
                      data?.slug,
                      config?.gridCard?.cta,
                    );
                  }}
                  hasNextPageDataIsLoading={productListRes.isFetchingNextPage}
                />
              ) : (
                <ProductListingView
                  onListRef={ref => {
                    listRef.current = ref;
                  }}
                  loading={productListRes.isLoading || !isTransitionComplete}
                  config={config}
                  filterAndSortBar={{
                    config,
                    text:
                      formatCategoryName(selectedL3CategoryData?.name) ||
                      formatCategoryName(selectedL1CategoryData?.name),
                    isSortApplied: selectedSort?.value !== 'popular',
                    onTextPress: openCategoryBtmSheet,
                    onSortPress: openSortBtmSheet,
                    onFilterPress: openFilterBtmSheet,
                    appliedFilters: countSelectedFilterTabs(filterOption),
                  }}
                  data={productList}
                  department={page?.department}
                  l3CategoryData={fetchL3CategoryData}
                  l4CategoryData={l4CategoryData}
                  selectedL3Category={selectedL3CategoryData}
                  slug={slug}
                  onLoadMore={handleLoadMore}
                  onl3CategoryPress={data => {
                    navigateTo(
                      navBeanObj({
                        ...config?.categoryList?.cta,
                        params: {
                          slug: data?.slug,
                          page: {
                            department: data?.department?.[0],
                            category: data?.slug,
                          },
                        },
                      }),
                      navigation,
                    );
                  }}
                  onl4CategoryPress={data => {
                    handleApplyFilter(
                      generateFilterReq({
                        l3_category_names: [data?.value],
                      }),
                    );
                  }}
                  onMultiVariantPress={openMultiVariantBtmSheet}
                  onProductCardPress={data => {
                    handleRedirectProductPage(
                      data?.slug,
                      config?.gridCard?.cta,
                    );
                  }}
                  hasNextPageDataIsLoading={productListRes.isFetchingNextPage}
                />
              );
            }}
          />
        );
      }}
    />
  );
};

export default ProductListingScreen;
