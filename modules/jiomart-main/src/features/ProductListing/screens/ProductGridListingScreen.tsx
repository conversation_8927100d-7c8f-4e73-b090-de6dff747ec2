import {Platform, StyleSheet, View} from 'react-native';
import React from 'react';
import useProductGridListingScreenController from '../controllers/useProductGridListingScreenController';
import type {ProductGridListingScreenProps} from '../types/ProductGridListingScreenType';
import ScreenSlot, {
  DeeplinkHandler,
} from '../../../../../jiomart-general/src/ui/JMScreenSlot';
import BottomSheet from '@jm/jiomart-general/src/ui/BottomSheet/BottomSheet';
import SortBtmSheet from '../components/SortBtmSheet';
import FilterBtmSheet from '../components/FilterBtmSheet';
import MultiVariantView from '../components/MultiVariantView';
import SubCategoriesView from '../components/SubCategoriesView';
import {FlashList} from '@shopify/flash-list';
import Animated from 'react-native-reanimated';
import JMFab from '../components/JMFab';
import {JioText} from '@jio/rn_components';
import GroceriesTopBar from '../components/GroceriesTopBar';
import NegativeScreenUI from '@jm/jiomart-general/src/ui/NegativeScreenUI';
import {JioTypography} from '@jio/rn_components/src/index.types';
import {formatCategoryName} from '@jm/jiomart-common/src/utils/JMCommonFunctions';
import {rh} from '@jm/jiomart-common/src/JMResponsive';
import {AppScreens} from '@jm/jiomart-common/src/JMAppScreenEntry';

const ProductGridListingScreen = (props: ProductGridListingScreenProps) => {
  const {
    navigation,
    navigationBean,
    config,
    slug,
    params,
    sortOptions,
    selectedSort,
    sortBtmSheet,
    openSortBtmSheet,
    closeSortBtmSheet,
    filterOption,
    filterBtmSheet,
    openFilterBtmSheet,
    closeFilterBtmSheet,
    brandBtmSheet,
    openBrandBtmSheet,
    closeBrandBtmSheet,
    selectedVariants,
    openMultiVariantBtmSheet,
    closeMultiVariantBtmSheet,
    multiVariantBtmSheet,
    handleSelectedSort,
    scrollToTop,
    categoryL2Title,
    listRef,
    animatedCatHeaderTitle,
    l4CategoryData,
    allImage,
    fabAnimatedStyle,
    filterTab,
    setFilterTab,
    handleApplyFilter,
    handleClearAllFilter,
    handleRedirectProductPage,
  } = useProductGridListingScreenController(props);
  return (
    <DeeplinkHandler
      navigationBean={navigationBean}
      navigation={navigation}
      children={_ => {
        return (
          <ScreenSlot
            navigationBean={navigationBean}
            navigation={navigation}
            bottomSheetContent={
              <>
                <BottomSheet
                  visible={sortBtmSheet}
                  onBackDropClick={closeSortBtmSheet}
                  onDrag={closeSortBtmSheet}>
                  <SortBtmSheet
                    onClose={closeSortBtmSheet}
                    data={sortOptions}
                    title={config?.bottomSheet?.sort?.headerTitle ?? ''}
                    defaultSelected={selectedSort?.display}
                    onSelectedSort={handleSelectedSort}
                  />
                </BottomSheet>
                <BottomSheet
                  visible={filterBtmSheet}
                  maxHeightPercent={95}
                  isStatic
                  staticHeight={rh(650)}
                  onBackDropClick={closeFilterBtmSheet}
                  onDrag={closeFilterBtmSheet}>
                  <FilterBtmSheet
                    data={filterOption}
                    screen={AppScreens.PRODUCT_LISTING_SCREEN}
                    slug={slug}
                    params={params}
                    selectedTab={filterTab}
                    onFilterTab={val => {
                      setFilterTab(val);
                    }}
                    onClose={closeFilterBtmSheet}
                    onApply={handleApplyFilter}
                    onClearAll={handleClearAllFilter}
                    config={config?.bottomSheet?.filter}
                  />
                </BottomSheet>
                <BottomSheet
                  visible={brandBtmSheet}
                  maxHeightPercent={95}
                  isStatic
                  staticHeight={rh(650)}
                  onBackDropClick={closeBrandBtmSheet}
                  onDrag={closeBrandBtmSheet}>
                  <FilterBtmSheet
                    data={filterOption}
                    screen={AppScreens.PRODUCT_LISTING_SCREEN}
                    slug={slug}
                    params={params}
                    selectedTab={filterTab}
                    onFilterTab={val => {
                      setFilterTab(val);
                    }}
                    onClose={closeBrandBtmSheet}
                    onApply={handleApplyFilter}
                    onClearAll={handleClearAllFilter}
                    config={config?.bottomSheet?.brand}
                  />
                </BottomSheet>
                <BottomSheet
                  visible={multiVariantBtmSheet}
                  onBackDropClick={closeMultiVariantBtmSheet}
                  onDrag={closeMultiVariantBtmSheet}>
                  <MultiVariantView
                    onClose={closeMultiVariantBtmSheet}
                    verticalCode={'GROCERIES'}
                    config={config?.bottomSheet?.multiVariant}
                    handleImagePress={data => {
                      handleRedirectProductPage(
                        data?.packetItem?.slug,
                        config?.bottomSheet?.multiVariant?.cta,
                      );
                    }}
                    packets={selectedVariants}
                  />
                </BottomSheet>
              </>
            }
            children={() => {
              return (
                <>
                  {!config?.disableCategoryName &&
                    categoryL2Title?.length > 0 && (
                      <>
                        <Animated.View
                          style={[
                            animatedCatHeaderTitle,
                            styles.categoryTitle,
                            {overflow: 'hidden'},
                          ]}>
                          <JioText
                            text={formatCategoryName(
                              categoryL2Title?.[0]?.display,
                            )}
                            appearance={JioTypography.HEADING_XXS}
                            style={{
                              flex: 1,
                              alignSelf: 'center',
                              textAlignVertical: 'center',
                              marginTop: Platform.OS == 'ios' ? 10 : 0,
                            }}
                          />
                        </Animated.View>
                      </>
                    )}
                  <View style={styles.container}>
                    {/* <SubCategoriesView
                      data={l4CategoryData}
                      onCategorySelect={handleCategorySelect}
                      allImage={allImage}
                      resetCategoryAnimation={resetCategoryAnimation}
                      setResetCategoryAnimation={setResetCategoryAnimation}
                    />
                    <View style={styles.listContainer}>
                      {!noItemsFound ? (
                        <GroceriesTopBar
                          openFilterBtmSheet={handleFilterPress}
                          openSortBtmSheet={openSortBtmSheet}
                          openBrandBtmSheet={handleBrandPress}
                          selSortFilterName={selectedSort?.display}
                          selBrandName={selectedFilters?.brand?.[0] || ''}
                          readFilterDataUtil={handleApplyFilter}
                          animateL2Header={animateL2HeaderGrid}
                          appliedFilters={selectedFilters}
                          config={config}
                        />
                      ) : null}
                      {isLoadingFilters ? (
                        <GroceryGridShimmer />
                      ) : noItemsFound ? (
                        <NegativeScreenUI
                          imageUrl={
                            config?.negativeCases?.noItemFound?.imageUrl
                          }
                          title={
                            config?.negativeCases?.noItemFound?.title ?? ''
                          }
                          subTitle={
                            config?.negativeCases?.noItemFound?.subTitle ?? ''
                          }
                          isButtonVisible={true}
                          buttonTitle={
                            config?.negativeCases?.noItemFound?.buttonTitle
                          }
                          onPress={() => {
                            JMNavigation({
                              navigationType: NavigationType.GO_BACK,
                            });
                          }}
                        />
                      ) : (
                        <FlashList
                          ref={listRef}
                          data={products}
                          scrollEnabled={products?.length > 2}
                          renderItem={renderItem}
                          numColumns={2}
                          showsVerticalScrollIndicator={false}
                          estimatedItemSize={290}
                          onScroll={handleScrollGrid}
                          onEndReached={handleLoadMore}
                          onEndReachedThreshold={0.5}
                          ListFooterComponent={
                            isLoading ? (
                              <View style={styles.footer}>
                                <ActivityIndicator
                                  size="large"
                                  color="#0078AD"
                                />
                              </View>
                            ) : null
                          }
                          contentContainerStyle={{
                            paddingHorizontal: 8,
                            paddingTop: 10,
                            paddingBottom: 120,
                          }}
                        />
                      )}
                    </View> */}
                    {!config?.disableFabIcon && (
                      // !noItemsFound &&
                      <Animated.View style={fabAnimatedStyle}>
                        <JMFab style={{bottom: 65}} onPress={scrollToTop} />
                      </Animated.View>
                    )}
                  </View>
                </>
              );
            }}
          />
        );
      }}
    />
  );
};

export default ProductGridListingScreen;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#EFEFEF',
    flexDirection: 'row',
    zIndex: 10,
  },
  negativeScreenContainer: {flex: 1, backgroundColor: '#EFEFEF'},
  listContainer: {
    flex: 1,
  },
  footer: {
    padding: 16,
    alignItems: 'center',
  },
  categoryTitle: {
    zIndex: -1000000,
    backgroundColor: '#ffff',
  },
});
