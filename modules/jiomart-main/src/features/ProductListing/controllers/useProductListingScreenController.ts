import {JMConfigFileName} from '../../../../../jiomart-networkmanager/src/JMConfigFileManager/JMFileName';
import {useConfigFile} from '../../../../../jiomart-general/src/hooks/useJMConfig';
import type {UseProductListingScreenProps} from '../types/ProductListingScreenType';
import {useEffect, useRef, useState} from 'react';
import {useInfiniteQuery, useQuery} from '@tanstack/react-query';
import {RQKey} from '../../.../../../../../jiomart-common/src/JMConstants';
import JMProductNetworkController from '../../../../../jiomart-networkmanager/src/JMNetworkController/JMProductNetworkController';
import useAllCategoriesViewModel from '../../AllCategories/viewModel/useAllCategoriesViewModel';
import {
  fetchL3Category,
  selectedL1Category,
  selectedL3Category,
} from '../utils';
import {formatCategoryName} from '../../../../../jiomart-common/src/utils/JMCommonFunctions';
import {navigateTo} from '@jm/jiomart-general/src/navigation/JMNavGraph';
import {
  navBeanObj,
  type NavigationBean,
} from '@jm/jiomart-common/src/JMNavGraphUtil';
import {getBaseURL} from '@jm/jiomart-networkmanager/src/JMEnvironmentConfig';
import useTransitionState from '@jm/jiomart-general/src/hooks/useTransitionState';

const productController = new JMProductNetworkController();

const useProductListingScreenController = (
  props: UseProductListingScreenProps,
) => {
  const {getAllCategoryApi} = useAllCategoriesViewModel();
  const {route, navigation} = props;
  const initialSortApplied = {
    value: 'popular',
    logo: 'https://cdn.pixelbin.io/v2/jio-mart-2/MOluAr/original/rhos-sit/misc/default-assets/original/popular.png',
    display: 'Popularity',
    priority: 1,
    name: 'Popularity',
    is_selected: false,
  };

  const slug = route?.params?.params?.slug;
  const page = route?.params?.params?.page;
  const sort = route?.params?.params?.sort || initialSortApplied?.value;
  const filter = route?.params?.params?.filter || '';

  const isTransitionComplete = useTransitionState();
  const config = useConfigFile(JMConfigFileName.JMPLPConfigFileName)?.data
    ?.data;

  const isGrid = config?.gridListDepartment?.includes(page?.department);

  const [params, setParams] = useState({
    page_id: '*',
    page_size: 20,
    sort_on: sort,
    f: filter,
  });
  const [selectedSort, setSelectedSort] = useState<any>(initialSortApplied);
  const [selectedVariants, setSelectedVariants] = useState<any[]>([]);
  // bottomsheet state
  const [categoryBtmSheet, setCategoryBtmSheet] = useState(false);
  const [sortBtmSheet, setSortBtmSheet] = useState(false);
  const [filterBtmSheet, setFilterBtmSheet] = useState(false);
  const [multiVariantBtmSheet, setMultiVariantBtmSheet] = useState(false);
  const listRef = useRef<any>(null);

  const productListRes = useInfiniteQuery({
    queryKey: [
      RQKey.PRODUCT_LIST,
      params.page_id,
      params.f,
      params.sort_on,
      slug,
    ],
    queryFn: ({pageParam = '*'}) => {
      return productController.fetchProductList({
        slug: slug,
        params: {
          ...params,
          page_id: pageParam,
        },
      });
    },
    gcTime: 0,
    initialPageParam: '*',
    placeholderData: (previousData, _) => previousData,
    enabled: !!(slug && isTransitionComplete),
    getNextPageParam: lastPage => {
      return lastPage?.page?.has_next ? lastPage?.page?.next_id : undefined;
    },
  });

  const pages = productListRes.data?.pages || [];
  const productList = pages?.flatMap(item => item?.items) || [];
  const sortOptions = pages?.[0]?.sort_on?.filter(
    (item: any) => item?.display !== 'Relevance',
  );
  const filterOption = pages?.[0]?.filters;
  const [filterTab, setFilterTab] = useState<string>('');

  const handleLoadMore = () => {
    if (productListRes.hasNextPage && !productListRes.isFetchingNextPage) {
      productListRes.fetchNextPage();
    }
  };

  const category = useQuery({
    queryKey: [RQKey.CATEGORY],
    queryFn: () => getAllCategoryApi(),
    enabled: isTransitionComplete,
  });

  const selectedL3CategoryData = selectedL3Category(category.data, slug);
  const selectedL1CategoryData = selectedL1Category(category.data, slug);
  const fetchL3CategoryData = fetchL3Category(category.data, slug);

  const l4CategoryData = filterOption
    ?.find(
      (f: any) =>
        f?.key?.kind === 'multivalued' && f?.key?.name === 'l3_category_names',
    )
    ?.values?.map((item: any) => {
      return {
        ...item,
        display: formatCategoryName(item?.display),
      };
    });
  // allImage =
  //   state.tabCategoryData?.find(
  //     filter =>
  //       filter?.key?.kind === 'multivalued' &&
  //       filter?.key?.name === 'category-l2',
  //   )?.values?.[0]?.logo ?? '';

  const getCategoryTitle = (category: any, selectedL1Category: any) => {
    const title = category?.filter(
      (data: any) => data?.slug === selectedL1Category,
    );
    return title?.[0]?.name;
  };

  // sort methods

  const handleSelectedSort = (value: any) => {
    setSelectedSort(value);
    setParams(prev => {
      return {
        ...prev,
        sort_on: value?.value,
        page_id: '*',
      };
    });
    scrollToTop();
  };

  // filter method
  const handleApplyFilter = (value: string) => {
    setParams(prev => {
      return {
        ...prev,
        f: value,
      };
    });
    closeFilterBtmSheet();
  };
  const handleClearAllFilter = (value: string) => {
    setParams(prev => {
      return {
        ...prev,
        f: value,
      };
    });
  };

  const handleRedirectProductPage = (slug: string, cta: NavigationBean) => {
    const actionUrl = `${getBaseURL()}${cta?.actionUrl?.replace(
      '[SLUG]',
      slug,
    )}`;

    navigateTo(
      navBeanObj({
        ...cta,
        actionUrl,
        params: {
          slug,
        },
      }),
      navigation,
    );
    closeMultiVariantBtmSheet();
  };

  // bottomsheet methods

  const openCategoryBtmSheet = () => {
    setCategoryBtmSheet(true);
  };
  const closeCategoryBtmSheet = () => {
    setCategoryBtmSheet(false);
  };
  const openSortBtmSheet = () => {
    setSortBtmSheet(true);
  };
  const closeSortBtmSheet = () => {
    setSortBtmSheet(false);
  };
  const openFilterBtmSheet = () => {
    setFilterBtmSheet(true);
  };
  const closeFilterBtmSheet = () => {
    setFilterBtmSheet(false);
  };
  const openMultiVariantBtmSheet = (val: any) => {
    setSelectedVariants(val);
    setMultiVariantBtmSheet(true);
  };
  const closeMultiVariantBtmSheet = () => {
    setMultiVariantBtmSheet(false);
  };

  const scrollToTop = () => {
    listRef.current?.scrollToOffset({offset: 0, animated: true});
  };

  useEffect(() => {
    if (!filterTab) {
      setFilterTab(filterOption?.[0]?.key?.name);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [filterOption]);

  return {
    ...props,
    navigationBean: route.params,
    config,
    slug,
    page,
    params,
    productList,
    isTransitionComplete,
    sortOptions,
    filterOption,
    productListRes,
    isGrid,
    selectedSort,
    category,
    categoryBtmSheet,
    openCategoryBtmSheet,
    closeCategoryBtmSheet,
    sortBtmSheet,
    openSortBtmSheet,
    closeSortBtmSheet,
    filterBtmSheet,
    openFilterBtmSheet,
    closeFilterBtmSheet,
    multiVariantBtmSheet,
    openMultiVariantBtmSheet,
    closeMultiVariantBtmSheet,
    selectedVariants,
    getCategoryTitle,
    selectedL1CategoryData,
    selectedL3CategoryData,
    fetchL3CategoryData,
    handleSelectedSort,
    l4CategoryData,
    handleLoadMore,
    listRef,
    scrollToTop,
    handleApplyFilter,
    handleClearAllFilter,
    filterTab,
    setFilterTab,
    handleRedirectProductPage,
  };
};

export default useProductListingScreenController;
