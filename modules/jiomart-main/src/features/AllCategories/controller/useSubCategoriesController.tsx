import {useColor} from '@jio/rn_components/src/theme/color/useColor';
import {useEffect, useState} from 'react';
import {ImageSourcePropType} from 'react-native';
import Animated, {
  Extrapolate,
  interpolate,
  measure,
  runOnUI,
  useAnimatedRef,
  useAnimatedStyle,
  useDerivedValue,
  useSharedValue,
  withTiming,
} from 'react-native-reanimated';
import {getDivisionDimension} from '@jm/jiomart-common/src/JMResponsive';

type ImageType = ImageSourcePropType | {uri: string} | undefined;

const Width = getDivisionDimension.width;
const Height = getDivisionDimension.height;

const useSubCategoriesController = (props: any) => {
  const imageUrl: ImageType = props.childElement.logo?.url
    ? {uri: props.childElement.logo?.url}
    : undefined;

  const [Sel, setSel] = useState(false);
  const listRef = useAnimatedRef<Animated.View>();
  const heightValue = useSharedValue(0);
  const open = useSharedValue(false);
  const progress = useDerivedValue(() =>
    open.value ? withTiming(1) : withTiming(0),
  );
  const articleColor = useColor('primary_20');
  const heightAnimationStyle = useAnimatedStyle(() => ({
    height: interpolate(
      progress.value,
      [0, 1],
      [0, heightValue.value],
      Extrapolate.CLAMP,
    ),
  }));

  useEffect(() => {
    heightValue.value = 0;
    open.value = false;
    setSel(false);
  }, [props.childElement.name]);

  const imageRatio = Width < Height ? Width : Height;

  const getSubCategoryText = (inputString: string) => {
    const parts = inputString.split('::');
    return parts[parts.length - 1];
  };

  const handleSubCategoryPress = () => {
    if (heightValue.value == 0 && !Sel) {
      runOnUI(() => {
        'worklet';
        heightValue.value = withTiming(measure(listRef)!.height);
      })();
    } else {
      heightValue.value = withTiming(0);
      if (Sel) {
        open.value = !open.value;
      }
    }
    open.value = !open.value;
    setSel(!Sel);
  };

  return {
    ...props,
    heightValue,
    Sel,
    listRef,
    imageUrl,
    imageRatio,
    getSubCategoryText,
    articleColor,
    progress,
    heightAnimationStyle,
    handleSubCategoryPress,
  };
};

export default useSubCategoriesController;
