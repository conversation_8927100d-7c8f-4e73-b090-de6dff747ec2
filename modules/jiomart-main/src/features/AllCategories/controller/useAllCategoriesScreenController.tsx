import {Animated} from 'react-native';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import {removeStringPref} from '@jm/jiomart-common/src/JMAsyncStorageHelper';
import {
  AsyncStorageKeys,
  RQKey,
} from '@jm/jiomart-common/src/JMConstants';
import {type UseJMAllCategoriesScreenProps} from '../types/JMAllCategoriesScreenProps';
import useAllCategoriesViewModel from '../viewModel/useAllCategoriesViewModel';
import {useQuery} from '@tanstack/react-query';
import usePincodeChange from '@jm/jiomart-address/src/hooks/usePincodeChange';
import useTransitionState from '@jm/jiomart-general/src/hooks/useTransitionState';
import type {CustomCategoryItems} from '../model/AllCategoriesResponse';
import {
  navBeanObj,
  type NavigationBean,
} from '@jm/jiomart-common/src/JMNavGraphUtil';
import {navigateTo} from '@jm/jiomart-general/src/navigation/JMNavGraph';
import {useConfigFile} from '@jm/jiomart-general/src/hooks/useJMConfig';
import {JMConfigFileName} from '@jm/jiomart-networkmanager/src/JMConfigFileManager/JMFileName';
import {getBaseURL} from '@jm/jiomart-networkmanager/src/JMEnvironmentConfig';

const useAllCategorieScreenController = (
  props: UseJMAllCategoriesScreenProps,
) => {
  const {navigation, route} = props;

  const {getAllCategoryApi} = useAllCategoriesViewModel();
  const isTransitionComplete = useTransitionState();

  const config: any = useConfigFile(
    JMConfigFileName.JMAllCategoriesConfigurationFileName,
  )?.data;
  const category = useQuery({
    queryKey: [RQKey.CATEGORY],
    queryFn: () => getAllCategoryApi(),
    enabled: isTransitionComplete,
  });

  var statusBarColor = '#0078ac';
  const insets = useSafeAreaInsets();
  const statusBarHeight = insets.top;

  const redirectToPlp = (
    slug: any,
    department: any,
    category: any,
    cta: NavigationBean,
  ) => {
    const actionUrl = `${getBaseURL()}${cta?.actionUrl?.replace(
      '[SLUG]',
      slug,
    )}`;
    navigateTo(
      navBeanObj({
        ...cta,
        actionUrl,
        params: {
          slug,
          page: {
            department,
            category,
          },
        },
      }),
      navigation,
    );
  };

  const handleL1CategoryPress = (
    cat: CustomCategoryItems,
    cta: NavigationBean,
  ) => {
    if (cat?.childs && Array.isArray(cat.childs)) {
      redirectToPlp(
        cat.slug,
        cat.department,
        cat?.childs && cat?.childs?.length > 0
          ? cat?.childs?.[0]?.slug
          : cat?.department?.[0],
        cta,
      );
    }
  };

  const handleL3CategoryPress = (
    cat: CustomCategoryItems,
    cta: NavigationBean,
  ) => {
    redirectToPlp(cat?.slug, cat?.department, cat?.slug, cta);
  };

  usePincodeChange(async () => {
    await removeStringPref(AsyncStorageKeys.ALL_CATEGORIES);
    category.refetch();
  });

  return {
    ...props,
    config,
    statusBarHeight,
    statusBarColor,
    stripTranslateY,
    category,
    insets,
    handleL1CategoryPress,
    handleL3CategoryPress,
    navigationBean: route.params,
  };
};

const scrollY = new Animated.Value(0);
const stripTranslateY = scrollY.interpolate({
  inputRange: [0, 100],
  outputRange: [0, -50],
  extrapolate: 'clamp',
});

export default useAllCategorieScreenController;
