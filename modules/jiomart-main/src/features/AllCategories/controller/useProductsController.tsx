import {useColor} from '@jio/rn_components';
import {Platform} from 'react-native';
import handleHapticFeedback from '@jm/jiomart-common/src/utils/JMHapticFeedback';

const useProductsController = (props: any) => {
  const {onPress} = props;
  const handleProductPress = () => {
    onPress();
    handleHapticFeedback(
      Platform.OS == 'android' ? 'impactMedium' : 'impactLight',
    );
  };

  const articleColor = useColor('primary_20');

  const getProductText = (inputString: string) => {
    var parts = inputString.split('::');
    if (parts.length === 1) {
      parts = inputString.split('-');
    }
    return parts[parts.length - 1];
  };

  return {
    ...props,
    getProductText,
    articleColor,
    handleProductPress,
  };
};

export default useProductsController;
