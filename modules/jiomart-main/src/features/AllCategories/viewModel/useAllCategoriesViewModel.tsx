import AsyncStorage from '@react-native-async-storage/async-storage';
import {AsyncStorageKeys} from '@jm/jiomart-common/src/JMConstants';
import {JMSharedViewModel} from '@jm/jiomart-common/src/JMSharedViewModel';
import {AppSourceType} from '@jm/jiomart-common/src/SourceType';
import JMAllCategoriesNetworkController from '@jm/jiomart-networkmanager/src/JMNetworkController/JMAllCategoriesNetworkController';
import {allCategoryConfig} from '../config/allCategoryConfig';

const topNavigation = allCategoryConfig?.topNavigation;
const allCategoriesController = new JMAllCategoriesNetworkController();

const useAllCategoriesViewModel = () => {
  const getAllCategoryApi = async () => {
    try {
      const cachedData = await AsyncStorage.getItem(
        AsyncStorageKeys.ALL_CATEGORIES,
      );
      if (cachedData !== null) {
        const parsedCachedData = JSON.parse(cachedData);
        if (parsedCachedData && parsedCachedData.length > 0) {
          return parsedCachedData;
        } else {
          return allCategoriesData();
        }
      } else {
        return allCategoriesData();
      }
    } catch (error) {
      return error;
    }
  };

  const allCategoriesData = async () => {
    try {
      const productsResponse =
        await allCategoriesController.fetchAllCategories();

      if (JMSharedViewModel.Instance.appSource === AppSourceType.JM_JCP) {
        const departmentsResponse =
          await allCategoriesController.fetchDepartments();
        const departmentDataItems = await departmentsResponse?.items;

        if (productsResponse && productsResponse.filters) {
          var department = [];
          var categoryL1 = [];
          var categoryL2 = [];

          productsResponse?.filters?.forEach(identifier => {
            if (
              identifier.key.name === 'department' ||
              identifier.key.name === 'departments'
            ) {
              identifier.values.forEach(item => {
                if (!topNavigation.includes(item.value)) {
                  return;
                }
                departmentDataItems?.forEach(d => {
                  if (item.value === d.slug) {
                    item.uid = d.uid; // Add UID to the item
                  }
                });
                if (
                  item.display !== null &&
                  item.display !== '' &&
                  item.display !== undefined &&
                  item.logo !== null &&
                  item.logo !== '' &&
                  item.logo !== undefined
                ) {
                  department.push(item);
                }
              });
              department = sortDepartmentsData(department);
            }

            if (
              identifier.key.name === 'category-l1' ||
              identifier.key.display === 'Category L1'
            ) {
              categoryL1 = identifier.values;
            }
            if (
              identifier.key.name === 'category-l2' ||
              identifier.key.display === 'Category L2'
            ) {
              categoryL2 = identifier.values;
            }
          });

          const categoryData = buildCategoryTree(
            department,
            categoryL1,
            categoryL2,
          );
          storeAllCategoriesData(categoryData);
          return categoryData;
        }
      } else {
        if (productsResponse) {
          storeAllCategoriesData(productsResponse);
          return productsResponse;
        }
      }
    } catch (error) {
      return error;
    }
  };

  const sortDepartmentsData = departmentsData => {
    const sortedDepartments = departmentsData.sort((a, b) => {
      const priorityOrderA = topNavigation.indexOf(a.value);
      const priorityOrderB = topNavigation.indexOf(b.value);
      return priorityOrderA - priorityOrderB;
    });
    return sortedDepartments;
  };

  const buildCategoryTree = (
    departmentTemp,
    categoryL1Temp,
    categoryL2Temp,
  ) => {
    var department = [];
    departmentTemp.map(dept => {
      var departmentItem = {};
      departmentItem.slug = dept.value;
      departmentItem.name = dept.display;
      departmentItem.banners = {portrait: {url: dept.logo}};
      departmentItem.department = [dept.value];
      departmentItem.childs = buildL1CategoryTree(
        dept,
        categoryL1Temp,
        categoryL2Temp,
      );
      department.push(departmentItem);
    });
    return department;
  };

  const buildL1CategoryTree = (dept, categoryL1Temp, categoryL2Temp) => {
    var categoryL1 = [];
    categoryL1Temp.map(catL1 => {
      if (
        catL1.display
          .toLowerCase()
          .startsWith(`${dept.display.toLowerCase()}::`)
      ) {
        var categoryL1Item = {};
        categoryL1Item.slug = extractSlug(catL1.value);
        categoryL1Item.name = catL1.display;
        categoryL1Item.logo = {url: catL1.logo};
        categoryL1Item.department = [dept.value];
        categoryL1Item.childs = buildL2CategoryTree(
          dept,
          catL1,
          categoryL2Temp,
        );
        categoryL1.push(categoryL1Item);
      }
    });
    return categoryL1;
  };

  const buildL2CategoryTree = (dept, catL1, categoryL2Temp) => {
    var categoryL2 = [];
    categoryL2Temp.map(catL2 => {
      if (
        catL2.display
          .toLowerCase()
          .startsWith(`${catL1.display.toLowerCase()}::`)
      ) {
        var categoryL2Item = {};
        categoryL2Item.slug = extractSlug(catL2.value);
        categoryL2Item.name = catL2.display;
        categoryL2Item.logo = {url: catL2.logo};
        categoryL2Item.department = [dept.value];
        categoryL2.push(categoryL2Item);
      }
    });

    return categoryL2;
  };

  const extractSlug = (input: string) => {
    return input
      .toLowerCase() // Convert to lowercase
      .replace(/::/g, '-') // Replace "::" with "-"
      .replace(/[, &]/g, '-') // Replace ",", "&", and spaces with "-"
      .replace(/--+/g, '-') // Remove any double dashes
      .trim(); // Remove leading/trailing spaces
  };

  const storeAllCategoriesData = async data => {
    try {
      await AsyncStorage.setItem(
        AsyncStorageKeys.ALL_CATEGORIES,
        JSON.stringify(data),
      );
    } catch (error) {}
  };

  return {
    getAllCategoryApi,
  };
};

export default useAllCategoriesViewModel;
