import React from 'react';
import {JioText} from '@jio/rn_components';
import {JioTypography} from '@jio/rn_components/src/index.types';
import {
  TouchableOpacity,
  View,
  type StyleProp,
  type ViewStyle,
} from 'react-native';
import {RightArrowIcon} from '@jm/jiomart-general/src/assets/icons';
import MicroTap from '@jm/jiomart-general/src/utils/Animation/Micro/MicroTap';
import useProductsController from '../controller/useProductsController';
import productsStyles from '../styles/products';
type PropsType = {
  onPress: () => void;
  name: string;
  style?: StyleProp<ViewStyle>;
};

const Products = (props: PropsType) => {
  const {name, style, getProductText, articleColor, handleProductPress} =
    useProductsController(props);
  return (
    <TouchableOpacity
      activeOpacity={1}
      onPress={handleProductPress}
      style={[productsStyles.products_container, style]}>
      <JioText
        appearance={JioTypography.BODY_XS}
        text={getProductText(name)}
        color="primary_grey_80"
        maxLines={2}
        style={productsStyles.products_text}
      />
      <MicroTap color={articleColor} borderRadius={100} padding={0}>
        <View style={productsStyles.toggle_container}>
          <RightArrowIcon />
        </View>
      </MicroTap>
    </TouchableOpacity>
  );
};

export default Products;
