import React, {useMemo, useState} from 'react';
import {View, Image, ActivityIndicator} from 'react-native';
import {useTheme} from '@jio/rn_components';
import {getDivisionDimension} from '@jm/jiomart-common/src/JMResponsive';

const Width = getDivisionDimension.width;
const Height = getDivisionDimension.height;

const CustomImage = ({style, source}) => {
  const [loading, setLoading] = useState(true);

  const handleLoadStart = () => {
    setLoading(true);
  };

  const handleLoadEnd = () => {
    setLoading(false);
  };

  const handleLoad = () => {
    setLoading(false);
  };

  const memorizedImage = useMemo(
    () => (
      <Image
        style={style}
        source={source}
        onLoadStart={handleLoadStart}
        onLoadEnd={handleLoadEnd}
        onLoad={handleLoad}
      />
    ),
    [source],
  );

  const theme = useTheme();

  const imageRatio = Width < Height ? Width : Height;

  return (
    <View>
      {loading ? (
        <View
          style={[
            {
              width: 48 * imageRatio,
              height: 48 * imageRatio,
              position: 'absolute',
              backgroundColor: theme.primary_grey_40,
              borderRadius: 5,
            },
          ]}></View>
      ) : null}
      {memorizedImage}
    </View>
  );
};

export default CustomImage;
