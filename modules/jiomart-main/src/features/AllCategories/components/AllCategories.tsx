import React from 'react';
import {JioText} from '@jio/rn_components';
import {JioTypography} from '@jio/rn_components/src/index.types';
import {FlatList, ScrollView, TouchableOpacity, View} from 'react-native';
import Animated from 'react-native-reanimated';
import useAllCategoriesController from '../controller/useAllCategoriesController';
import {CustomCategoryItems} from '../model/AllCategoriesResponse';
import allCategoriesStyles from '../styles/all_categories';
import Categories from './Categories';
import {rh} from '@jm/jiomart-common/src/JMResponsive';

type AllCategoriesProps = {
  allCategory: CustomCategoryItems[];
  handleScroll: any;
  onL1CategoryPress: (cat: CustomCategoryItems) => void;
  onL3CategoryPress: (cat: CustomCategoryItems) => void;
};

const AllCategories = (props: AllCategoriesProps) => {
  const {
    allCategory,
    handleScroll,
    scrollViewRef,
    animatedStyle,
    selectedIndex,
    selectedDividerColorAnimatedStyle,
    dividerColorAnimatedStyle,
    handleCategoryAnimation,
    onL1CategoryPress,
    keyExtractor,
    renderItem,
  } = useAllCategoriesController(props);

  return (
    <View style={allCategoriesStyles.container}>
      <View style={allCategoriesStyles.left_container}>
        <ScrollView
          bounces={false}
          onScroll={handleScroll}
          scrollEventThrottle={16}
          style={[allCategoriesStyles.scrollview, {paddingTop: rh(16)}]}
          ref={scrollViewRef}
          showsVerticalScrollIndicator={false}>
          <Animated.View
            style={[allCategoriesStyles.selectedCategoryBox, animatedStyle]}>
            <View style={allCategoriesStyles.verticalDivider} />
          </Animated.View>
          {allCategory && allCategory?.length > 0
            ? allCategory.map(
                (l1Category: CustomCategoryItems, index: number) => {
                  return (
                    <View key={`ele-${index}-${l1Category?.slug}`}>
                      <Categories
                        data={l1Category}
                        index={index}
                        selectedIndex={selectedIndex}
                        onPress={() =>
                          handleCategoryAnimation(index, l1Category.name)
                        }
                      />

                      {index == selectedIndex || selectedIndex - 1 == index ? (
                        <Animated.View
                          style={[
                            allCategoriesStyles.divider,
                            selectedDividerColorAnimatedStyle,
                          ]}
                        />
                      ) : (
                        <Animated.View
                          style={[
                            allCategoriesStyles.divider,
                            dividerColorAnimatedStyle,
                          ]}
                        />
                      )}
                    </View>
                  );
                },
              )
            : null}
        </ScrollView>
      </View>
      <View style={allCategoriesStyles.right_container}>
        <TouchableOpacity
          activeOpacity={1}
          style={[allCategoriesStyles.title_container, {marginTop: rh(16)}]}
          onPress={() => onL1CategoryPress?.(allCategory[selectedIndex])}>
          <JioText
            appearance={JioTypography.HEADING_XS}
            text={allCategory[selectedIndex].name}
            style={allCategoriesStyles.title}
            color="primary_60"
            maxLines={2}
          />
          {/* <ArrowIcon /> */}
        </TouchableOpacity>

        <FlatList
          data={allCategory[selectedIndex].childs}
          renderItem={renderItem}
          keyExtractor={keyExtractor}
          showsVerticalScrollIndicator={false}
        />
      </View>
    </View>
  );
};

export default AllCategories;
