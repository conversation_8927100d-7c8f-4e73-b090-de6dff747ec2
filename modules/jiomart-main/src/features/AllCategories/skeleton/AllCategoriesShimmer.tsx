import React from 'react';
import {ShimmerKind} from '@jio/rn_components/src/index.types';
import {StyleSheet, View} from 'react-native';
import JioMartShimmer from '@jm/jiomart-general/src/utils/JioMartShimmer';
import {getScreenDim, rh, rw} from '@jm/jiomart-common/src/JMResponsive';

const shimmerArray = Array(8).fill(null);

const windowHeight = getScreenDim.height;
const windowWidth = getScreenDim.width;

const AllCategoriesShimmer = () => {
  return (
    <View style={[allCategoriesStyles.container, {backgroundColor: 'white'}]}>
      <View style={allCategoriesStyles.left_container}>
        <View style={{paddingTop: 16}}>
          <CategoriesShimmer />
        </View>
        {shimmerArray.map((_, index) => (
          <CategoriesShimmer key={index} />
        ))}
      </View>
      <View style={allCategoriesStyles.right_container}>
        <View style={[allCategoriesStyles.title_container, {marginTop: 16}]}>
          <JioMartShimmer
            width={rw(180)}
            height={rh(26)}
            kind={ShimmerKind.RECTANGLE}
            style={{borderRadius: 0}}
          />
        </View>

        {shimmerArray.map((_, index) => (
          <SubCategoriesShimmer key={index} />
        ))}
      </View>
    </View>
  );
};

export default AllCategoriesShimmer;

const CategoriesShimmer = () => {
  return (
    <View>
      <View style={categoriesStyles.categories_container}>
        <JioMartShimmer
          width={rw(48)}
          height={rw(48)}
          kind={ShimmerKind.RECTANGLE}
          style={{borderRadius: 5}}
        />
      </View>
      <View style={{alignItems: 'center'}}>
        <JioMartShimmer
          width={rw(72)}
          height={rh(3)}
          kind={ShimmerKind.RECTANGLE}
          style={{borderRadius: 0}}
        />
      </View>
    </View>
  );
};

const SubCategoriesShimmer = () => {
  return (
    <View
      style={{
        marginHorizontal: rw(12),
        marginBottom: rh(8),
        borderWidth: 1,
        borderColor: '#E0E0E0',
        borderRadius: 12,
      }}>
      <View style={subCategoriesStyles.subcategory_container}>
        <View style={subCategoriesStyles.content_container}>
          <JioMartShimmer
            width={rw(48)}
            height={rw(48)}
            kind={ShimmerKind.CIRCLE}
            style={{marginLeft: rw(16)}}
          />
          <JioMartShimmer
            width={rw(120)}
            height={rh(14)}
            kind={ShimmerKind.RECTANGLE}
            style={{marginLeft: rw(12), borderRadius: 0}}
          />
        </View>
        <View style={subCategoriesStyles.toggle_container}>
          <JioMartShimmer
            width={rw(24)}
            height={rw(24)}
            kind={ShimmerKind.RECTANGLE}
            style={{borderRadius: 0}}
          />
        </View>
      </View>
    </View>
  );
};

const allCategoriesStyles = StyleSheet.create({
  container: {
    width: windowWidth,
    height: windowHeight - rh(64),
    flex: 1,
    flexDirection: 'row',
  },
  left_container: {
    width: rw(88),
    height: '100%',
    borderRightWidth: 1,
    borderRightColor: '#E0E0E0',
  },
  right_container: {
    flex: 1,
    height: '100%',
  },
  title_container: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    minHeight: rh(80),
    marginHorizontal: rw(12),
    marginVertical: rh(8),
    paddingHorizontal: rw(16),
    paddingVertical: rh(12),
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#E0E0E0',
  },
});

const categoriesStyles = StyleSheet.create({
  categories_container: {
    width: '96%',
    height: rh(105),
    flexDirection: 'column',
    justifyContent: 'center',
    alignItems: 'center',
    paddingTop: 4,
    paddingRight: 4,
    paddingBottom: 8,
    paddingLeft: 4,
  },
});

const subCategoriesStyles = StyleSheet.create({
  subcategory_container: {
    height: rh(76),
    justifyContent: 'space-between',
    flexDirection: 'row',
  },
  content_container: {
    width: rw(189),
    flexDirection: 'row',
    alignItems: 'center',
  },
  toggle_container: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingLeft: rw(8),
  },
});
