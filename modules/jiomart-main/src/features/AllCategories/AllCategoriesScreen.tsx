import React from 'react';
import {GestureHandlerRootView} from 'react-native-gesture-handler';
import {NoProducts} from '@jm/jiomart-general/src/assets/icons';
import ScreenSlot, {DeeplinkHandler} from '@jm/jiomart-general/src/ui/JMScreenSlot';
import NegativeScreenUI from '@jm/jiomart-general/src/ui/NegativeScreenUI';
import AllCategories from './components/AllCategories';
import useAllCategoriesScreenController from './controller/useAllCategoriesScreenController';
import AllCategoriesShimmer from './skeleton/AllCategoriesShimmer';
import {JMAllCategoriesScreenProps} from './types/JMAllCategoriesScreenProps';

const AllCategoriesScreen = (props: JMAllCategoriesScreenProps) => {
  const {
    navigation,
    navigationBean,
    config,
    category,
    JMRNNavigatorModule,
    insets,
    handleL1CategoryPress,
    handleL3CategoryPress,
  } = useAllCategoriesScreenController(props);

  if (category?.isLoading) {
    return <AllCategoriesShimmer />;
  }

  return (
    <DeeplinkHandler
      navigationBean={navigationBean}
      navigation={navigation}
      children={_ => (
        <ScreenSlot
          navigationBean={navigationBean}
          navigation={navigation}
          children={_ => {
            return (
              <GestureHandlerRootView>
                {category.data?.length > 0 ? (
                  <AllCategories
                    allCategory={category.data}
                    handleScroll={undefined}
                    onL1CategoryPress={cat => {
                      handleL1CategoryPress(cat, config?.l1_category?.cta);
                    }}
                    onL3CategoryPress={cat => {
                      handleL3CategoryPress(cat, config?.l3_category?.cta);
                    }}
                  />
                ) : (
                  <>
                    {
                      <NegativeScreenUI
                        logoSvg={<NoProducts />}
                        title={'Coming Soon...'}
                        subTitle={
                          'Products are currently not available at the selected PIN code'
                        }
                        isButtonVisible
                        buttonTitle={'Go Back to Home'}
                        onPress={() => {
                          JMRNNavigatorModule.nativeBackPress();
                        }}
                        offset={insets.bottom}
                      />
                    }
                  </>
                )}
              </GestureHandlerRootView>
            );
          }}
        />
      )}
    />
  );
};

export default AllCategoriesScreen;
