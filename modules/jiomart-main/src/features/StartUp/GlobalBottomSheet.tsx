import React from 'react';
import useGlobalBottomSheetController from './controllers/useGlobalBottomSheetController';
import PermissionBottomSheet from './PermissionBottomSheet';
import BottomSheet from '../../../../jiomart-general/src/ui/BottomSheet/BottomSheet';
import {useGlobalState} from '../../../../jiomart-general/src/utils/JMGlobalStateProvider';
import PrivacyPolicyBottomSheet from './PrivacyPolicyBottomSheet';

const GlobalBottomSheet = () => {
  const {permissionConfig, privacyPolicyConfig, checkAndOpenNextSheet} =
    useGlobalBottomSheetController();
  const {
    permissionBtmSheet,
    privacyPolicyBtmSheet,
    softUpdateBtmSheet,
    setPermissionBtmSheet,
    setPrivacyPolicyBtmSheet,
    setSoftUpdateBtmSheet,
  } = useGlobalState();

  return (
    <>
      {permissionConfig?.isVisible ? (
        <BottomSheet
          visible={permissionBtmSheet}
          disabledBackDropClick
          disableBlurGain
          disabledGesture>
          <PermissionBottomSheet
            permissionConfigData={permissionConfig}
            onProceed={() => {
              setPermissionBtmSheet(false);
              checkAndOpenNextSheet();
            }}
          />
        </BottomSheet>
      ) : null}
      {privacyPolicyConfig?.isVisible ? (
        <BottomSheet
          visible={privacyPolicyBtmSheet}
          disabledBackDropClick
          disableBlurGain
          disabledGesture>
          <PrivacyPolicyBottomSheet
            onClose={() => {
              setPrivacyPolicyBtmSheet(false);
              checkAndOpenNextSheet();
            }}
            onAccept={() => {
              setPrivacyPolicyBtmSheet(false);
              checkAndOpenNextSheet();
            }}
            configData={privacyPolicyConfig}
          />
        </BottomSheet>
      ) : null}

      {/* <BottomSheet
        visible={softUpdateBtmSheet}
        disabledBackDropClick={softUpdateConfig?.immediate_update}
        disabledGesture={softUpdateConfig?.immediate_update}
        disableBlurGain
        onBackDropClick={() => {
          softUpdateRef.current?.close();
          checkAndHideBnb();
          checkAndOpenNextSheet();
        }}
        onDrag={() => {
          softUpdateRef.current?.close();
          checkAndHideBnb();
          checkAndOpenNextSheet();
        }}>
        <SoftUpdateBtmSheet
          config={softUpdateConfig}
          onClose={() => {
            if (softUpdateConfig?.immediate_update) {
              return;
            }
              checkAndHideBnb();
            softUpdateRef.current?.close();
            checkAndOpenNextSheet();
          }}
          onButtonPress={() => {softUpdateApp(); checkAndHideBnb();}}
        />
      </BottomSheet>
    */}
    </>
  );
};

export default GlobalBottomSheet;
