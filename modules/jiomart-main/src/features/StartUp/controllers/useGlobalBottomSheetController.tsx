import usePermissionBottomSheetController from './usePermissionBottomSheetController';
import {useConfigFileFromCache} from '../../../../../jiomart-general/src/hooks/useJMConfig';
import {JMConfigFileName} from '../../../../../jiomart-networkmanager/src/JMConfigFileManager/JMFileName';
import {useGlobalState} from '../../../../../jiomart-general/src/utils/JMGlobalStateProvider';
import {getPrefString} from '../../../../../jiomart-common/src/JMAsyncStorageHelper';
import {AsyncStorageKeys} from '../../../../../jiomart-common/src/JMConstants';
import {JMSharedViewModel} from '../../../../../jiomart-common/src/JMSharedViewModel';

const useGlobalBottomSheetController = () => {
  const config = useConfigFileFromCache(
    JMConfigFileName.JMCommonContentFileName,
  );
  const permissionConfig = config?.data?.permissionConfig;
  const privacyPolicyConfig = config?.data?.privacyPolicyConfig;

  // const softUpdate = useSoftUpdateBottomSheetController();
  const permission = usePermissionBottomSheetController();
  // const {setKeyForEachLaunch, getValueForEachLaunch} = useEachAppLaunch();

  const {
    setPermissionBtmSheet,
    setPrivacyPolicyBtmSheet,
    setSoftUpdateBtmSheet,
  } = useGlobalState();

  const checkAndOpenNextSheet = async () => {
    try {
      const isPermissionGranted = await getPrefString(
        AsyncStorageKeys.PERMISSION_GRANTED,
      );
      // const isValidForSoftUpdate = await softUpdate.isValidToOpenSoftUpdate({
      //   appVersion: softUpdate.appVersion,
      //   ...softUpdate.softUpdateConfig,
      // });
      const isPrivacyPolicy = await getPrefString(
        AsyncStorageKeys.PRIVACY_POLICY,
      );
      console.log(
        'isPermissionGranted',
        isPermissionGranted,
      );

      if (isPermissionGranted !== 'true') {
        setPermissionBtmSheet(true);
      }
      // else if(// logic for soft update open) {
      //  setSoftUpdateBtmSheet(true)
      // }
      else if (JMSharedViewModel.Instance.loggedInStatus && isPrivacyPolicy !== 'true') {
        setPrivacyPolicyBtmSheet(true);
      }
    } catch (error) {
      console.log('🚀 ~ checkAndOpenNextSheet ~ error:', error);
    }
  };

  return {
    permissionConfig,
    privacyPolicyConfig,
    // ...softUpdate,
    ...permission,
    checkAndOpenNextSheet,
  };
};

export default useGlobalBottomSheetController;
