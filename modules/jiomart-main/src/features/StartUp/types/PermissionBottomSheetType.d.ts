import type {
  JioIconProps,
  JioTextProps,
} from '@jio/rn_components/src/index.types';
import type {BottomSheetChildren} from '../../../../../jiomart-general/src/ui/BottomSheet/types/BottomSheetType';

interface PermissionItem {
  permissionTitle: JioTextProps;
  permissionSubTitle: JioTextProps;
  permissionIcon: JioIconProps;
}

export interface PermissionsData {
  isVisible: boolean;
  title: string;
  subTitle: string;
  buttonText: string;
  permissions: PermissionItem[];
}

export interface PermissionBottomSheetProps extends BottomSheetChildren {
  onProceed: () => void;
  permissionConfigData: PermissionsData;
}
