import { CartConstant } from '../constants/CartConstant';
import { InitialState } from '../types/InitialState.s';
import {Status} from '../types/status';

interface CartInitialState extends InitialState {
  hideAddToCartButton: {};
}
const cartInitialState: CartInitialState = {
  hideAddToCartButton: {},
  loading: {},
  payload: [],
  error: {},
  status: Status.REQUEST,
  message: '',
};

const CartReducer = (
  state = cartInitialState,
  action: {type: string; payload: any},
) => {
  switch (action.type) {
    case CartConstant.GET_CART_REQUEST:
      return {
        ...state,
      };
    case CartConstant.GET_CART_SUCCESS:
      return {
        ...state,
        payload: action.payload,
      };
    case CartConstant.GET_CART_FAILURE:
      return {
        ...state,
        error: action.payload,
      };
    case CartConstant.INITIATE_ADD_TO_CART_REQUEST:
      return {
        ...state,
        hideAddToCartButton: {
          ...state.hideAddToCartButton,
          [action.payload.body.item_id]: true,
        },
        loading: {
          ...state.loading,
          [action.payload.body.item_id]: true,
        },
      };
    case CartConstant.INITIATE_ADD_TO_CART_SUCCESS:
      let hideAddButton = true;
      const product = action.payload.payload?.cart?.items?.filter(
        (cartItem: any) => cartItem?.product?.uid === action.payload.uid,
      );
      hideAddButton = product && product?.length > 0;
      return {
        ...state,
        hideAddToCartButton: {
          ...state.hideAddToCartButton,
          [action.payload.uid]: hideAddButton,
        },
        payload: action.payload.payload,
      };
    case CartConstant.INITIATE_ADD_TO_CART_FAILURE:
      return {
        ...state,
        hideAddToCartButton: {
          ...state.hideAddToCartButton,
          [action.payload.uid]: false,
        },
        error: action.payload.payload,
        status: Status.ERROR,
      };
    case CartConstant.UPDATE_CART_REQUEST:
      return {
        ...state,
      };
    case CartConstant.UPDATE_CART_SUCCESS:
      return {
        ...state,
        hideAddToCartButton: {
          ...state.hideAddToCartButton,
          [action.payload.uid]: action.payload.qty > 0,
        },
        payload: action.payload.payload,
      };
    case CartConstant.UPDATE_CART_FAILURE:
      return {
        ...state,
        payload: action.payload.payload,
      };
    case CartConstant.BUY_NOW_SUCCESS:
      return {
        ...state,
        payload: action.payload,
      };
    case CartConstant.BUY_NOW_FAILURE:
      return {
        ...state,
        error: action.payload,
      };
    case CartConstant.RESET_CART:
      return {
        ...state,
        hideAddToCartButton: {},
        loading: {},
        payload: [],
        error: {},
      };
    case CartConstant.CART_LOADING:
      return {
        ...state,
        loading: {
          ...state.loading,
          [action.payload.uid]: action.payload.loading,
        },
      };
    case CartConstant.SET_HIDE_ADD_TO_CART_BUTTON:
      return {
        ...state,
        hideAddToCartButton: {
          ...state.hideAddToCartButton,
          [action.payload.uid]: action.payload.hide,
        },
      };
    default:
      return state;
  }
};

export default CartReducer;
