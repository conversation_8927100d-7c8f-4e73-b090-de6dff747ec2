import {useCallback, useEffect} from 'react';
import {ParamsData} from '..';
import {
  subscribeToRNEvent,
  unsubscribeToRNEvent,
} from '../../../../node_modules/@ucp/one-retail/src/utils/bridge/Emitter';
import {addStringPref} from '../../../jiomart-common/src/JMAsyncStorageHelper';
import {
  AsyncStorageKeys,
  EventEmitterKeys,
} from '../../../jiomart-common/src/JMConstants';
import {
  deleteGuestUserSession,
  deleteUserDetails,
  deleteUserSession,
  getUserDetails,
} from '../../../jiomart-common/src/JMDataBaseManager';
import {NavigationBean} from '../../../jiomart-common/src/JMNavGraphUtil';
import {JMSharedViewModel} from '../../../jiomart-common/src/JMSharedViewModel';
import {AppSourceType} from '../../../jiomart-common/src/SourceType';
import {JMLogger} from '../../../jiomart-common/src/utils/JMLogger';
import {useGlobalState} from '../../../jiomart-general/src/utils/JMGlobalStateProvider';
import {
  callVersionFile,
  getConfigFileData,
} from '../../../jiomart-networkmanager/src/JMConfigFileManager/JMFileManager';
import {JMConfigFileName} from '../../../jiomart-networkmanager/src/JMConfigFileManager/JMFileName';
import {
  fetchGuestUserSession,
  fetchUserDetails,
  fetchUserSession,
  logoutUser,
} from '../../../jiomart-networkmanager/src/JMNetworkController/JMUserAPINetworkController';
import {JioMartMainUIProps} from '../types/JioMartMainUIType';
import useAddress from '@jm/jiomart-address/src/hooks/useAddress';
import usePincodeChange from '@jm/jiomart-address/src/hooks/usePincodeChange';
import type {JMAddressModel} from '../../../jiomart-networkmanager/src/models/Address/JMAddressModel';
import useUserProfile from '../../../jiomart-general/src/hooks/useUserProfile';
import {
  useAppStartupConfig,
  useConfigFileFromCache,
  useVersionFile,
} from '../../../jiomart-general/src/hooks/useJMConfig';
import {fetchDataLoader} from '../../../jiomart-networkmanager/src/JMNetworkController/JMDataLoaderNetworkController';
import useCartOperation from '@jm/jiomart-cart/src/hooks/useCartOperation';
import useWishlistOperation from '@jm/jiomart-wishlist/src/hooks/useWishlistOperation';

const useJioMartMainUIController = ({
  route,
  navigation,
}: JioMartMainUIProps) => {
  let beanData = route.params as NavigationBean;

  useVersionFile({enabled: true});
  useAppStartupConfig([
    JMConfigFileName.JMCommonContentFileName,
    JMConfigFileName.JMAddressConfigurationFileNAme,
  ]);
  const config = useConfigFileFromCache(
    JMConfigFileName.JMCommonContentFileName,
  )?.data;

  const {saveUserData} = useUserProfile();
  const {setEvent, setUserInitials, setAddress} = useGlobalState();

  if (beanData.params) {
    const paramBean = beanData.params as ParamsData;
    JMSharedViewModel.Instance.setDeeplinkUrlData(paramBean.deeplink ?? '');
  }

  const {setIntialAddress, getDefaultAddress} = useAddress();

  const handleAddress = async (initialAddress?: JMAddressModel) => {
    let address: any = await getDefaultAddress();
    address = !address ? initialAddress : JSON.parse(address ?? '');
    setAddress(address);
  };
  useEffect(() => {
    if (config?.location) {
      setIntialAddress({
        ...config?.location,
        pin: config?.location?.pincode,
      });
      handleAddress({
        ...config?.location,
        pin: config?.location?.pincode,
      });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [config?.location]);

  usePincodeChange(async () => {
    await handleAddress();
  });

  const {getCart} = useCartOperation();
  const {getWishlistIds} = useWishlistOperation();

  useEffect(() => {
    getCart.mutate();
    if (JMSharedViewModel.Instance.loggedInStatus) {
      getWishlistIds.mutate();
    }
  }, []);

  useEffect(() => {
    if (beanData.params !== null) {
      navigation.setParams({params: null});
      callVersionFile(JMConfigFileName.JMVersionFileName, versionData => {
        getConfigFileData(
          JMConfigFileName.JMCommonContentFileName,
          content => {},
        );
        getConfigFileData(
          JMConfigFileName.JMHeaderConfigurationFileName,
          content => {},
        );
      }).catch(error => {
        JMLogger.log('file version read error - ', error);
      });
    }
  }, [route.params]);

  const handleChildEvent = useCallback(async (data: any) => {
    if (data?.logout) {
      if (JMSharedViewModel.Instance.appSource === AppSourceType.JM_BAU) {
        const logoutResponse = await logoutUser();
        if (logoutResponse?.status === 'success') {
          deleteGuestUserSession();
          deleteUserDetails();
          deleteUserSession();
          setUserInitials('');
          JMSharedViewModel.Instance.setLoggedInStatus(false);
          addStringPref(AsyncStorageKeys.PRIVACY_POLICY, '');
          fetchGuestUserSession();
        }
      } else {
        deleteGuestUserSession();
        deleteUserDetails();
        deleteUserSession();
        setUserInitials('');
        JMSharedViewModel.Instance.setLoggedInStatus(false);
        addStringPref(AsyncStorageKeys.PRIVACY_POLICY, '');
      }

      if (navigation.canGoBack) {
        navigation.goBack();
      }
      return;
    }
    if (navigation.canGoBack) {
      navigation.goBack();
    }
  }, []);

  const handleDidLoggedInEvent = useCallback(async (data?: any) => {
    try {
      if (data.authCode) {
        const response = await fetchUserSession(data.authCode);

        if (JMSharedViewModel.Instance.appSource === AppSourceType.JM_BAU) {
          if (response && response.status === 'success') {
            const profileResponse = await fetchUserDetails();
            JMSharedViewModel.Instance.setLoggedInStatus(true);
            setUserInitials(
              profileResponse?.result?.your_details?.display_name_full,
            );
            setEvent({
              WebViewEventEmitt: {sendToWebCraDetails: response},
            });
          }
        } else {
          if (response && response?.success === true) {
            const userDetailRaw = response?.data?.jcp_user_details?.users?.[0];
            const userDetails = JSON.stringify(userDetailRaw);
            saveUserData(userDetailRaw);
            await addStringPref(AsyncStorageKeys.USER_DETAILS, userDetails);
            JMSharedViewModel.Instance.setLoggedInStatus(true);
            setUserInitials(
              response?.data?.jcp_user_details?.users?.[0]?.first_name +
                ' ' +
                response?.data?.jcp_user_details?.users?.[0]?.last_name,
            );
            setEvent({
              WebViewEventEmitt: {sendToWebCraDetails: response},
            });
            console.log(
              '🚀 ~ handleDidLoggedInEvent ~ response.data:',
              JSON.stringify(response),
            );
          }
        }
      }
      if (navigation.canGoBack) {
        navigation.goBack();
      }
    } catch (error) {
      JMLogger.log('Error in handleDidLoggedInEvent - ', error);
      if (navigation.canGoBack) {
        navigation.goBack();
      }
    }
  }, []);

  useEffect(() => {
    subscribeToRNEvent(EventEmitterKeys.CLOSE, handleChildEvent);
    subscribeToRNEvent(EventEmitterKeys.ON_LOGGED_IN, handleDidLoggedInEvent);
    return () => {
      unsubscribeToRNEvent(EventEmitterKeys.CLOSE, () =>
        handleChildEvent(null),
      );
      unsubscribeToRNEvent(
        EventEmitterKeys.ON_LOGGED_IN,
        handleDidLoggedInEvent,
      );
    };
  }, []);

  useEffect(() => {
    if (JMSharedViewModel.Instance.appSource === AppSourceType.JM_BAU) {
      fetchGuestUserSession();
    }
    setUserLoginStatus();
    setUserInitialsToGlobal();
    fetchDataLoader();
  }, []);

  async function setUserLoginStatus() {
    const userResponse = await getUserDetails();
    JMSharedViewModel.Instance.setLoggedInStatus(userResponse !== null);
  }

  async function setUserInitialsToGlobal() {
    const userDetailsString = await getUserDetails();
    console.log(
      '🚀 ~ setUserInitialsToGlobal ~ userDetailsString:',
      userDetailsString,
    );
    if (userDetailsString) {
      const userDetails = JSON.parse(userDetailsString);
      if (JMSharedViewModel.Instance.appSource === AppSourceType.JM_BAU)
        setUserInitials(userDetails?.display_name_full);
      else
        setUserInitials(userDetails?.first_name + ' ' + userDetails?.last_name);
    }
  }
};

export default useJioMartMainUIController;
