import {KeyboardAvoidingView, Platform} from 'react-native';
import useCommonController from './JMCommonController';
import {KeyboardProps} from '@jhh/jio-health-common/src/JMScreenSlot.types';

export const MoveKeyboardUpWithButton: React.FC<KeyboardProps> = ({
  children,
  nativeResize,
}) => {
  if (nativeResize) {
    const {keyboardHeight} = useCommonController(true);
  }
  return (
    <KeyboardAvoidingView
      style={{flex: 1}}
      behavior={Platform.OS === 'ios' ? 'padding' : undefined}>
      {children}
    </KeyboardAvoidingView>
  );
};

export const MoveKeyboardUpWithoutButton: React.FC<KeyboardProps> = ({
  children,
  nativeResize,
}) => {
  if (nativeResize) {
    const {keyboardHeight} = useCommonController(true);
  }
  if (Platform.OS === 'android') {
    return (
      <MoveKeyboardUpWithButton nativeResize={nativeResize}>
        {children}
      </MoveKeyboardUpWithButton>
    );
  } else {
    return (
      <KeyboardAvoidingView
        style={{flex: 1}}
        enabled={false}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}>
        {children}
      </KeyboardAvoidingView>
    );
  }
};
