import React from 'react';
import {createNativeStackNavigator} from '@react-navigation/native-stack';
import OneRetailUI from '../../../../node_modules/@ucp/one-retail/src';
import JMAddressListScreen from '@jm/jiomart-address/src/screen/JMAddressListScreen';
import JMAddressSearchScreen from '@jm/jiomart-address/src/screen/JMAddressSearchScreen';
import JMAddressMapScreen from '@jm/jiomart-address/src/screen/JMAddressMapScreen';
import JMAddressFormScreen from '@jm/jiomart-address/src/screen/JMAddressFormScreen';
import JMAddressFormV1Screen from '@jm/jiomart-address/src/screen/JMAddressFormV1Screen';
import {NavigationStackData} from '../../../jiomart-common/src/JMNavGraphUtil';
import {getDestinationFromDeeplinkUrl} from '../../../jiomart-general/src/deeplink/JMDeeplinkUtility';
import CommonWebViewScreen from '../../../jiomart-webmanager/src/WebViewScreen';
import JMSplashScreen from '../features/spash/JMSplashScreen';
import JMSearchScreen from '../../../jiomart-general/src/ui/SearchScreen/JMSearchScreen';
import ProductListingScreen from '../features/ProductListing/screens/ProductListingScreen';
import ProductGridListingScreen from '../features/ProductListing/screens/ProductGridListingScreen';
import ProductSearchListingScreen from '../features/ProductListing/screens/ProductSearchListingScreen';
import AllCategoriesScreen from '@jm/jiomart-main/src/features/AllCategories/AllCategoriesScreen';
import {AppScreens} from '@jm/jiomart-common/src/JMAppScreenEntry';

const NavStack = createNativeStackNavigator<NavigationStackData>();

const JMNavigationStack = () => {
  return (
    <NavStack.Navigator
      initialRouteName={
        getDestinationFromDeeplinkUrl(
          '',
          AppScreens.SPLASH_SCREEN,
          true,
        ) as keyof NavigationStackData
      }
      screenOptions={{headerShown: false}}>
      <NavStack.Screen
        name={AppScreens.SPLASH_SCREEN}
        component={JMSplashScreen}
        options={{
          animation: 'none',
        }}
      />
      <NavStack.Screen
        name={AppScreens.COMMON_WEB_VIEW}
        component={CommonWebViewScreen}
        options={{
          animation: 'default',
        }}
      />
      <NavStack.Screen
        name={AppScreens.ADDRESS_LIST_SCREEN}
        component={JMAddressListScreen}
        options={{
          animation: 'default',
        }}
      />
      <NavStack.Screen
        name={AppScreens.ADDRESS_SEARCH_SCREEN}
        component={JMAddressSearchScreen}
        options={{
          animation: 'default',
        }}
      />
      <NavStack.Screen
        name={AppScreens.ADDRESS_MAP_SCREEN}
        component={JMAddressMapScreen}
        options={{
          animation: 'default',
        }}
      />
      <NavStack.Screen
        name={AppScreens.ADDRESS_FORM_SCREEN}
        component={JMAddressFormScreen}
        options={{
          animation: 'default',
        }}
      />
      <NavStack.Screen
        name={AppScreens.ADDRESS_FORM_V1_SCREEN}
        component={JMAddressFormV1Screen}
        options={{
          animation: 'default',
        }}
      />
      <NavStack.Screen
        name={AppScreens.ONE_RETAIL_SCREEN}
        component={OneRetailUI}
        options={{
          animation: 'default',
        }}
      />
      <NavStack.Screen
        name={AppScreens.ALL_CATEGORIES}
        component={AllCategoriesScreen}
        options={{
          animation: 'default',
        }}
      />
      <NavStack.Screen
        name={AppScreens.SEARCH_SCREEN}
        component={JMSearchScreen}
        options={{
          animation: 'default',
        }}
      />
      <NavStack.Screen
        name={AppScreens.PRODUCT_LISTING_SCREEN}
        component={ProductListingScreen}
        options={{
          animation: 'default',
        }}
      />
      <NavStack.Screen
        name={AppScreens.PRODUCT_GRID_LISTING_SCREEN}
        component={ProductGridListingScreen}
        options={{
          animation: 'default',
        }}
      />
      <NavStack.Screen
        name={AppScreens.PRODUCT_SEARCH_LISTING_SCREEN}
        component={ProductSearchListingScreen}
        options={{
          animation: 'default',
        }}
      />
    </NavStack.Navigator>
  );
};

export default JMNavigationStack;
