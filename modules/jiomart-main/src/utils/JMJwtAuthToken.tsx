import CryptoJS from 'crypto-js';
import Base64 from 'crypto-js/enc-base64';
 
interface JwtPayload {
  iss: string;
  iat: number;
  exp: number;
  data: string;
}
const base64urlEncode = (input: any): string => { 
  // Make the base64 encoded string URL-safe
  let base64 = Base64.stringify(input);
  base64 = base64
    .replace(/\+/g, '-')
    .replace(/\//g, '_')
    .replace(/=+$/, '');
 
  return base64;
};
const secretKey = '4e7187c1-bca5-4889-bbb6-6e47ccc17245'
// Function to generate JWT token with HMAC-SHA256
export const generateJwtToken = (payload: JwtPayload): string => {
  const header = {
    alg: 'HS256',
    typ: 'JWT'
  };
 
  // Encode header and payload to Base64 URL format
  const encodedHeader = base64urlEncode(CryptoJS.enc.Utf8.parse(JSON.stringify(header)));
  const encodedPayload = base64urlEncode(CryptoJS.enc.Utf8.parse(JSON.stringify(payload)));
 
  // Create the token body as per JWT format
  const tokenBody = `${encodedHeader}.${encodedPayload}`;
  console.debug("encodedPayload", encodedPayload.toString())
  // Calculate HMAC-SHA256 signature
  const signature = CryptoJS.HmacSHA256(tokenBody, secretKey);
  console.debug("Signature", signature.toString())
  const encodedSignature = base64urlEncode(signature);
  console.debug("encodedSignature", encodedSignature.toString())
  // Concatenate token body and signature to get the JWT token
  const jwtToken = `${tokenBody}.${encodedSignature}`;
 
  return jwtToken;
};