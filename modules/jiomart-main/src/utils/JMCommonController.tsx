import {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {useState, useEffect, useCallback} from 'react';
import {
  Keyboard,
  NativeScrollEvent,
  NativeSyntheticEvent,
  ScrollView,
} from 'react-native';
import {keyboardResize} from '../JMNativeBridge/JMBridge';
import {useFocusEffect} from '@react-navigation/native';

const useCommonController = (
  nativeKeyboardResize: boolean,
  bottomPaddingVal: number = 80,
) => {
  const [keyboardHeight, setKeyboardHeight] = useState(0);
  const [isKeyboardVisible, setIsKeyboardVisible] = useState(false);
  const [willKeyboardVisible, setWillKeyboardVisible] = useState(false);
  const [bottomPadding, setBottomPadding] = useState(0);
  const [scrollPosition, setScrollPosition] = useState<number>(0);

  useFocusEffect(
    useCallback(() => {
      if (nativeKeyboardResize) {
        console.debug('keyboardResize true');
        keyboardResize(true);
        return () => {
          console.debug('keyboardResize false');
          keyboardResize(false);
        };
      }
    }, []),
  );

  useFocusEffect(
    useCallback(() => {
      console.log('keyboard listner attached');
      const keyboardDidShowListener = Keyboard.addListener(
        'keyboardDidShow',
        event => {
          setKeyboardHeight(event.endCoordinates.height);
          setIsKeyboardVisible(true);
        },
      );
      const keyboardDidHideListener = Keyboard.addListener(
        'keyboardDidHide',
        () => {
          setKeyboardHeight(0);
          setIsKeyboardVisible(false);
        },
      );
      const keyboardWillShowListener = Keyboard.addListener(
        'keyboardWillShow',
        () => {
          setWillKeyboardVisible(true);
        },
      );
      const keyboardWillHideListener = Keyboard.addListener(
        'keyboardWillHide',
        () => {
          setWillKeyboardVisible(false);
        },
      );
      return () => {
        keyboardDidShowListener.remove();
        keyboardDidHideListener.remove();
        keyboardWillShowListener.remove();
        keyboardWillHideListener.remove();
      };
    }, []),
  );

  useEffect(() => {
    console.log('willKeyboardVisible ' + willKeyboardVisible);
    if (willKeyboardVisible) {
      setBottomPadding(bottomPaddingVal);
    } else {
      setBottomPadding(0);
    }
  }, [willKeyboardVisible]);

  // Function to handle the scroll event and update scroll position
  const handleScroll = (event: NativeSyntheticEvent<NativeScrollEvent>) => {
    setScrollPosition(event.nativeEvent.contentOffset.y);
  };

  // Function to scroll up by a specific amount
  const scrollUp = (
    scrollViewRef: React.RefObject<ScrollView>,
    scrollAmount: number,
  ) => {
    if (scrollViewRef.current) {
      console.log('scrollPosition ' + scrollPosition);
      scrollViewRef.current.scrollTo({
        y: Math.max(0, scrollPosition + scrollAmount),
        animated: true,
      });
    }
  };

  return {
    keyboardHeight,
    isKeyboardVisible,
    willKeyboardVisible,
    bottomPadding,
    handleScroll,
    scrollUp,
  };
};

export default useCommonController;
