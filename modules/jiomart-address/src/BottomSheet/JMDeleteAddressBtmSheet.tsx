import {StyleSheet, View} from 'react-native';
import React from 'react';
import {<PERSON><PERSON><PERSON><PERSON><PERSON>, JioText} from '@jio/rn_components';
import {ButtonSize, JioTypography} from '@jio/rn_components/src/index.types';
import JMBtmSheetHeader from '@jm/jiomart-general/src/ui/JMBtmSheetHeader';
import type {BottomSheetChildren} from '@jm/jiomart-general/src/ui/BottomSheet/types/BottomSheetType';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
interface JMDeleteAddressBtmSheetProps extends BottomSheetChildren {
  config: any;
  name: string;
  address: string;
  description: string;
  onDelete: () => void;
  onClose: () => void;
}

const JMDeleteAddressBtmSheet = (props: JMDeleteAddressBtmSheetProps) => {
  const {config, name, address, description, close, onClose, onDelete} = props;
  const insets = useSafeAreaInsets();
  return (
    <View style={[styles.container, {paddingBottom: insets.bottom}]}>
      <JMBtmSheetHeader
        title={config?.headerTitle ?? ''}
        onPress={() => close?.(onClose)}
      />
      <View style={styles.wrapper}>
        <JioText
          text={name}
          appearance={JioTypography.BODY_XS_BOLD}
          color="primary_grey_100"
          {...config?.name}
        />
        <JioText
          text={address}
          appearance={JioTypography.BODY_XXS}
          color={'primary_grey_80'}
          {...config?.address}
        />
        <JioText
          text={description}
          appearance={JioTypography.BODY_XXS}
          color={'primary_grey_80'}
          {...config?.description}
        />
      </View>
      <View style={styles.button}>
        <JioButton
          stretch
          size={ButtonSize.LARGE}
          onClick={() => {
            onDelete();
            close?.(onClose);
          }}
          {...config?.button}
        />
      </View>
    </View>
  );
};

export default JMDeleteAddressBtmSheet;

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#ffffff',
    borderTopRightRadius: 16,
    borderTopLeftRadius: 16,
  },
  wrapper: {marginHorizontal: 24},
  button: {
    borderTopWidth: 1,
    borderColor: '#E0E0E0',
    paddingVertical: 10,
    paddingHorizontal: 24,
    marginTop: 24,
  },
});
