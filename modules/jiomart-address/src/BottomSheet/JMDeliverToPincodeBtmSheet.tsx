import React from 'react';
import {useEffect, useRef} from 'react';
import {StyleSheet, TextInput, View} from 'react-native';
import {useState} from 'react';
import {JioButton, JioIcon, JioText} from '@jio/rn_components';
import {
  ButtonState,
  JioTypography,
  type JioColor,
} from '@jio/rn_components/src/index.types';
import {rh, rw} from '@jm/jiomart-common/src/JMResponsive';
import JMBtmSheetHeader from '@jm/jiomart-general/src/ui/JMBtmSheetHeader';
import type {BottomSheetChildren} from '@jm/jiomart-general/src/ui/BottomSheet/types/BottomSheetType';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import useAddressOperation from '../hooks/useAddressOperation';
import {capitalizeFirstLetter} from '@jm/jiomart-common/src/utils/JMStringUtility';
import {TouchableOpacity} from 'react-native-gesture-handler';
import {JMConfigFileName} from '@jm/jiomart-networkmanager/src/JMConfigFileManager/JMFileName';
import {useConfigFileFromCache} from '@jm/jiomart-general/src/hooks/useJMConfig';

interface JMDeliverToPincodeBtmSheetProps extends BottomSheetChildren {
  onClose?: () => void;
}

// const CONFIG = {
//   bottomSheet: {
//     deliverToBarPincode: {
//       title: 'Enter PIN Code',
//       description: {
//         title:
//           'Enter PIN code to see product availability, offers and discounts.',
//         color: 'primary_grey_80',
//       },
//       textInput: {
//         label: {
//           text: 'PIN Code',
//           color: 'primary_grey_80',
//         },
//         icon: {
//           ic: 'IcLocation',
//           color: 'primary_grey_80',
//         },
//         placeholder: 'Enter a Pincode',
//       },
//       button: {
//         apply: {
//           text: 'Apply',
//         },
//       },
//       message: {
//         invalidPincode: 'We are currently not delivering at this location',
//         pincodeChangeFailed:
//           'We are unable to process your request, retry later',
//       },
//       successIcon: {
//         ic: 'IcError',
//         size: 'small',
//         color: 'feedback_success',
//       },
//       errorIcon: {
//         ic: 'IcSuccess',
//         size: 'small',
//         color: 'feedback_error',
//       },
//     },
//   },
// };

const JMDeliverToPincodeBtmSheet = (props: JMDeliverToPincodeBtmSheetProps) => {
  const {close, onClose} = props;
  const insets = useSafeAreaInsets();
  // const config = CONFIG?.bottomSheet?.deliverToBarPincode;
  const config = useConfigFileFromCache(
    JMConfigFileName.JMAddressConfigurationFileNAme,
  )?.data?.bottomSheet?.deliverToBarPincode;
  const {getPincodeStatus, checkAndSetPincode} = useAddressOperation();

  const [pincode, setPincode] = useState('');
  const [isValidPincodeMessage, setIsValidPincodeMessage] = useState('');
  const [isValidPincode, setIsValidPincode] = useState<boolean | null>(null);

  const pincodeInputRef = useRef<TextInput>(null);

  const iconValidation = isValidPincode
    ? config?.successIcon
    : config?.errorIcon;

  const enterPincode = async (text: string) => {
    setPincode(text);
    if (text.length < 6) {
      setIsValidPincodeMessage('');
      setIsValidPincode(null);
    }
    if (text.length === 6) {
      getPincodeStatus.mutate(text, {
        onSuccess: response => {
          if (response?.success) {
            const city = capitalizeFirstLetter(
              response?.data?.[0]?.parents
                ?.find((it: any) => it?.sub_type === 'city')
                ?.name?.replace(/_/g, ' '),
            );
            const state = capitalizeFirstLetter(
              response?.data?.[0]?.parents
                ?.find((it: any) => it?.sub_type === 'state')
                ?.name?.replace(/_/g, ' '),
            );
            setIsValidPincodeMessage(`${city}, ${state}`);
            setIsValidPincode(true);
          } else {
            setIsValidPincodeMessage(config?.message?.invalidPincode);
            setIsValidPincode(false);
          }
        },
        onError: () => {
          setIsValidPincodeMessage(config?.message?.pincodeChangeFailed);
          setIsValidPincode(false);
        },
      });
    }
  };

  const resetPincodeInputField = () => {
    close?.(onClose);
  };

  const applyPincode = () => {
    try {
      checkAndSetPincode({
        pincode,
        city: isValidPincodeMessage?.split(',')?.[0],
        state: isValidPincodeMessage?.split(',')?.[1],
      });
      close?.(onClose);
    } catch (error) {}
  };

  useEffect(() => {
    setTimeout(() => {
      pincodeInputRef.current?.focus();
    }, 100);
  }, []);

  return (
    <View style={{paddingBottom: insets.bottom}}>
      <JMBtmSheetHeader
        title={config?.title ?? ''}
        onPress={resetPincodeInputField}
      />
      <JioText
        text={config?.description?.title ?? ''}
        style={styles.margin}
        color={config?.description?.color as JioColor}
        appearance={JioTypography.BODY_XS}
      />

      <View style={styles.container}>
        <View style={{justifyContent: 'center'}}>
          <JioText
            {...config?.textInput?.label}
            appearance={JioTypography.BODY_XS}
          />
          <View style={styles.textInputContainer}>
            <JioIcon {...config?.textInput?.icon} />
            <TextInput
              ref={pincodeInputRef}
              textContentType="postalCode"
              placeholder={config?.textInput?.placeholder}
              style={styles.textInput}
              keyboardType={'number-pad'}
              keyboardAppearance={'default'}
              editable
              enablesReturnKeyAutomatically
              value={pincode}
              onChangeText={enterPincode}
              autoComplete={'postal-code'}
              inputMode={'numeric'}
              maxLength={6}
              contextMenuHidden={true}
              selectionColor="#000000A6"
            />
            <TouchableOpacity activeOpacity={1} onPress={applyPincode}>
              <JioButton
                title={config?.button?.apply?.text ?? ''}
                state={
                  isValidPincode ? ButtonState.NORMAL : ButtonState.DISABLED
                }
              />
            </TouchableOpacity>
          </View>
          <View
            style={[
              styles.border,
              isValidPincode !== null
                ? isValidPincode
                  ? styles.valid
                  : styles.invalid
                : null,
            ]}
          />

          {pincode && isValidPincode !== null ? (
            <View style={{minHeight: rh(24), flexDirection: 'row'}}>
              <JioIcon
                {...iconValidation}
                style={[styles.marginTop, styles.marginRight]}
              />
              <JioText
                text={isValidPincodeMessage}
                maxLines={1}
                appearance={JioTypography.BODY_XXS}
                style={styles.marginTop}
                color={
                  isValidPincode !== null
                    ? isValidPincode
                      ? 'feedback_success_80'
                      : 'feedback_error_80'
                    : 'primary_inverse'
                }
              />
            </View>
          ) : null}
        </View>
      </View>
    </View>
  );
};

export default JMDeliverToPincodeBtmSheet;

const styles = StyleSheet.create({
  keyboardAvoidingContainer: {
    flexDirection: 'column',
    backgroundColor: 'white',
    justifyContent: 'space-between',
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
  },
  flex: {flex: 1},
  margin: {marginHorizontal: 24},
  container: {
    flexDirection: 'row',
    margin: 24,
  },
  textInputContainer: {
    flexDirection: 'row',
    columnGap: 8,
    marginBottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    // height: rh(40),
    fontSize: 16,
    // lineHeight: 24,
    letterSpacing: -0.05,
    fontFamily: 'JioType',
    fontStyle: 'normal',
    fontWeight: '500',
  },
  textInput: {
    width: rw(225),
    textDecorationColor: '#141414',
    fontSize: 16,
    lineHeight: 24,
    letterSpacing: -0.05,
    fontFamily: 'JioType',
    fontStyle: 'normal',
    fontWeight: '500',
  },
  border: {borderWidth: 1, width: rw(242), borderColor: '#000000A6'},
  valid: {borderColor: '#25AB21'},
  invalid: {borderColor: '#F50031'},
  marginTop: {marginTop: 4},
  marginRight: {marginRight: 4},
});
