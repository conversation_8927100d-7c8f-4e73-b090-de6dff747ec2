import React from 'react';
import {
  ActivityIndicator,
  FlatList,
  Keyboard,
  StyleSheet,
  TouchableOpacity,
  TouchableWithoutFeedback,
  View,
} from 'react-native';
import useJMAddressSearchScreenController from '../controller/useJMAddressSearchScreenController';
import ScreenSlot, {
  DeeplinkHandler,
} from '@jm/jiomart-general/src/ui/JMScreenSlot';
import type {JMAddressSearchScreenProps} from '../types/JMAddressSearchScreenType';
import {JioIcon, JioText} from '@jio/rn_components';
import Divider, {
  DividerGap,
  DividerType,
} from '@jm/jiomart-general/src/ui/Divider';
import JMSearchBar from '@jm/jiomart-general/src/ui/JMSearchBar';
import JMSearchSuggestionItem from '@jm/jiomart-general/src/ui/JMSearchSugestionItem';

const JMAddressSearchScreen = (props: JMAddressSearchScreenProps) => {
  const {
    navigationBean,
    navigation,
    config,
    handleSearchSuggestionClick,
    search,
    handleChangeText,
    suggestion,
    isFetchingLocation,
    handleCurrentLocation,
    suggestionClick,
    setSuggestionClick,
  } = useJMAddressSearchScreenController(props);

  const renderItemSeperator = () => (
    <Divider
      type={DividerType.THIN}
      horizontal={DividerGap.GAP24}
      top={DividerGap.GAP12}
    />
  );

  return (
    <DeeplinkHandler
      navigationBean={navigationBean}
      navigation={navigation}
      children={_ => (
        <ScreenSlot
          navigationBean={navigationBean}
          navigation={navigation}
          children={_ => {
            return (
              <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
                <View style={styles.container}>
                  <JioText
                    style={{marginTop: 16, marginHorizontal: 24}}
                    {...config?.headerTitle}
                  />
                  <JMSearchBar
                    style={styles.search}
                    textInput={{
                      value: search,
                      onChangeText: val => {
                        handleChangeText(val);
                      },
                      onFocus: () => {
                        setSuggestionClick(false);
                      },
                      placeholder: config?.searchBar?.placeholder,
                    }}
                  />
                  {search && !suggestionClick && suggestion?.length > 0 ? (
                    <FlatList
                      data={suggestion ?? []}
                      bounces={false}
                      renderItem={({item}) => {
                        return (
                          <JMSearchSuggestionItem
                            icon={{...config?.searchSuggestion?.icon}}
                            title={{
                              ...config?.searchSuggestion?.title,
                              text: item?.primaryText,
                            }}
                            subTitle={{
                              ...config?.searchSuggestion?.subTitle,
                              text: item?.description,
                            }}
                            onPress={handleSearchSuggestionClick(item?.placeID)}
                            style={styles.searchSuggestion}
                          />
                        );
                      }}
                      ItemSeparatorComponent={renderItemSeperator}
                    />
                  ) : (
                    <>
                      <Divider
                        text="OR"
                        type={DividerType.THIN}
                        right={DividerGap.GAP16}
                        left={DividerGap.GAP24}
                        top={DividerGap.GAP12}
                      />
                      {isFetchingLocation ? (
                        <ActivityIndicator
                          style={styles.loader}
                          {...config?.location?.loader}
                        />
                      ) : (
                        <TouchableOpacity
                          onPress={handleCurrentLocation}
                          activeOpacity={1}>
                          <View style={styles.location}>
                            <JioIcon {...config?.location?.icon} />
                            <View>
                              <JioText {...config?.location?.title} />
                              <JioText {...config?.location?.subTitle} />
                            </View>
                          </View>
                        </TouchableOpacity>
                      )}
                    </>
                  )}
                </View>
              </TouchableWithoutFeedback>
            );
          }}
        />
      )}
    />
  );
};

export default JMAddressSearchScreen;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
  },
  title: {marginTop: 16, marginHorizontal: 24},
  row: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  location: {
    flexDirection: 'row',
    alignItems: 'center',
    columnGap: 12,
    paddingVertical: 12,
    marginHorizontal: 24,
  },
  search: {
    marginHorizontal: 24,
    marginTop: 12,
    marginBottom: 16,
  },
  searchSuggestion: {
    marginHorizontal: 24,
  },
  loader: {
    alignSelf: 'flex-start',
    paddingHorizontal: 24,
    paddingVertical: 12,
  },
});
