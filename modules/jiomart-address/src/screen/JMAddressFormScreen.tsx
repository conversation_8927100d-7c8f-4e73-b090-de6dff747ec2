import React from 'react';
import {SafeAreaView, StyleSheet, TouchableOpacity, View} from 'react-native';
import type {JMAddressFormScreenProps} from '../types/JMAddressFormScreenType';
import useJMAddressFormScreenController, {
  AddressFormField,
  AddressType,
} from '../controller/useJMAddressFormScreenController';
import ScreenSlot, {
  DeeplinkHandler,
} from '@jm/jiomart-general/src/ui/JMScreenSlot';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view';
import {JioButton, JioText} from '@jio/rn_components';
import {ButtonKind, ButtonSize} from '@jio/rn_components/src/index.types';
import MapView, {Marker} from 'react-native-maps';
import {rh} from '@jm/jiomart-common/src/JMResponsive';
import {MapLocationMarker, MapTooltipSelected} from '../assets/icons';
import Divider, {
  DividerGap,
  DividerType,
} from '@jm/jiomart-general/src/ui/Divider';
import JMInputTextField from '@jm/jiomart-general/src/ui/JMInputTextField';
import Feedback from '@jio/rn_components/src/utils/Feedback';

const JMAddressFormScreen = (props: JMAddressFormScreenProps) => {
  const {
    navigation,
    navigationBean,
    address,
    regionCamera,
    config,
    formData,
    formError,
    handleInputRef,
    onChangeText,
    validateValue,
    handleFormError,
    handleAddressSubmit,
    handleSaveAs,
  } = useJMAddressFormScreenController(props);
  return (
    <DeeplinkHandler
      navigationBean={navigationBean}
      navigation={navigation}
      children={_ => (
        <ScreenSlot
          navigationBean={navigationBean}
          navigation={navigation}
          children={_ => {
            return (
              <SafeAreaView style={styles.wrapper}>
                <KeyboardAwareScrollView
                  extraScrollHeight={-50}
                  extraHeight={230}
                  showsHorizontalScrollIndicator={false}
                  showsVerticalScrollIndicator={false}
                  keyboardShouldPersistTaps="handled"
                  enableOnAndroid
                  bounces={false}>
                  <View style={styles.mapContainer}>
                    <MapView
                      {...config?.map}
                      initialCamera={regionCamera}
                      style={styles.map}>
                      <Marker
                        opacity={0}
                        coordinate={{
                          latitude: address?.lat,
                          longitude: address?.lon,
                        }}
                      />
                    </MapView>
                    <View style={styles.marker}>
                      <MapTooltipSelected />
                      <MapLocationMarker />
                    </View>
                  </View>
                  <View style={styles.form}>
                    <View style={styles.container}>
                      <View>
                        <JioText
                          {...config?.headerTitle}
                          text={address?.address}
                        />
                        <TouchableOpacity
                          onPress={() => {
                            navigation?.goBack();
                          }}
                          activeOpacity={0.65}
                          style={styles.editLocation}>
                          <JioText
                            {...config?.link}
                            style={styles.editLocationText}
                          />
                        </TouchableOpacity>
                        <Divider
                          type={DividerType.THIN}
                          vertical={DividerGap.GAP16}
                        />
                        <JioText {...config?.formHeaderTitle} />
                      </View>
                      <View style={styles.inputRow}>
                        <JMInputTextField
                          onRef={handleInputRef(AddressFormField.HOUSE_NO)}
                          isReadOnly={
                            config?.form?.[AddressFormField.HOUSE_NO]
                              ?.isReadOnly
                          }
                          textInput={{
                            ...config?.form?.[AddressFormField.HOUSE_NO]
                              ?.textInput,
                            value: formData[AddressFormField.HOUSE_NO],
                            onChangeText: onChangeText(
                              AddressFormField.HOUSE_NO,
                            ),
                            onBlur: handleFormError(
                              AddressFormField.HOUSE_NO,
                              validateValue(
                                formData[AddressFormField.HOUSE_NO],
                                config?.form?.[AddressFormField.HOUSE_NO],
                              ),
                            ),
                          }}
                          label={{
                            ...config?.form?.[AddressFormField.HOUSE_NO]?.label,
                          }}
                          feedback={{
                            ...config?.form?.[AddressFormField.HOUSE_NO]
                              ?.feedback,
                            stateText: formError[AddressFormField.HOUSE_NO],
                          }}
                        />
                        <JMInputTextField
                          onRef={handleInputRef(AddressFormField.FLOOR_NO)}
                          isReadOnly={
                            config?.form?.[AddressFormField.FLOOR_NO]
                              ?.isReadOnly
                          }
                          textInput={{
                            ...config?.form?.[AddressFormField.FLOOR_NO]
                              ?.textInput,
                            value: formData[AddressFormField.FLOOR_NO],
                            onChangeText: onChangeText(
                              AddressFormField.FLOOR_NO,
                            ),
                            onBlur: handleFormError(
                              AddressFormField.FLOOR_NO,
                              validateValue(
                                formData[AddressFormField.FLOOR_NO],
                                config?.form?.[AddressFormField.FLOOR_NO],
                              ),
                            ),
                          }}
                          label={{
                            ...config?.form?.[AddressFormField.FLOOR_NO]?.label,
                          }}
                          feedback={{
                            ...config?.form?.[AddressFormField.FLOOR_NO]
                              ?.feedback,
                            stateText: formError[AddressFormField.FLOOR_NO],
                          }}
                        />
                      </View>
                      <JMInputTextField
                        onRef={handleInputRef(AddressFormField.TOWER_NO)}
                        isReadOnly={
                          config?.form?.[AddressFormField.TOWER_NO]?.isReadOnly
                        }
                        textInput={{
                          ...config?.form?.[AddressFormField.TOWER_NO]
                            ?.textInput,
                          value: formData[AddressFormField.TOWER_NO],
                          onChangeText: onChangeText(AddressFormField.TOWER_NO),
                          onBlur: handleFormError(
                            AddressFormField.TOWER_NO,
                            validateValue(
                              formData[AddressFormField.TOWER_NO],
                              config?.form?.[AddressFormField.TOWER_NO],
                            ),
                          ),
                        }}
                        label={{
                          ...config?.form?.[AddressFormField.TOWER_NO]?.label,
                        }}
                        feedback={{
                          ...config?.form?.[AddressFormField.TOWER_NO]
                            ?.feedback,
                          stateText: formError[AddressFormField.TOWER_NO],
                        }}
                      />
                      <JMInputTextField
                        onRef={handleInputRef(
                          AddressFormField.BUILDING_APARTMENT_NAME,
                        )}
                        isReadOnly={
                          config?.form?.[
                            AddressFormField.BUILDING_APARTMENT_NAME
                          ]?.isReadOnly
                        }
                        textInput={{
                          ...config?.form?.[
                            AddressFormField.BUILDING_APARTMENT_NAME
                          ]?.textInput,
                          value:
                            formData[AddressFormField.BUILDING_APARTMENT_NAME],
                          onChangeText: onChangeText(
                            AddressFormField.BUILDING_APARTMENT_NAME,
                          ),
                          onBlur: handleFormError(
                            AddressFormField.BUILDING_APARTMENT_NAME,
                            validateValue(
                              formData[
                                AddressFormField.BUILDING_APARTMENT_NAME
                              ],
                              config?.form?.[
                                AddressFormField.BUILDING_APARTMENT_NAME
                              ],
                            ),
                          ),
                        }}
                        label={{
                          ...config?.form?.[
                            AddressFormField.BUILDING_APARTMENT_NAME
                          ]?.label,
                        }}
                        feedback={{
                          ...config?.form?.[
                            AddressFormField.BUILDING_APARTMENT_NAME
                          ]?.feedback,
                          stateText:
                            formError[AddressFormField.BUILDING_APARTMENT_NAME],
                        }}
                      />
                      <JMInputTextField
                        onRef={handleInputRef(AddressFormField.ADDRESS)}
                        isReadOnly={
                          config?.form?.[AddressFormField.ADDRESS]?.isReadOnly
                        }
                        textInput={{
                          ...config?.form?.[AddressFormField.ADDRESS]
                            ?.textInput,
                          value: formData[AddressFormField.ADDRESS],
                          onChangeText: onChangeText(AddressFormField.ADDRESS),
                          onBlur: handleFormError(
                            AddressFormField.ADDRESS,
                            validateValue(
                              formData[AddressFormField.ADDRESS],
                              config?.form?.[AddressFormField.ADDRESS],
                            ),
                          ),
                        }}
                        label={{
                          ...config?.form?.[AddressFormField.ADDRESS]?.label,
                        }}
                        feedback={{
                          ...config?.form?.[AddressFormField?.ADDRESS]
                            ?.feedback,
                          stateText: formError[AddressFormField.ADDRESS],
                        }}
                      />
                      <JMInputTextField
                        onRef={handleInputRef(AddressFormField.LANDMARK_AREA)}
                        isReadOnly={
                          config?.form?.[AddressFormField.LANDMARK_AREA]
                            ?.isReadOnly
                        }
                        textInput={{
                          ...config?.form?.[AddressFormField.LANDMARK_AREA]
                            ?.textInput,
                          value: formData[AddressFormField.LANDMARK_AREA],
                          onChangeText: onChangeText(
                            AddressFormField.LANDMARK_AREA,
                          ),
                          onBlur: handleFormError(
                            AddressFormField.LANDMARK_AREA,
                            validateValue(
                              formData[AddressFormField.LANDMARK_AREA],
                              config?.form?.[AddressFormField.LANDMARK_AREA],
                            ),
                          ),
                        }}
                        label={{
                          ...config?.form?.[AddressFormField.LANDMARK_AREA]
                            ?.label,
                        }}
                        feedback={{
                          ...config?.form?.[AddressFormField.LANDMARK_AREA]
                            ?.feedback,
                          stateText: formError[AddressFormField.LANDMARK_AREA],
                        }}
                      />
                      <View style={styles.inputRow}>
                        <JMInputTextField
                          onRef={handleInputRef(AddressFormField.PINCODE)}
                          isReadOnly={
                            config?.form?.[AddressFormField.PINCODE]?.isReadOnly
                          }
                          textInput={{
                            ...config?.form?.[AddressFormField.PINCODE]
                              ?.textInput,
                            value: formData[AddressFormField.PINCODE],
                            onChangeText: onChangeText(
                              AddressFormField.PINCODE,
                            ),
                            onBlur: handleFormError(
                              AddressFormField.PINCODE,
                              validateValue(
                                formData[AddressFormField.PINCODE],
                                config?.form?.[AddressFormField.PINCODE],
                              ),
                            ),
                          }}
                          label={{
                            ...config?.form?.[AddressFormField.PINCODE]?.label,
                          }}
                          feedback={{
                            ...config?.form?.[AddressFormField.PINCODE]
                              ?.feedback,
                            stateText: formError[AddressFormField.PINCODE],
                          }}
                        />
                        <JMInputTextField
                          onRef={handleInputRef(AddressFormField.CITY)}
                          isReadOnly={
                            config?.form?.[AddressFormField.CITY]?.isReadOnly
                          }
                          textInput={{
                            ...config?.form?.[AddressFormField.CITY]?.textInput,
                            value: formData[AddressFormField.CITY],
                            onChangeText: onChangeText(AddressFormField.CITY),
                            onBlur: handleFormError(
                              AddressFormField.CITY,
                              validateValue(
                                formData[AddressFormField.CITY],
                                config?.form?.[AddressFormField.CITY],
                              ),
                            ),
                          }}
                          label={{
                            ...config?.form?.[AddressFormField.CITY]?.label,
                          }}
                          feedback={{
                            ...config?.form?.[AddressFormField.CITY]?.feedback,
                            stateText: formError[AddressFormField.CITY],
                          }}
                        />
                      </View>
                      <JMInputTextField
                        onRef={handleInputRef(AddressFormField.STATE)}
                        isReadOnly={
                          config?.form?.[AddressFormField.STATE]?.isReadOnly
                        }
                        textInput={{
                          ...config?.form?.[AddressFormField.STATE]?.textInput,
                          value: formData[AddressFormField.STATE],
                          onChangeText: onChangeText(AddressFormField.STATE),
                          onBlur: handleFormError(
                            AddressFormField.STATE,
                            validateValue(
                              formData[AddressFormField.STATE],
                              config?.form?.[AddressFormField.STATE],
                            ),
                          ),
                        }}
                        label={{
                          ...config?.form?.[AddressFormField.STATE]?.label,
                        }}
                        feedback={{
                          ...config?.form?.[AddressFormField.STATE]?.feedback,
                          stateText: formError[AddressFormField.STATE],
                        }}
                      />
                    </View>
                    <Divider
                      type={DividerType.LARGE}
                      color={'primary_grey_20'}
                      vertical={DividerGap.GAP24}
                    />
                    <View style={styles.deliveryContainer}>
                      <View>
                        <JioText
                          {...config?.deliveryContactDetail?.headerTitle}
                          style={{marginBottom: 4}}
                        />
                        <JioText
                          {...config?.deliveryContactDetail?.subTitle}
                          style={{marginBottom: 4}}
                        />
                      </View>
                      <JMInputTextField
                        onRef={handleInputRef(AddressFormField.RECEIVER_NAME)}
                        isReadOnly={
                          config?.form?.[AddressFormField.RECEIVER_NAME]
                            ?.isReadOnly
                        }
                        textInput={{
                          ...config?.form?.[AddressFormField.RECEIVER_NAME]
                            ?.textInput,
                          value: formData[AddressFormField.RECEIVER_NAME],
                          onChangeText: onChangeText(
                            AddressFormField.RECEIVER_NAME,
                          ),
                          onBlur: handleFormError(
                            AddressFormField.RECEIVER_NAME,
                            validateValue(
                              formData[AddressFormField.RECEIVER_NAME],
                              config?.form?.[AddressFormField.RECEIVER_NAME],
                            ),
                          ),
                        }}
                        label={{
                          ...config?.form?.[AddressFormField.RECEIVER_NAME]
                            ?.label,
                        }}
                        feedback={{
                          ...config?.form?.[AddressFormField.RECEIVER_NAME]
                            ?.feedback,
                          stateText: formError[AddressFormField.RECEIVER_NAME],
                        }}
                      />
                      <JMInputTextField
                        onRef={handleInputRef(AddressFormField.RECEIVER_NUMBER)}
                        isReadOnly={
                          config?.form?.[AddressFormField.RECEIVER_NUMBER]
                            ?.isReadOnly
                        }
                        textInput={{
                          ...config?.form?.[AddressFormField.RECEIVER_NUMBER]
                            ?.textInput,
                          value: formData[AddressFormField.RECEIVER_NUMBER],
                          onChangeText: onChangeText(
                            AddressFormField.RECEIVER_NUMBER,
                          ),
                          onBlur: handleFormError(
                            AddressFormField.RECEIVER_NUMBER,
                            validateValue(
                              formData[AddressFormField.RECEIVER_NUMBER],
                              config?.form?.[AddressFormField.RECEIVER_NUMBER],
                            ),
                          ),
                        }}
                        label={{
                          ...config?.form?.[AddressFormField.RECEIVER_NUMBER]
                            ?.label,
                        }}
                        prefix={{
                          ...config?.form?.[AddressFormField.RECEIVER_NUMBER]
                            ?.prefix,
                        }}
                        feedback={{
                          ...config?.form?.[AddressFormField.RECEIVER_NUMBER]
                            ?.feedback,
                          stateText:
                            formError[AddressFormField.RECEIVER_NUMBER],
                        }}
                      />
                    </View>
                    <Divider
                      type={DividerType.LARGE}
                      color={'primary_grey_20'}
                      vertical={DividerGap.GAP24}
                    />
                    <View style={styles.saveAs}>
                      <JioText {...config?.saveAsHeaderTitle} />
                      <View style={styles.saveAsContentContainer}>
                        {config?.saveAs?.map((item: any) => (
                          <JioButton
                            title={item.title}
                            size={ButtonSize.SMALL}
                            kind={
                              formData[AddressFormField.ADDRESS_TYPE] ===
                              item?.value
                                ? ButtonKind.PRIMARY
                                : ButtonKind.SECONDARY
                            }
                            onClick={handleSaveAs(item?.value)}
                          />
                        ))}
                      </View>

                      <View>
                        {formError[AddressFormField.ADDRESS_TYPE] &&
                          formData[AddressFormField.ADDRESS_TYPE] ==
                            undefined &&
                          formData[AddressFormField.ADDRESS_TYPE] !=
                            AddressType.HOME &&
                          formData[AddressFormField.ADDRESS_TYPE] !=
                            AddressType.WORK && (
                            <Feedback
                              {...config?.form?.[AddressFormField.ADDRESS_TYPE]
                                ?.feedback}
                              stateText={
                                formError[AddressFormField.ADDRESS_TYPE]
                              }
                            />
                          )}
                      </View>
                      {formData[AddressFormField.ADDRESS_TYPE] != undefined &&
                      formData[AddressFormField.ADDRESS_TYPE] != null &&
                      ![AddressType.HOME, AddressType.WORK]?.includes(
                        formData[AddressFormField.ADDRESS_TYPE],
                      ) ? (
                        <JMInputTextField
                          onRef={handleInputRef(AddressFormField.ADDRESS_TYPE)}
                          isReadOnly={
                            config?.form?.[AddressFormField.ADDRESS_TYPE]
                              ?.isReadOnly
                          }
                          textInput={{
                            ...config?.form?.[AddressFormField.ADDRESS_TYPE]
                              ?.textInput,
                            value: formData[AddressFormField.ADDRESS_TYPE],
                            onChangeText: onChangeText(
                              AddressFormField.ADDRESS_TYPE,
                            ),
                            onBlur: handleFormError(
                              AddressFormField.ADDRESS_TYPE,
                              validateValue(
                                formData[AddressFormField.ADDRESS_TYPE],
                                config?.form?.[AddressFormField.ADDRESS_TYPE],
                              ),
                            ),
                          }}
                          label={{
                            ...config?.form?.[AddressFormField.ADDRESS_TYPE]
                              ?.label,
                          }}
                          feedback={{
                            ...config?.form?.[AddressFormField.ADDRESS_TYPE]
                              ?.feedback,
                            stateText: formError[AddressFormField.ADDRESS_TYPE],
                          }}
                        />
                      ) : null}
                    </View>
                  </View>
                </KeyboardAwareScrollView>
                <View style={styles.submit}>
                  <JioButton
                    stretch
                    size={ButtonSize.LARGE}
                    {...config?.submitButton}
                    style={{marginHorizontal: 24}}
                    onClick={handleAddressSubmit}
                  />
                </View>
              </SafeAreaView>
            );
          }}
        />
      )}
    />
  );
};

export default JMAddressFormScreen;

const styles = StyleSheet.create({
  wrapper: {flex: 1, backgroundColor: '#ffffff'},
  form: {
    backgroundColor: '#ffffff',
    marginTop: rh(129),
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
  },
  inputRow: {flexDirection: 'row', columnGap: 16},
  mapContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
  },
  map: {flex: 1, height: rh(200)},
  editLocationText: {alignSelf: 'flex-start'},
  marker: {
    position: 'absolute',
    flexDirection: 'column',
    alignItems: 'center',
    alignSelf: 'center',
  },
  container: {
    paddingTop: 16,
    paddingHorizontal: 24,
    rowGap: 12,
  },
  editLocation: {
    marginTop: 8,
    paddingVertical: 8,
  },
  deliveryContainer: {
    paddingHorizontal: 24,
    backgroundColor: '#ffffff',
    rowGap: 12,
  },
  saveAs: {
    flex: 1,
    marginHorizontal: 24,
    columnGap: 16,
    marginBottom: 26,
  },
  submit: {
    borderTopWidth: 1,
    borderTopColor: '#E0E0E0',
    paddingVertical: 12,
  },
  saveAsContentContainer: {
    marginVertical: 12,
    flexDirection: 'row',
    columnGap: 8,
  },
});
