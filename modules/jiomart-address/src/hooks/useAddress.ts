import {getPrefString} from '@jm/jiomart-common/src/JMAsyncStorageHelper';
import {AsyncStorageKeys} from '@jm/jiomart-common/src/JMConstants';
import type {JMAddressModel} from '@jm/jiomart-networkmanager/src/models/Address/JMAddressModel';
import {updateDBAddress} from '../utils';

const useAddress = () => {
  const setIntialAddress = (address: JMAddressModel) => {
    getPrefString(AsyncStorageKeys.X_LOCATION_DETAIL)
      .then(val => {
        if (!val) {
          updateDBAddress(address);
        }
      })
      .catch(() => {
        updateDBAddress(address);
      });
  };

  const getDefaultAddress = async () => {
    try {
      return await getPrefString(AsyncStorageKeys.X_LOCATION_DETAIL);
    } catch (error) {
      throw error;
    }
  };

  return {setIntialAddress, getDefaultAddress};
};

export default useAddress;
