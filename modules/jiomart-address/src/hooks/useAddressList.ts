import {useQuery} from '@tanstack/react-query';
import {queryOptions} from '@tanstack/react-query';
import {RQKey} from '@jm/jiomart-common/src/JMConstants';
import JMAddressNetworkController from '@jm/jiomart-networkmanager/src/JMNetworkController/JMAddressNetworkController';

const addressController = new JMAddressNetworkController();

export const addressListOption = () => {
  return queryOptions({
    queryKey: [RQKey.GET_ADDRESS],
    queryFn: addressController.getAddressList,
  });
};

const useAddressList = () => {
  return useQuery(addressListOption());
};

export default useAddressList;
