import {useMutation, useQueryClient} from '@tanstack/react-query';
import {RQKey} from '@jm/jiomart-common/src/JMConstants';
import type {JMAddressModel} from '@jm/jiomart-networkmanager/src/models/Address/JMAddressModel';
import {JMSharedViewModel} from '@jm/jiomart-common/src/JMSharedViewModel';
import {AppSourceType} from '@jm/jiomart-common/src/SourceType';
import {normalizeJMAddress, updateDBAddress} from '../utils';
import {emitPincodeChange} from '../Event';
import JMAddressNetworkController from '@jm/jiomart-networkmanager/src/JMNetworkController/JMAddressNetworkController';

const addressController = new JMAddressNetworkController();

const useAddressOperation = () => {
  const queryClient = useQueryClient();

  const address: any = queryClient.getQueryData([RQKey.GET_ADDRESS]);

  const getPincodeStatus = useMutation({
    mutationFn: addressController.fetchPincodeCity,
  });
  const setDefaultAddress = useMutation({
    mutationFn: addressController.defaultAddress,
    onSuccess: (response: any, variable) => {
      if (response?.success) {
        queryClient.invalidateQueries({
          queryKey: [RQKey.GET_ADDRESS],
        });
        let formatedAddress: any;
        switch (JMSharedViewModel.Instance.appSource) {
          case AppSourceType.JM_JCP:
            formatedAddress = normalizeJMAddress({
              is_default_address: variable?.is_default_address,
              pin: variable?.pincode,
              name: variable?.name,
              phone: variable?.phone,
              address: variable?.address,
              area: variable?.area,
              city: variable?.city,
              state: variable?.state,
            });
            break;
          case AppSourceType.JM_BAU:
            // bau formated need
            formatedAddress = normalizeJMAddress({
              is_default_address: variable?.is_default_address,
              pin: variable?.pincode,
              name: variable?.name,
              phone: variable?.phone,
              address: variable?.address,
              area: variable?.area,
              city: variable?.city,
              state: variable?.state,
            });
            break;
        }
        updateDBAddress(formatedAddress);
        emitPincodeChange();
      }
    },
  });
  const removeAddress = useMutation({
    mutationFn: addressController.removeAddress,
    onSuccess: (response: any) => {
      if (response?.is_deleted) {
        queryClient.invalidateQueries({
          queryKey: [RQKey.GET_ADDRESS],
        });
      }
    },
  });
  const saveAddress = useMutation({
    mutationFn: addressController.insertAddress,
    onSuccess: (response: any, variable) => {
      if (response?.success) {
        queryClient.invalidateQueries({
          queryKey: [RQKey.GET_ADDRESS],
        });
        let formatedAddress: any;
        switch (JMSharedViewModel.Instance.appSource) {
          case AppSourceType.JM_JCP:
            formatedAddress = normalizeJMAddress({
              is_default_address: variable?.is_default_address,
              pin: variable?.area_code,
              name: variable?.name,
              phone: variable?.phone,
              address: variable?.address,
              area: variable?.area,
              city: variable?.city,
              state: variable?.state,
            });
            break;
          case AppSourceType.JM_BAU:
            // bau formated need
            formatedAddress = normalizeJMAddress({
              is_default_address: variable?.is_default_address,
              pin: variable?.area_code,
              name: variable?.name,
              phone: variable?.phone,
              address: variable?.address,
              area: variable?.area,
              city: variable?.city,
              state: variable?.state,
            });
            break;
        }

        updateDBAddress(formatedAddress);
        emitPincodeChange();
      }
    },
  });
  const updateAddress = useMutation({
    mutationFn: addressController.editAddress,
    onSuccess: (response: any, variable) => {
      if (response?.success) {
        queryClient.invalidateQueries({
          queryKey: [RQKey.GET_ADDRESS],
        });
        let formatedAddress: any;
        switch (JMSharedViewModel.Instance.appSource) {
          case AppSourceType.JM_JCP:
            formatedAddress = normalizeJMAddress({
              is_default_address: variable?.is_default_address,
              pin: variable?.area_code,
              name: variable?.name,
              phone: variable?.phone,
              address: variable?.address,
              area: variable?.area,
              city: variable?.city,
              state: variable?.state,
            });
            break;
          case AppSourceType.JM_BAU:
            // bau formated need
            formatedAddress = normalizeJMAddress({
              is_default_address: variable?.is_default_address,
              pin: variable?.area_code,
              name: variable?.name,
              phone: variable?.phone,
              address: variable?.address,
              area: variable?.area,
              city: variable?.city,
              state: variable?.state,
            });
            break;
        }
        updateDBAddress(formatedAddress);
        emitPincodeChange();
      }
    },
  });

  const checkAndSetPincode = async (requestData: any) => {
    try {
      const {pincode, state, city} = requestData;

      const parseAddress = address ?? [];
      const addressPresentInList = parseAddress?.find(
        (it: JMAddressModel) => it.pin === pincode,
      );

      if (addressPresentInList && true) {
        const request = generateDefaultAddressRequestBody(addressPresentInList);
        setDefaultAddress.mutate(request);
      } else {
        const changePincodeDetail: JMAddressModel = {
          pin: pincode,
          state,
          city,
        };
        updateDBAddress(changePincodeDetail);
        emitPincodeChange();
      }
    } catch (error) {}
  };

  const generateDefaultAddressRequestBody = (item: JMAddressModel) => {
    switch (JMSharedViewModel.Instance.appSource) {
      case AppSourceType.JM_JCP:
        return {
          id: item?.id,
          is_default_address: true,
          pincode: item?.pin,
          name: item?.name,
          phone: item?.phone,
          address: item?.address,
          area: item?.area,
          city: item?.city,
          state: item?.state,
        };
      case AppSourceType.JM_BAU:
        return {};
      default:
        throw new Error('app source not found');
    }
  };
  const generateRemoveAddressRequestBody = (item: JMAddressModel) => {
    switch (JMSharedViewModel.Instance.appSource) {
      case AppSourceType.JM_JCP:
        return {id: item?.id};
      case AppSourceType.JM_BAU:
        return {};
      default:
        throw new Error('app source not found');
    }
  };
  const generateSaveAddressRequestBody = (item: JMAddressModel) => {
    switch (JMSharedViewModel.Instance.appSource) {
      case AppSourceType.JM_JCP:
        const _custom_json = {
          flat_or_house_no: item?.flat_or_house_no,
          floor_no: item?.floor_no,
          tower_no: item?.tower_no,
          input_mode: item?.input_mode,
        };
        const geo_location = {
          longitude: item?.lon,
          latitude: item?.lat,
        };
        const bodyParam = {
          is_default_address: item?.is_default_address,
          name: item?.name,
          phone: item?.phone,
          address: item?.address,
          area: item?.area,
          landmark: item?.landmark,
          area_code: item?.pin,
          address_type: item?.address_type,
          geo_location: geo_location,
          _custom_json,
          city: item?.city,
          state: item?.state,
        };
        return bodyParam;
      case AppSourceType.JM_BAU:
        return {};
      default:
        throw new Error('app source not found');
    }
  };
  const generateUpdateAddressRequestBody = (item: JMAddressModel) => {
    switch (JMSharedViewModel.Instance.appSource) {
      case AppSourceType.JM_JCP:
        const _custom_json = {
          flat_or_house_no: item?.flat_or_house_no,
          floor_no: item?.floor_no,
          tower_no: item?.tower_no,
          input_mode: item?.input_mode,
        };
        const geo_location = {
          longitude: item?.lon,
          latitude: item?.lat,
        };
        const bodyParam = {
          id: item?.id,
          is_default_address: item?.is_default_address,
          name: item?.name,
          phone: item?.phone,
          address: item?.address,
          area: item?.area,
          landmark: item?.landmark,
          area_code: item?.pin,
          address_type: item?.address_type,
          geo_location: geo_location,
          _custom_json,
          city: item?.city,
          state: item?.state,
        };
        return bodyParam;
      case AppSourceType.JM_BAU:
        return {};
      default:
        throw new Error('app source not found');
    }
  };

  return {
    getPincodeStatus,
    setDefaultAddress,
    removeAddress,
    saveAddress,
    updateAddress,
    checkAndSetPincode,
    generateDefaultAddressRequestBody,
    generateRemoveAddressRequestBody,
    generateSaveAddressRequestBody,
    generateUpdateAddressRequestBody,
  };
};

export default useAddressOperation;
