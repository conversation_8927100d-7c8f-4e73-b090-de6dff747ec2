import {useEffect} from 'react';
import {
  subscribeToRNEvent,
  unsubscribeToRNEvent,
} from '@jm/jiomart-common/src/Emitter';
import {EventEmitterKeys} from '@jm/jiomart-common/src/JMConstants';

const usePincodeChange = (callback?: () => void) => {
  useEffect(() => {
    const handlePincodeChangeEvent = () => {
      callback?.();
    };
    subscribeToRNEvent(
      EventEmitterKeys.PINCODE_CHANGE,
      handlePincodeChangeEvent,
    );
    return () => {
      unsubscribeToRNEvent(
        EventEmitterKeys.PINCODE_CHANGE,
        handlePincodeChangeEvent,
      );
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);
};

export default usePincodeChange;
