import {useState} from 'react';
import {Alert, Platform} from 'react-native';
import {
  AndroidPermission,
  IOSPermission,
} from '@jm/jiomart-common/src/JMConstants';
import {
  checkAndRequestSinglePermission,
  openAppSetting,
  type JMPermissionType,
} from '@jm/jiomart-common/src/JMPermission';
import {RESULTS} from 'react-native-permissions';
import {getCurrentPosition} from '@jm/jiomart-common/src/GeolocationUtility';
import {getReverseGeoCodeFromLatLongNB} from '@jm/jiomart-general/src/bridge/JMRNAddressBridge';

interface UseCurrentLocationProps {
  alertBlocked: {
    title: string;
    message?: string;
    button?: {
      text?: string;
    };
  };
}

const useCurrentLocation = (props: UseCurrentLocationProps) => {
  const {alertBlocked} = props;
  const [isFetchingLocation, setIsFetchingLocation] = useState(false);
  const LocationPermission = Platform.select({
    ios: IOSPermission.LOCATION_WHEN_IN_USE,
    android: AndroidPermission.ACCESS_FINE_LOCATION,
  });

  const fetchCurrentLocation = async () => {
    try {
      setIsFetchingLocation(true);
      const status = await checkAndRequestSinglePermission(
        LocationPermission as JMPermissionType,
      );
      switch (status) {
        case RESULTS.GRANTED:
        case RESULTS.LIMITED:
          return await getCurrentPosition();
        case RESULTS.BLOCKED:
          Alert.alert(alertBlocked.title, alertBlocked.message, [
            {
              text: alertBlocked.button?.text,
              onPress: () => {
                openAppSetting();
              },
            },
          ]);
          return Promise.resolve(status);
        default:
          return Promise.resolve(status);
      }
    } catch (error) {
      return Promise.reject(RESULTS.UNAVAILABLE);
    } finally {
      setIsFetchingLocation(false);
    }
  };

  const fetchLocationFromReverseGeoCodeFromLatLong = async () => {
    const geoCords: any = await fetchCurrentLocation();
    const lat = geoCords?.coords?.latitude ?? 0;
    const long = geoCords?.coords?.longitude ?? 0;
    const address = await getReverseGeoCodeFromLatLongNB(lat, long);
    return {
      address: JSON.parse(address ?? ''),
      coords: geoCords?.coords,
    };
  };

  return {
    isFetchingLocation,
    fetchCurrentLocation,
    fetchLocationFromReverseGeoCodeFromLatLong,
  };
};

export default useCurrentLocation;
