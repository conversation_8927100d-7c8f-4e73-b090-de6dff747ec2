import {navBeanObj} from '@jm/jiomart-common/src/JMNavGraphUtil';
import {navigateTo} from '@jm/jiomart-general/src/navigation/JMNavGraph';
import type {UseJMAddressSearchScreenProps} from '../types/JMAddressSearchScreenType';
import useCurrentLocation from '../hooks/useCurrentLocation';
import type {JMAddressModel} from '@jm/jiomart-networkmanager/src/models/Address/JMAddressModel';
import type {GeoCoordinates} from 'react-native-geolocation-service';
import useAddressSearchSuggestion from '../hooks/useAddressSearchSuggestion';
import {useState} from 'react';
import {useConfigFileFromCache} from '@jm/jiomart-general/src/hooks/useJMConfig';
import {JMConfigFileName} from '@jm/jiomart-networkmanager/src/JMConfigFileManager/JMFileName';

// const config = {
//   headerTitle: {
//     text: 'To add a new address, search for your area, landmark, street name or apartment.',
//     appearance: 'heading_xxs',
//     color: 'primary_grey_80',
//   },
//   shouldShowNoOfSearchSuggestion: 4,
//   googlePredictionFilter: {
//     countries: ['in'],
//   },
//   alert: {
//     blockedLocation: {
//       title: '',
//       message:
//         'Please provide your location or search using your closest landmark/apartment name',
//       button: {
//         text: 'Enable Now',
//       },
//     },
//   },
//   location: {
//     icon: {
//       ic: 'IcCameraFocus',
//       color: 'primary_60',
//       size: 'medium',
//     },
//     title: {
//       text: 'Use Current Location',
//       color: 'primary_60',
//       appearance: 'body_s_bold',
//     },
//     subTitle: {
//       text: 'For more accurate delivery using GPS',
//       color: 'primary_grey_80',
//       appearance: 'body_xxs',
//     },
//     loader: {
//       animating: true,
//       hidesWhenStopped: false,
//       color: '#0C5273',
//       size: 'small',
//     },
//   },
//   searchBar: {
//     placeholder: 'Search for area, landmark',
//   },
//   searchSuggestion: {
//     icon: {
//       ic: 'IcLocation',
//       color: 'primary_grey_80',
//       size: 'medium',
//     },
//     title: {
//       text: '',
//       color: 'primary_grey_100',
//       appearance: 'body_s',
//     },
//     subTitle: {
//       text: '',
//       color: 'primary_grey_80',
//       appearance: 'body_xxs',
//       maxLines: 1,
//     },
//     cta: {
//       navTitle: 'Add Address',
//       HeaderType: 1,
//       source: '',
//       destination: 'JMAddressMapScreen',
//       actionType: 'T001',
//       actionUrl: '',
//       bundle: '',
//     },
//   },
// };

const useJMAddressSearchScreenController = (
  props: UseJMAddressSearchScreenProps,
) => {
  const {navigation, route} = props;
  const config = useConfigFileFromCache(
    JMConfigFileName.JMAddressConfigurationFileNAme,
  )?.data?.screen?.addressSearch;

  const {isFetchingLocation, fetchLocationFromReverseGeoCodeFromLatLong} =
    useCurrentLocation({
      alertBlocked: config?.alert?.blockedLocation,
    });
  const {search, suggestion, handleChangeText, handleSearchSuggestion} =
    useAddressSearchSuggestion({
      shouldShowNoOfSearchSuggestion: config?.shouldShowNoOfSearchSuggestion,
      predictionFilter: config?.googlePredictionFilter,
    });
  const [suggestionClick, setSuggestionClick] = useState(false);

  const handleSearchSuggestionClick = (placeId: string) => {
    return async () => {
      setSuggestionClick(true);
      const response = await handleSearchSuggestion(placeId);
      if (response) {
        handleChangeText('');
        handleMapRedirection(
          response?.place?.coordinate as GeoCoordinates,
          JSON.parse(response?.address ?? ''),
        );
      }
    };
  };

  const handleCurrentLocation = async () => {
    const {address, coords}: any =
      await fetchLocationFromReverseGeoCodeFromLatLong();
    handleMapRedirection(coords, address);
  };

  const handleMapRedirection = (
    geoCoords: GeoCoordinates,
    address?: JMAddressModel,
  ) => {
    navigateTo(
      navBeanObj({
        ...config?.searchSuggestion?.cta,
        params: {coords: geoCoords, address},
      }),
      navigation,
    );
  };
  return {
    ...props,
    search,
    suggestion,
    handleChangeText,
    handleSearchSuggestionClick,
    handleCurrentLocation,
    isFetchingLocation,
    config,
    suggestionClick,
    setSuggestionClick,
    navigationBean: route.params,
  };
};

export default useJMAddressSearchScreenController;
