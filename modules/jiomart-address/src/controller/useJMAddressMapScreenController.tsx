import {useRef, useState} from 'react';
import {Platform} from 'react-native';
import useCurrentLocation from '../hooks/useCurrentLocation';
import type {UseJMAddressMapScreenProps} from '../types/JMAddressMapScreenType';
import type MapView from 'react-native-maps';
import type {Region} from 'react-native-maps';
import {JMAddressModel} from '@jm/jiomart-networkmanager/src/models/Address/JMAddressModel';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import {getReverseGeoCodeFromLatLongNB} from '@jm/jiomart-general/src/bridge/JMRNAddressBridge';
import type {GeoPosition} from 'react-native-geolocation-service';
import useAddressSearchSuggestion from '../hooks/useAddressSearchSuggestion';
import {navigateTo} from '@jm/jiomart-general/src/navigation/JMNavGraph';
import {navBeanObj} from '@jm/jiomart-common/src/JMNavGraphUtil';
import {useConfigFileFromCache} from '@jm/jiomart-general/src/hooks/useJMConfig';
import {JMConfigFileName} from '@jm/jiomart-networkmanager/src/JMConfigFileManager/JMFileName';

// const config = {
//   headerTitle: {
//     text: 'To add a new address, search for your area, landmark, street name or apartment.',
//     appearance: 'heading_xxs',
//     color: 'primary_grey_80',
//   },
//   shouldShowNoOfSearchSuggestion: 4,
//   googlePredictionFilter: {
//     countries: ['in'],
//   },
//   alert: {
//     blockedLocation: {
//       title: '',
//       message:
//         'Please provide your location or search using your closest landmark/apartment name',
//       button: {
//         text: 'Enable Now',
//       },
//     },
//     invalidLocation: {
//       leftIcon: {
//         ic: 'IcWarning',
//         size: 'medium',
//         color: 'feedback_warning',
//       },
//       title: {
//         text: 'Unable to fetch location',
//         appearance: 'body_xxs_bold',
//       },
//       subTitle: {
//         text: "You haven't set your location pin yet! Please confirm your location to proceed",
//         appearance: 'body_xxs',
//         color: 'black',
//       },
//       backgroundColor: 'feedback_warning_20',
//     },
//   },
//   searchSuggestion: {
//     icon: {
//       ic: 'IcLocation',
//       color: 'primary_grey_80',
//       size: 'medium',
//     },
//     title: {
//       text: '',
//       color: 'primary_grey_100',
//       appearance: 'body_s',
//     },
//     subTitle: {
//       text: '',
//       color: 'primary_grey_80',
//       appearance: 'body_xxs',
//       maxLines: 1,
//     },
//   },
//   searchBar: {
//     placeholder: 'Search for area, landmark',
//   },
//   map: {
//     scrollEnabled: true,
//     zoomEnabled: true,
//     pitchEnabled: false,
//     rotateEnabled: true,
//     showsCompass: false,
//     showsBuildings: true,
//     showsTraffic: false,
//     showsIndoors: false,
//     provider: 'google',
//     initialCamera: {
//       pitch: 0,
//       heading: 0,
//       zoom: 18,
//     },
//   },
//   cameraFocus: {
//     icon: {
//       ic: 'IcCameraFocus',
//       color: 'primary_grey_80',
//     },
//     loader: {
//       animating: true,
//       hidesWhenStopped: false,
//       color: '#0C5273',
//       size: 'small',
//     },
//   },
//   addressBox: {
//     title: {
//       text: 'YOUR LOCATION',
//       appearance: 'overline',
//       color: 'primary_grey_60',
//     },
//     address: {
//       icon: {
//         ic: 'IcLocation',
//         color: 'primary_grey_80',
//         size: 'medium',
//       },
//       subTitle: {
//         text: '',
//         color: 'primary_grey_80',
//         appearance: 'body_xxs',
//       },
//       button: {
//         title: 'Confirm Location',
//         size: 'large',
//         kind: 'primary',
//         stretch: true,
//         cta: {
//           navTitle: 'Add Address',
//           HeaderType: 1,
//           source: '',
//           destination: 'JMAddressFormScreen',
//           actionType: 'T001',
//           actionUrl: '',
//           bundle: '',
//         },
//       },
//     },
//   },
// };
const useJMAddressMapScreenController = (props: UseJMAddressMapScreenProps) => {
  const {route, navigation} = props;
  const config = useConfigFileFromCache(
    JMConfigFileName.JMAddressConfigurationFileNAme,
  )?.data?.screen?.addressMap;
  const location = route.params?.params?.coords;
  const address = route.params?.params?.address;
  const regionCamera = {
    ...config?.map?.initialCamera,
    center: {
      latitude: location.latitude,
      longitude: location.longitude,
    },
  };

  const insets = useSafeAreaInsets();
  const {isFetchingLocation, fetchCurrentLocation} = useCurrentLocation({
    alertBlocked: config?.alert?.blockedLocation,
  });

  const {search, suggestion, handleChangeText, handleSearchSuggestion} =
    useAddressSearchSuggestion({
      shouldShowNoOfSearchSuggestion: config?.shouldShowNoOfSearchSuggestion,
      predictionFilter: config?.googlePredictionFilter,
    });

  const [currentAddress, setCurrentAddress] = useState<JMAddressModel | null>(
    address,
  );
  const [isMoving, setIsMoving] = useState(false);
  const [suggestionClick, setSuggestionClick] = useState(false);

  const savedAddress = useRef<JMAddressModel>(address);
  const isInitialRegionChange = useRef(true);
  const mapViewRef = useRef<MapView | null>(null);

  const handleSearchSuggestionClick = (placeId: string) => {
    return async () => {
      setSuggestionClick(true);
      const cameraFocus = await mapViewRef.current?.getCamera();
      const response = await handleSearchSuggestion(placeId);
      if (response?.place?.coordinate && cameraFocus) {
        cameraFocus.center.latitude = response?.place?.coordinate?.latitude;
        cameraFocus.center.longitude = response?.place?.coordinate?.longitude;
        mapViewRef.current?.animateCamera(cameraFocus);
      }
    };
  };
  const animateCamera = async () => {
    const position = (await fetchCurrentLocation()) as GeoPosition;
    const cameraFocus = await mapViewRef.current?.getCamera();
    if (position?.coords && cameraFocus) {
      cameraFocus.center.latitude = position?.coords?.latitude;
      cameraFocus.center.longitude = position?.coords?.longitude;
      mapViewRef.current?.animateCamera(cameraFocus);
    }
  };
  const onRegionChange = () => {
    console.log('🚀 ~ onRegionChange ~ onRegionChange:');
    if (isInitialRegionChange.current) {
      console.log('🚀 ~ onRegionChange ~ skipping initial region change on Android');
      return;
    }
    setIsMoving(true);
  };
  const onRegionChangeComplete = async (region: Region) => {
    try {
      console.log('🚀 ~ onRegionChangeComplete ~ onRegionChangeComplete:');
      if (isInitialRegionChange.current) {
        console.log('🚀 ~ onRegionChangeComplete ~ skipping initial region change on Android');
        isInitialRegionChange.current = false;
        return;
      }
      console.log('🚀 ~ onRegionChangeComplete ~ processing region change');
      const lat = region?.latitude ?? 0;
      const long = region?.longitude ?? 0;
      if (lat && long) {
        let latestAddress = await getReverseGeoCodeFromLatLongNB(lat, long);
        latestAddress = JSON.parse(latestAddress ?? '');
        if (latestAddress?.address) {
          setCurrentAddress(prev => {
            return {
              ...savedAddress.current,
              ...prev,
              ...latestAddress,
            };
          });
        } else {
          setCurrentAddress(null);
        }
      }
    } catch (error) {
      console.log('🚀 ~ onRegionChangeComplete ~ error:', error);
      setCurrentAddress(null);
    } finally {
      setIsMoving(false);
    }
  };
  const handleConfirmLocation = () => {
    navigateTo(
      navBeanObj({
        ...config?.addressBox?.address?.button?.cta,
        params: {
          address: currentAddress,
        },
      }),
      navigation,
    );
  };

  const onMapReady = async () => {
    console.log('🚀 ~ onMapReady ~ Map is ready');
    if (Platform.OS === 'android') {
      setTimeout(() => {
        if (mapViewRef.current) {
          mapViewRef.current.getCamera().then(camera => {
            mapViewRef.current?.animateCamera(camera);
          });
        }
      }, 300);
    }
  };

  return {
    ...props,
    config,
    mapViewRef,
    regionCamera,
    isFetchingLocation,
    location,
    animateCamera,
    search,
    handleChangeText,
    handleSearchSuggestionClick,
    suggestion,
    currentAddress,
    insets,
    isMoving,
    onRegionChange,
    onRegionChangeComplete,
    onMapReady, // Add the onMapReady handler
    suggestionClick,
    setSuggestionClick,
    handleConfirmLocation,
    navigationBean: route.params,
  };
};

export default useJMAddressMapScreenController;
