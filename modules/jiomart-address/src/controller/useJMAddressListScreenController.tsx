import useAddressList from '../hooks/useAddressList';
import type {UseJMAddressListScreenProps} from '../types/JMAddressListScreenType';
import {navigateTo} from '@jm/jiomart-general/src/navigation/JMNavGraph';
import {
  navBeanObj,
  type NavigationBean,
} from '@jm/jiomart-common/src/JMNavGraphUtil';
import {useRef, useState} from 'react';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import useAddressOperation from '../hooks/useAddressOperation';
import {Platform, type FlatList} from 'react-native';
import {useConfigFileFromCache} from '@jm/jiomart-general/src/hooks/useJMConfig';
import {JMConfigFileName} from '@jm/jiomart-networkmanager/src/JMConfigFileManager/JMFileName';

// const config = {
//   headerTitle: {
//     text: 'Saved Address',
//     appearance: 'heading_xxs',
//     color: 'primary_grey_100',
//   },
//   shouldShowNoOfAddress: 20,
//   dropDown: {
//     default: [
//       {
//         isVisible: true,
//         type: 'EDIT',
//         icon: {
//           ic: 'IcEditPen',
//           color: 'primary_grey_80',
//         },
//         label: {
//           text: 'Edit',
//         },
//         cta: {
//           navTitle: 'Add Address',
//           HeaderType: 1,
//           source: '',
//           destination: 'JMAddressMapScreen',
//           actionType: 'T001',
//           actionUrl: '',
//           bundle: '',
//         },
//       },
//     ],
//     normal: [
//       {
//         isVisible: true,
//         type: 'EDIT',
//         icon: {
//           ic: 'IcEditPen',
//           color: 'primary_grey_80',
//         },
//         label: {
//           text: 'Edit',
//         },
//         cta: {
//           navTitle: 'Add Address',
//           HeaderType: 1,
//           source: '',
//           destination: 'JMAddressMapScreen',
//           actionType: 'T001',
//           actionUrl: '',
//           bundle: '',
//         },
//       },
//       {
//         isVisible: true,
//         type: 'DELETE',
//         icon: {
//           ic: 'IcTrash',
//           color: 'primary_grey_80',
//         },
//         label: {
//           text: 'Delete',
//         },
//       },
//       {
//         isVisible: true,
//         type: 'MARK_AS_DEFAULT',
//         icon: {
//           ic: 'IcMessageSend',
//           color: 'primary_grey_80',
//         },
//         label: {
//           text: 'Mark as Default',
//         },
//       },
//     ],
//   },
//   alert: {
//     addressBook: {
//       leftIcon: {
//         ic: 'IcWarningColored',
//         size: 'medium',
//         color: '',
//       },
//       title: {
//         text: 'Your address book is full',
//         appearance: 'body_xxs_bold',
//         color: 'black',
//       },
//       subTitle: {
//         text: 'To add a new address, please delete one of your saved address.',
//         appearance: 'body_xxs',
//         color: 'black',
//       },
//       backgroundColor: 'feedback_warning_20',
//     },
//   },
//   cards: {
//     addressListCard: {
//       name: {
//         color: 'primary_grey_100',
//         appearance: 'body_xs_bold',
//         maxLines: 2,
//       },
//       type: {},
//       icon: {
//         ic: 'IcMoreVertical',
//         color: 'primary_60',
//         size: 'medium',
//       },
//       address: {},
//       phone: {
//         text: 'Phone: [TEXT]',
//       },
//       subText: {
//         text: 'Default Address',
//       },
//     },
//     orderReviewListCard: {
//       name: {
//         color: 'primary_grey_100',
//         appearance: 'body_xs_bold',
//         maxLines: 2,
//       },
//       type: {},
//       phone: {
//         text: 'Phone: [TEXT]',
//       },
//       icon: {
//         ic: 'IcEditPen',
//       },
//       subText: {
//         text: 'Default Address',
//       },
//     },
//   },
//   negativeCases: {
//     emptyAddress: {
//       image: {
//         uri: 'https://myjiostatic.cdn.jio.com/JioMart/Common/qna.jpg',
//       },
//       title: {
//         text: "You don't have any address saved!",
//       },
//       subTitle: {
//         text: 'Add a new address to find the best products and offers in your area.',
//       },
//       button: {
//         title: 'Add New Address',
//         iconLeft: 'IcAdd',
//       },
//       isButtonVisible: true,
//       shouldShowContentInCenter: false,
//       cta: {
//         navTitle: 'Add Address',
//         HeaderType: 1,
//         source: '',
//         destination: 'JMAddressSearchScreen',
//         actionType: 'T001',
//         actionUrl: '',
//         bundle: '',
//       },
//     },
//   },
//   addAddressButton: {
//     title: 'Add New Address',
//     iconLeft: 'IcAdd',
//     cta: {
//       navTitle: 'Add Address',
//       HeaderType: 1,
//       source: '',
//       destination: 'JMAddressSearchScreen',
//       actionType: 'T001',
//       actionUrl: '',
//       bundle: '',
//     },
//   },
//   bottomSheet: {
//     delete: {
//       headerTitle: 'Delete Address',
//       name: {},
//       address: {},
//       description: {},
//       button: {
//         title: 'Delete',
//       },
//     },
//   },
// };
const enum DropDownType {
  EDIT = 'EDIT',
  DELETE = 'DELETE',
  MARK_AS_DEFAULT = 'MARK_AS_DEFAULT',
}

const useJMAddressListScreenController = (
  props: UseJMAddressListScreenProps,
) => {
  const {navigation, route} = props;
  const config = useConfigFileFromCache(
    JMConfigFileName.JMAddressConfigurationFileNAme,
  )?.data?.screen?.addressList;
  const addressData = useAddressList();
  const insets = useSafeAreaInsets();
  const {
    setDefaultAddress,
    removeAddress,
    generateRemoveAddressRequestBody,
    generateDefaultAddressRequestBody,
  } = useAddressOperation();

  const address =
    addressData.data?.slice(0, config?.shouldShowNoOfAddress) ?? [];
  const showAlertAddressBook = address?.length >= config?.shouldShowNoOfAddress;
  const card = true
    ? config?.cards?.addressListCard
    : config?.cards?.orderReviewListCard;

  const [menuInfo, setMenuInfo] = useState<{
    y: number;
    index: number;
    default: boolean;
  } | null>(null);
  const [deleteBtmSheet, setDeleteBtmSheet] = useState<number | null>(null);
  const iconRefs = useRef<{[key: string]: any}>({});
  const flatListRef = useRef<FlatList<any>>(null);

  const onIconPress = (
    id: string,
    index: number,
    is_default_address: boolean = false,
  ) => {
    const ref = iconRefs.current[`${id}_${index}`];
    if (ref?.measureInWindow) {
      ref.measureInWindow((x: any, y: any) => {
        const updateY = Platform.OS === 'android' ? y - 20 : y - 90;
        setMenuInfo({y: updateY, index, default: is_default_address});
      });
    }
  };

  const handleEmptyAddress = () => {
    navigateTo(
      navBeanObj({
        ...config?.negativeCases?.emptyAddress?.cta,
      }),
      navigation,
    );
  };
  const handleAddAddres = () => {
    navigateTo(
      navBeanObj({
        ...config?.addAddressButton?.cta,
      }),
      navigation,
    );
  };
  const handleEdit = (cta: NavigationBean) => {
    return () => {
      if (menuInfo) {
        navigateTo(
          navBeanObj({
            ...cta,
            params: {
              address: address[menuInfo?.index],
              coords: {
                latitude: address[menuInfo?.index]?.lat,
                longitude: address[menuInfo?.index]?.lon,
              },
            },
          }),
          navigation,
        );
        setMenuInfo(null);
      }
    };
  };
  const handleDelete = () => {
    if (menuInfo) {
      setDeleteBtmSheet(menuInfo?.index + 1);
      setMenuInfo(null);
    }
  };
  const handleMarkAsDefault = () => {
    setMenuInfo(null);
    flatListRef.current?.scrollToOffset({offset: 0, animated: true});
    if (menuInfo) {
      const request = generateDefaultAddressRequestBody(
        address[menuInfo?.index],
      );
      setDefaultAddress.mutate(request);
    }
  };
  const handleDeleteAddress = () => {
    flatListRef.current?.scrollToOffset({offset: 0, animated: true});
    if (deleteBtmSheet) {
      const request = generateRemoveAddressRequestBody(
        address[deleteBtmSheet - 1],
      );
      removeAddress.mutate(request);
    }
  };

  const generateMenuList = (config: any) => {
    const sw = menuInfo?.default ? 'default' : 'normal';
    return config?.[sw]
      ?.map((item: any) => {
        if (!item?.isVisible) {
          return;
        }
        switch (item?.type) {
          case DropDownType.EDIT:
            return {
              ...item,
              onPress: handleEdit(item?.cta),
            };
          case DropDownType.DELETE:
            return {
              ...item,
              onPress: handleDelete,
            };
          case DropDownType.MARK_AS_DEFAULT:
            return {
              ...item,
              onPress: handleMarkAsDefault,
            };
          default:
            return;
        }
      })
      ?.filter((item: any) => item != null);
  };

  return {
    config,
    showAlertAddressBook,
    card,
    address,
    handleEmptyAddress,
    handleAddAddres,
    menuInfo,
    setMenuInfo,
    generateMenuList,
    onIconPress,
    iconRefs,
    insets,
    flatListRef,
    deleteBtmSheet,
    setDeleteBtmSheet,
    handleDeleteAddress,
    ...props,
    navigationBean: route.params,
  };
};

export default useJMAddressListScreenController;
