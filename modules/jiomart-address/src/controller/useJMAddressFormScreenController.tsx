import type {UseJMAddressFormScreenProps} from '../types/JMAddressFormScreenType';
import useFormHandler from '@jm/jiomart-general/src/hooks/useFormHandler';
import useAddressOperation from '../hooks/useAddressOperation';
import type {JMAddressModel} from '@jm/jiomart-networkmanager/src/models/Address/JMAddressModel';
import useUserProfile from '@jm/jiomart-general/src/hooks/useUserProfile';
import {navigateTo} from '@jm/jiomart-general/src/navigation/JMNavGraph';
import {navBeanObj} from '@jm/jiomart-common/src/JMNavGraphUtil';
import {useConfigFileFromCache} from '@jm/jiomart-general/src/hooks/useJMConfig';
import {JMConfigFileName} from '@jm/jiomart-networkmanager/src/JMConfigFileManager/JMFileName';
import {useEffect} from 'react';

// const config: any = {
//   map: {
//     scrollEnabled: false,
//     zoomEnabled: false,
//     pitchEnabled: false,
//     rotateEnabled: false,
//     showsCompass: false,
//     showsBuildings: true,
//     showsTraffic: false,
//     showsIndoors: false,
//     provider: 'google',
//     initialCamera: {
//       pitch: 45,
//       heading: 90,
//       zoom: 18,
//     },
//   },
//   headerTitle: {
//     color: 'primary_grey_80',
//     appearance: 'body_xxs',
//   },
//   link: {
//     text: 'Edit Location on Map',
//     color: 'primary_60',
//     appearance: 'body_xxs_link',
//   },
//   formHeaderTitle: {
//     text: 'Address Details',
//     color: 'black',
//     appearance: 'heading_xxs',
//   },
//   deliveryContactDetail: {
//     headerTitle: {
//       text: 'Delivery Contact Details',
//       appearance: 'heading_xxs',
//       color: 'primary_grey_100',
//     },
//     subTitle: {
//       text: 'This mobile number will receive an OTP required for collecting the order.',
//       appearance: 'body_xxs',
//       color: 'primary_grey_100',
//     },
//   },
//   submitButton: {
//     title: 'Save & Proceed',
//     cta: {
//       navTitle: 'Add Address',
//       HeaderType: 1,
//       source: '',
//       destination: 'JMAddressListScreen',
//       actionType: 'T001',
//       actionUrl: '',
//       bundle: '',
//     },
//   },
//   saveAs: [
//     {
//       title: 'Home',
//       value: 'Home',
//     },
//     {
//       title: 'Work',
//       value: 'Work',
//     },
//     {
//       title: 'Other',
//       value: '',
//     },
//   ],
//   saveAsHeaderTitle: {
//     text: 'Save As',
//     appearance: 'heading_xxs',
//     color: 'primary_grey_100',
//   },
//   form: {
//     flat_or_house_no: {
//       min: 0,
//       max: 8,
//       isReadOnly: false,
//       isRegexClean: false,
//       required: false,
//       regex: '',
//       error: {
//         min: 'Please enter House No. (min 0 characters)',
//         max: 'Please enter House No. (max 8 characters)',
//         required: 'Please enter your House No.',
//         default: 'Please enter a valid House No.',
//       },
//       textInput: {
//         autoCorrect: false,
//         keyboardType: 'default',
//       },
//       label: {
//         text: 'House No.',
//       },
//       feedback: {
//         state: 'error',
//       },
//     },
//     floor_no: {
//       min: 0,
//       max: 8,
//       isReadOnly: false,
//       isRegexClean: false,
//       required: false,
//       regex: '',
//       error: {
//         min: 'Please enter Floor No. (min 0 characters)',
//         max: 'Please enter Floor No. (max 8 characters)',
//         required: 'Please enter your Floor No.',
//         default: 'Please enter a valid Floor No.',
//       },
//       textInput: {
//         autoCorrect: false,
//         keyboardType: 'default',
//       },
//       label: {
//         text: 'Floor No.',
//       },
//       feedback: {
//         state: 'error',
//       },
//     },
//     tower_no: {
//       min: 0,
//       max: 8,
//       isReadOnly: false,
//       isRegexClean: false,
//       required: false,
//       regex: '',
//       error: {
//         min: 'Please enter Tower No. (min 0 characters)',
//         max: 'Please enter Tower No. (max 8 characters)',
//         required: 'Please enter your Tower No.',
//         default: 'Please enter a valid Tower No.',
//       },
//       textInput: {
//         autoCorrect: false,
//         keyboardType: 'default',
//       },
//       label: {
//         text: 'Tower No.',
//       },
//       feedback: {
//         state: 'error',
//       },
//     },
//     area: {
//       min: 3,
//       max: 40,
//       isReadOnly: false,
//       isRegexClean: false,
//       required: false,
//       regex: '',
//       error: {
//         min: 'Please enter Building / Apartment Name (min 3 characters)',
//         max: 'Please enter Building / Apartment Name (max 40 characters)',
//         required:
//           'Please enter Building / Apartment Name (min 3 - max 40 characters)',
//         default: 'Please enter valid Building / Apartment Name',
//       },
//       textInput: {
//         autoCorrect: false,
//         keyboardType: 'default',
//       },
//       label: {
//         text: 'Building / Apartment Name',
//       },
//       feedback: {
//         state: 'error',
//       },
//     },
//     address: {
//       min: 3,
//       max: 512,
//       isReadOnly: false,
//       isRegexClean: false,
//       required: true,
//       regex: '^[A-Za-z0-9,./ -]*$',
//       error: {
//         min: 'Please enter address (min 3 characters)',
//         max: 'Please enter address (max 512 characters)',
//         required: 'Please enter valid address (min 3 - max 512 characters)',
//         default: 'Please enter a valid address',
//       },
//       textInput: {
//         autoCorrect: false,
//         keyboardType: 'default',
//       },
//       label: {
//         text: 'Address*',
//       },
//       feedback: {
//         state: 'error',
//       },
//     },
//     landmark: {
//       min: 3,
//       max: 80,
//       isReadOnly: false,
//       isRegexClean: false,
//       required: true,
//       regex: '[A-Za-z0-9,./ -]*$',
//       error: {
//         min: 'Please enter a valid Landmark / Area  (min 3 characters)',
//         max: 'Please enter a valid Landmark / Area (max 80 characters)',
//         required: 'Please enter your Landmark / Area',
//         default: 'Please enter a valid Landmark / Area',
//       },
//       textInput: {
//         autoCorrect: false,
//         keyboardType: 'default',
//       },
//       label: {
//         text: 'Landmark / Area*',
//       },
//       feedback: {
//         state: 'error',
//       },
//     },
//     pincode: {
//       min: 6,
//       max: 6,
//       isReadOnly: false,
//       isRegexClean: false,
//       required: true,
//       regex: '^[0-9]{6}$',
//       error: {
//         min: 'Please enter a ZIP or postal code',
//         max: 'Please enter a ZIP or postal code',
//         required: 'Please enter your ZIP or postal code',
//         default: 'Please enter a valid ZIP or postal code',
//       },
//       textInput: {
//         autoCorrect: false,
//         keyboardType: 'default',
//       },
//       label: {
//         text: 'Pincode*',
//       },
//       feedback: {
//         state: 'error',
//       },
//     },
//     city: {
//       min: 3,
//       max: 200,
//       isReadOnly: false,
//       isRegexClean: false,
//       required: false,
//       regex: '^[A-Za-z0-9. ]*$',
//       error: {
//         min: 'Please enter a valid City (min 3 characters)',
//         max: 'Please enter a valid City (max 200 characters)',
//         required: 'Please enter your City',
//         default: 'Please enter a valid City',
//       },
//       textInput: {
//         autoCorrect: false,
//         keyboardType: 'default',
//       },
//       label: {
//         text: 'City*',
//       },
//       feedback: {
//         state: 'error',
//       },
//     },
//     state: {
//       min: 3,
//       max: 200,
//       isReadOnly: false,
//       isRegexClean: false,
//       required: true,
//       regex: '^[A-Za-z0-9. ]*$',
//       error: {
//         min: 'Please enter a valid State (min 3 characters)',
//         max: 'Please enter a valid State (max 200 characters)',
//         required: 'Please enter your State',
//         default: 'Please enter a valid State',
//       },
//       textInput: {
//         autoCorrect: false,
//         keyboardType: 'default',
//       },
//       label: {
//         text: 'State*',
//       },
//       feedback: {
//         state: 'error',
//       },
//     },
//     name: {
//       min: 3,
//       max: 80,
//       isReadOnly: false,
//       isRegexClean: false,
//       required: true,
//       regex: '^[A-Za-z0-9. ]*$',
//       error: {
//         min: 'Please enter a valid Receiver Name (min 3 characters)',
//         max: 'Please enter a valid Receiver Name (max 80 characters)',
//         required: 'Please enter your Receiver Name',
//         default: 'Please enter a valid Receiver Name',
//       },
//       textInput: {
//         autoCorrect: false,
//         keyboardType: 'default',
//       },
//       label: {
//         text: 'Receiver Name*',
//       },
//       feedback: {
//         state: 'error',
//       },
//     },
//     phone: {
//       min: 10,
//       max: 10,
//       isReadOnly: false,
//       isRegexClean: false,
//       required: true,
//       regex: '^[0-9]{10}$',
//       error: {
//         min: 'Please enter a valid Receiver Number (min 10 digits)',
//         max: 'Please enter a valid Receiver Number (max 10 digits)',
//         required: 'Please enter your Receiver Number',
//         default: 'Please enter a valid Receiver Number',
//       },
//       textInput: {
//         autoCorrect: false,
//         keyboardType: 'default',
//       },
//       label: {
//         text: 'Receiver Number*',
//       },
//       prefix: {
//         title: {
//           text: '+91',
//         },
//         icon: {
//           ic: 'IcChevronDown',
//         },
//       },
//       feedback: {
//         state: 'error',
//       },
//     },
//     address_type: {
//       min: 3,
//       max: 30,
//       isReadOnly: false,
//       isRegexClean: false,
//       required: true,
//       regex: '^[A-Za-z0-9. ]*$',
//       error: {
//         min: 'Please enter a valid Address Type (min 3 digits)',
//         max: 'Please enter a valid Address Type (max 30 digits)',
//         required: 'Please enter your Address Type',
//         default: 'Please enter a valid Address Type',
//       },
//       textInput: {
//         autoCorrect: false,
//         keyboardType: 'default',
//       },
//       label: {
//         text: "Eg. Club House, Kumar's Home*",
//       },
//       feedback: {
//         state: 'error',
//       },
//     },
//   },
// };

export const enum AddressFormField {
  ID = 'id',
  HOUSE_NO = 'flat_or_house_no',
  FLOOR_NO = 'floor_no',
  TOWER_NO = 'tower_no',
  BUILDING_APARTMENT_NAME = 'area',
  ADDRESS = 'address',
  LANDMARK_AREA = 'landmark',
  PINCODE = 'pincode',
  CITY = 'city',
  STATE = 'state',
  RECEIVER_NAME = 'name',
  RECEIVER_NUMBER = 'phone',
  ADDRESS_TYPE = 'address_type',
}

export const enum AddressType {
  HOME = 'Home',
  WORK = 'Work',
  OTHER = 'Other',
}

const useJMAddressFormScreenController = (
  props: UseJMAddressFormScreenProps,
) => {
  const {route, navigation} = props;
  const config = useConfigFileFromCache(
    JMConfigFileName.JMAddressConfigurationFileNAme,
  )?.data?.screen?.addressForm;
  const address = route.params?.params?.address;
  const regionCamera = {
    ...config?.map?.initialCamera,
    center: {
      latitude: address?.lat,
      longitude: address?.lon,
    },
  };

  const {userData} = useUserProfile();

  const receiverName = address?.id
    ? address?.name
    : userData?.first_name + ' ' + userData?.last_name;
  const receiverNumber = address?.id
    ? address?.phone
    : userData?.phone_numbers?.find(num => num.primary && num.verified)?.phone;

  const {
    saveAddress,
    updateAddress,
    generateSaveAddressRequestBody,
    generateUpdateAddressRequestBody,
  } = useAddressOperation();

  const {
    formData,
    formError,
    handleInputRef,
    handleFormError,
    handleFormData,
    onChangeText,
    validateValue,
    validateForm,
  } = useFormHandler(
    {
      [AddressFormField.ID]: address?.id,
      [AddressFormField.RECEIVER_NAME]: receiverName,
      [AddressFormField.RECEIVER_NUMBER]: receiverNumber ?? '',
      [AddressFormField.ADDRESS_TYPE]: address?.address_type,
      [AddressFormField.ADDRESS]: address?.address,
      [AddressFormField.HOUSE_NO]: address?.flat_or_house_no,
      [AddressFormField.FLOOR_NO]: address?.floor_no,
      [AddressFormField.TOWER_NO]: address?.tower_no,
      [AddressFormField.BUILDING_APARTMENT_NAME]: address?.area,
      [AddressFormField.LANDMARK_AREA]: address?.landmark,
      [AddressFormField.CITY]: address?.city,
      [AddressFormField.STATE]: address?.state,
      [AddressFormField.PINCODE]: address?.pin,
    },
    config?.form,
  );

  const handleSaveAs = (val: string) => {
    return () => {
      handleFormData(AddressFormField.ADDRESS_TYPE)(val);
    };
  };

  const handleRedirection = () => {
    navigateTo(
      navBeanObj({
        ...config?.submitButton?.cta,
      }),
      navigation,
    );
  };

  const handleAddressSubmit = () => {
    try {
      if (!validateForm()) {
        return;
      }

      const payload: JMAddressModel = {
        id: formData[AddressFormField.ID],
        city: formData[AddressFormField.CITY],
        state: formData[AddressFormField.STATE],
        pin: formData[AddressFormField.PINCODE],
        name: formData[AddressFormField.RECEIVER_NAME],
        phone: formData[AddressFormField.RECEIVER_NUMBER],
        address_type: formData[AddressFormField.ADDRESS_TYPE],
        address: formData[AddressFormField.ADDRESS],
        flat_or_house_no: formData[AddressFormField.HOUSE_NO],
        floor_no: formData[AddressFormField.FLOOR_NO],
        tower_no: formData[AddressFormField.TOWER_NO],
        area: formData[AddressFormField.BUILDING_APARTMENT_NAME],
        landmark: formData[AddressFormField.LANDMARK_AREA],
        lat: regionCamera?.center?.latitude,
        lon: regionCamera?.center?.longitude,
        input_mode: 'map',
        is_default_address: true,
      };

      if (formData[AddressFormField.ID]) {
        const request = generateUpdateAddressRequestBody(payload);
        updateAddress.mutate(request, {
          onSuccess: res => {
            if (res?.success) {
              handleRedirection();
            }
          },
        });
      } else {
        const request = generateSaveAddressRequestBody(payload);
        saveAddress.mutate(request, {
          onSuccess: res => {
            if (res?.success) {
              handleRedirection();
            }
          },
        });
      }
    } catch (error) {}
  };

  useEffect(() => {
    navigation.setParams({
      ...route.params,
      navTitle: formData[AddressFormField.ID]
        ? config?.header?.editNavTitle
        : route.params.navTitle,
    });
  }, []);

  return {
    ...props,
    address,
    regionCamera,
    config,
    formData,
    formError,
    handleInputRef,
    handleFormError,
    onChangeText,
    validateValue,
    handleAddressSubmit,
    handleSaveAs,
    navigationBean: route.params,
  };
};

export default useJMAddressFormScreenController;
