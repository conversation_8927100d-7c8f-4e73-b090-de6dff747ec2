import type {UseJMAddressFormV1ScreenProps} from '../types/JMAddressFormV1ScreenType';
import useFormHandler from '@jm/jiomart-general/src/hooks/useFormHandler';
import useCurrentLocation from '../hooks/useCurrentLocation';
import {navigateTo} from '@jm/jiomart-general/src/navigation/JMNavGraph';
import {navBeanObj} from '@jm/jiomart-common/src/JMNavGraphUtil';
import type {GeoCoordinates} from 'react-native-geolocation-service';
import type {JMAddressModel} from '@jm/jiomart-networkmanager/src/models/Address/JMAddressModel';
import useUserProfile from '@jm/jiomart-general/src/hooks/useUserProfile';
import {useConfigFileFromCache} from '@jm/jiomart-general/src/hooks/useJMConfig';
import {JMConfigFileName} from '@jm/jiomart-networkmanager/src/JMConfigFileManager/JMFileName';
import useAddressOperation from '../hooks/useAddressOperation';
import {capitalizeFirstLetter} from '@jm/jiomart-common/src/utils/JMStringUtility';
import {useEffect} from 'react';

// const config: any = {
//   map: {
//     scrollEnabled: false,
//     zoomEnabled: false,
//     pitchEnabled: false,
//     rotateEnabled: false,
//     showsCompass: false,
//     showsBuildings: true,
//     showsTraffic: false,
//     showsIndoors: false,
//     provider: 'google',
//     initialCamera: {
//       pitch: 45,
//       heading: 90,
//       zoom: 18,
//     },
//   },
//   locationBlock: {
//     subTitle: {
//       text: 'Additionally, for more accurate delivery to this address use your current location, or search for your building or area.',
//       color: 'primary_grey_100',
//       appearance: 'body_xxs',
//     },
//     shouldShowFieldValue: ['pincode', 'city', 'state', 'address', 'landmark'],
//     currentLocation: {
//       icon: {
//         ic: 'IcCameraFocus',
//         color: 'primary_60',
//       },
//       title: {
//         text: 'Use Current Location',
//         color: 'primary_60',
//         appearance: 'button',
//       },
//       cta: {
//         navTitle: 'Add Address',
//         HeaderType: 1,
//         source: '',
//         destination: 'JMAddressMapScreen',
//         actionType: 'T001',
//         actionUrl: '',
//         bundle: '',
//       },
//     },
//     search: {
//       icon: {
//         ic: 'IcSearch',
//         color: 'primary_grey_80',
//       },
//       cta: {
//         navTitle: 'Add Address',
//         HeaderType: 1,
//         source: '',
//         destination: 'JMAddressSearchScreen',
//         actionType: 'T001',
//         actionUrl: '',
//         bundle: '',
//       },
//     },
//   },
//   alert: {
//     blockedLocation: {
//       title: '',
//       message:
//         'Please provide your location or search using your closest landmark/apartment name',
//       button: {
//         text: 'Enable Now',
//       },
//     },
//   },
//   editDeliveryLocation: {
//     title: 'Edit Delivery Location',
//     iconLeft: 'IcEditPen',
//   },
//   headerTitle: {
//     color: 'primary_grey_80',
//     appearance: 'body_xxs',
//   },
//   link: {
//     text: 'Edit Location on Map',
//     color: 'primary_60',
//     appearance: 'body_xxs_link',
//   },
//   formHeaderTitle: {
//     text: 'Address Details',
//     color: 'black',
//     appearance: 'heading_xxs',
//   },
//   deliveryContactDetail: {
//     headerTitle: {
//       text: 'Delivery Contact Details',
//       appearance: 'heading_xxs',
//       color: 'primary_grey_100',
//     },
//     subTitle: {
//       text: 'This mobile number will receive an OTP required for collecting the order.',
//       appearance: 'body_xxs',
//       color: 'primary_grey_100',
//     },
//   },
//   submitButton: {
//     title: 'Save & Proceed',
//   },
//   saveAs: [
//     {
//       title: 'Home',
//       value: 'Home',
//     },
//     {
//       title: 'Work',
//       value: 'Work',
//     },
//     {
//       title: 'Other',
//       value: '',
//     },
//   ],
//   saveAsHeaderTitle: {
//     text: 'Save As',
//     appearance: 'heading_xxs',
//     color: 'primary_grey_100',
//   },
//   form: {
//     pincode: {
//       min: 6,
//       max: 6,
//       isReadOnly: false,
//       isRegexClean: false,
//       required: true,
//       regex: '^[0-9]{6}$',
//       error: {
//         min: 'Please enter a ZIP or postal code',
//         max: 'Please enter a ZIP or postal code',
//         required: 'Please enter your ZIP or postal code',
//         default: 'Please enter a valid ZIP or postal code',
//       },
//       textInput: {
//         autoCorrect: false,
//         keyboardType: 'number-pad',
//         maxLength: 6,
//       },
//       label: {
//         text: 'Pincode*',
//       },
//       feedback: {
//         state: 'error',
//       },
//     },
//     city: {
//       min: 3,
//       max: 200,
//       isReadOnly: true,
//       isRegexClean: false,
//       required: true,
//       regex: '^[A-Za-z0-9. ]*$',
//       error: {
//         min: 'Please enter a valid City (min 3 characters)',
//         max: 'Please enter a valid City (max 200 characters)',
//         required: 'Please enter your City',
//         default: 'Please enter a valid City',
//       },
//       textInput: {
//         autoCorrect: false,
//         keyboardType: 'default',
//       },
//       label: {
//         text: 'City*',
//       },
//       feedback: {
//         state: 'error',
//       },
//     },
//     state: {
//       min: 3,
//       max: 200,
//       isReadOnly: true,
//       isRegexClean: false,
//       required: true,
//       regex: '^[A-Za-z0-9. ]*$',
//       error: {
//         min: 'Please enter a valid State (min 3 characters)',
//         max: 'Please enter a valid State (max 200 characters)',
//         required: 'Please enter your State',
//         default: 'Please enter a valid State',
//       },
//       textInput: {
//         autoCorrect: false,
//         keyboardType: 'default',
//       },
//       label: {
//         text: 'State*',
//       },
//       feedback: {
//         state: 'error',
//       },
//     },
//     flat_or_house_no: {
//       min: 0,
//       max: 8,
//       isReadOnly: false,
//       isRegexClean: false,
//       required: false,
//       regex: '',
//       error: {
//         min: 'Please enter House No. (min 0 characters)',
//         max: 'Please enter House No. (max 8 characters)',
//         required: 'Please enter your House No.',
//         default: 'Please enter a valid House No.',
//       },
//       textInput: {
//         autoCorrect: false,
//         keyboardType: 'default',
//       },
//       label: {
//         text: 'House No.',
//       },
//       feedback: {
//         state: 'error',
//       },
//     },
//     floor_no: {
//       min: 0,
//       max: 8,
//       isReadOnly: false,
//       isRegexClean: false,
//       required: false,
//       regex: '',
//       error: {
//         min: 'Please enter Floor No. (min 0 characters)',
//         max: 'Please enter Floor No. (max 8 characters)',
//         required: 'Please enter your Floor No.',
//         default: 'Please enter a valid Floor No.',
//       },
//       textInput: {
//         autoCorrect: false,
//         keyboardType: 'default',
//       },
//       label: {
//         text: 'Floor No.',
//       },
//       feedback: {
//         state: 'error',
//       },
//     },
//     tower_no: {
//       min: 0,
//       max: 8,
//       isReadOnly: false,
//       isRegexClean: false,
//       required: false,
//       regex: '',
//       error: {
//         min: 'Please enter Tower No. (min 0 characters)',
//         max: 'Please enter Tower No. (max 8 characters)',
//         required: 'Please enter your Tower No.',
//         default: 'Please enter a valid Tower No.',
//       },
//       textInput: {
//         autoCorrect: false,
//         keyboardType: 'default',
//       },
//       label: {
//         text: 'Tower No.',
//       },
//       feedback: {
//         state: 'error',
//       },
//     },
//     area: {
//       min: 3,
//       max: 40,
//       isReadOnly: false,
//       isRegexClean: false,
//       required: false,
//       regex: '',
//       error: {
//         min: 'Please enter Building / Apartment Name (min 3 characters)',
//         max: 'Please enter Building / Apartment Name (max 40 characters)',
//         required:
//           'Please enter Building / Apartment Name (min 3 - max 40 characters)',
//         default: 'Please enter valid Building / Apartment Name',
//       },
//       textInput: {
//         autoCorrect: false,
//         keyboardType: 'default',
//       },
//       label: {
//         text: 'Building / Apartment Name',
//       },
//       feedback: {
//         state: 'error',
//       },
//     },
//     address: {
//       min: 3,
//       max: 512,
//       isReadOnly: false,
//       isRegexClean: false,
//       required: true,
//       regex: '^[A-Za-z0-9,./ -]*$',
//       error: {
//         min: 'Please enter address (min 3 characters)',
//         max: 'Please enter address (max 512 characters)',
//         required: 'Please enter valid address (min 3 - max 512 characters)',
//         default: 'Please enter a valid address',
//       },
//       textInput: {
//         autoCorrect: false,
//         keyboardType: 'default',
//       },
//       label: {
//         text: 'Address*',
//       },
//       feedback: {
//         state: 'error',
//       },
//     },
//     landmark: {
//       min: 3,
//       max: 80,
//       isReadOnly: false,
//       isRegexClean: false,
//       required: true,
//       regex: '[A-Za-z0-9,./ -]*$',
//       error: {
//         min: 'Please enter a valid Landmark / Area  (min 3 characters)',
//         max: 'Please enter a valid Landmark / Area (max 80 characters)',
//         required: 'Please enter your Landmark / Area',
//         default: 'Please enter a valid Landmark / Area',
//       },
//       textInput: {
//         autoCorrect: false,
//         keyboardType: 'default',
//       },
//       label: {
//         text: 'Landmark / Area*',
//       },
//       feedback: {
//         state: 'error',
//       },
//     },
//     name: {
//       min: 3,
//       max: 80,
//       isReadOnly: false,
//       isRegexClean: false,
//       required: true,
//       regex: '^[A-Za-z0-9. ]*$',
//       error: {
//         min: 'Please enter a valid Receiver Name (min 3 characters)',
//         max: 'Please enter a valid Receiver Name (max 80 characters)',
//         required: 'Please enter your Receiver Name',
//         default: 'Please enter a valid Receiver Name',
//       },
//       textInput: {
//         autoCorrect: false,
//         keyboardType: 'default',
//       },
//       label: {
//         text: 'Receiver Name*',
//       },
//       feedback: {
//         state: 'error',
//       },
//     },
//     phone: {
//       min: 10,
//       max: 10,
//       isReadOnly: false,
//       isRegexClean: false,
//       required: true,
//       regex: '^[0-9]{10}$',
//       error: {
//         min: 'Please enter a valid Receiver Number (min 10 digits)',
//         max: 'Please enter a valid Receiver Number (max 10 digits)',
//         required: 'Please enter your Receiver Number',
//         default: 'Please enter a valid Receiver Number',
//       },
//       textInput: {
//         autoCorrect: false,
//         keyboardType: 'number-pad',
//         maxLength: 10,
//       },
//       label: {
//         text: 'Receiver Number*',
//       },
//       prefix: {
//         title: {
//           text: '+91',
//         },
//         icon: {
//           ic: 'IcChevronDown',
//         },
//       },
//       feedback: {
//         state: 'error',
//       },
//     },
//     address_type: {
//       min: 3,
//       max: 30,
//       isReadOnly: false,
//       isRegexClean: false,
//       required: true,
//       regex: '^[A-Za-z0-9. ]*$',
//       error: {
//         min: 'Please enter a valid Address Type (min 3 digits)',
//         max: 'Please enter a valid Address Type (max 30 digits)',
//         required: 'Please enter your Address Type',
//         default: 'Please enter a valid Address Type',
//       },
//       textInput: {
//         autoCorrect: false,
//         keyboardType: 'default',
//       },
//       label: {
//         text: "Eg. Club House, Kumar's Home*",
//       },
//       feedback: {
//         state: 'error',
//       },
//     },
//   },
// };

export const enum AddressFormField {
  ID = 'id',
  HOUSE_NO = 'flat_or_house_no',
  FLOOR_NO = 'floor_no',
  TOWER_NO = 'tower_no',
  BUILDING_APARTMENT_NAME = 'area',
  ADDRESS = 'address',
  LANDMARK_AREA = 'landmark',
  PINCODE = 'pincode',
  CITY = 'city',
  STATE = 'state',
  RECEIVER_NAME = 'name',
  RECEIVER_NUMBER = 'phone',
  ADDRESS_TYPE = 'address_type',
}

export const enum AddressType {
  HOME = 'Home',
  WORK = 'Work',
  OTHER = 'Other',
}

const useJMAddressFormV1ScreenController = (
  props: UseJMAddressFormV1ScreenProps,
) => {
  const {route, navigation} = props;
  const config = useConfigFileFromCache(
    JMConfigFileName.JMAddressConfigurationFileNAme,
  )?.data?.screen?.addressFormV1;
  const address = route.params?.params?.address;
  const latitude = address?.lat;
  const longitude = address?.lon;
  const geoCordinate: GeoCoordinates = {
    latitude,
    longitude,
  };
  const regionCamera = {
    ...config?.map?.initialCamera,
    center: geoCordinate,
  };

  const {userData} = useUserProfile();
  const {
    saveAddress,
    updateAddress,
    getPincodeStatus,
    generateSaveAddressRequestBody,
    generateUpdateAddressRequestBody,
  } = useAddressOperation();

  const receiverName = address?.id
    ? address?.name
    : userData?.first_name + ' ' + userData?.last_name;
  const receiverNumber = address?.id
    ? address?.phone
    : userData?.phone_numbers?.find(num => num.primary && num.verified)?.phone;

  const {fetchLocationFromReverseGeoCodeFromLatLong} = useCurrentLocation({
    alertBlocked: config?.alert?.blockedLocation,
  });

  const {
    formData,
    formError,
    handleInputRef,
    handleFormError,
    handleFormData,
    onChangeText,
    validateValue,
    validateForm,
  } = useFormHandler(
    {
      [AddressFormField.ID]: address?.id,
      [AddressFormField.RECEIVER_NAME]: receiverName,
      [AddressFormField.RECEIVER_NUMBER]: receiverNumber,
      [AddressFormField.ADDRESS_TYPE]: address?.address_type,
      [AddressFormField.ADDRESS]: address?.address,
      [AddressFormField.HOUSE_NO]: address?.flat_or_house_no,
      [AddressFormField.FLOOR_NO]: address?.floor_no,
      [AddressFormField.TOWER_NO]: address?.tower_no,
      [AddressFormField.BUILDING_APARTMENT_NAME]: address?.area,
      [AddressFormField.LANDMARK_AREA]: address?.landmark,
      [AddressFormField.CITY]: address?.city,
      [AddressFormField.STATE]: address?.state,
      [AddressFormField.PINCODE]: address?.pin,
    },
    config?.form,
  );

  const resetCoords = () => {
    const updateAddress = {...address};
    delete updateAddress?.lat;
    delete updateAddress?.lon;
    navigation.setParams({params: {address: updateAddress}});
  };

  const handlePincodeChange = (field: any) => {
    return (value: string) => {
      resetCoords();
      let text = value;
      const fieldConfig = config?.[field];
      if (fieldConfig?.isRegexClean && fieldConfig?.regex) {
        const regex = new RegExp(fieldConfig?.regex, 'g');
        text = text?.replace?.(regex, '') ?? '';
      }
      handleFormData(field)(text);
      if (text?.length === 6) {
        getPincodeStatus.mutate(text, {
          onSuccess: response => {
            if (response?.success) {
              const city = capitalizeFirstLetter(
                response?.data?.[0]?.parents
                  ?.find((it: any) => it?.sub_type === 'city')
                  ?.name?.replace(/_/g, ' '),
              );
              const state = capitalizeFirstLetter(
                response?.data?.[0]?.parents
                  ?.find((it: any) => it?.sub_type === 'state')
                  ?.name?.replace(/_/g, ' '),
              );
              handleFormData(AddressFormField.CITY)(city);
              handleFormData(AddressFormField.STATE)(state);
            } else {
              handleFormError(
                AddressFormField.PINCODE,
                config?.message?.invalidPincode,
              )();
            }
          },
          onError: () => {
            handleFormError(
              AddressFormField.PINCODE,
              config?.message?.pincodeChangeFailed,
            )();
          },
        });
      } else {
        handleFormData(AddressFormField.CITY)('');
        handleFormData(AddressFormField.STATE)('');
      }
    };
  };

  const shouldShowLocationBlock = (val: string[]) => {
    let isValid = true;
    for (let i = 0; i < val?.length; i++) {
      if (!formData[val[i]]) {
        isValid = false;
        break;
      }
    }
    return isValid;
  };

  const shouldShowMapBlock = (val: string[]) => {
    let isValid = true;
    for (let i = 0; i < val?.length; i++) {
      if (!formData[val[i]]) {
        isValid = false;
        break;
      }
    }
    return isValid;
  };

  const handleCurrentLocation = async () => {
    const {address, coords} =
      await fetchLocationFromReverseGeoCodeFromLatLong();
    handleMapRedirection(coords, address);
  };
  const handleMapRedirection = (
    geoCoords: GeoCoordinates,
    address?: JMAddressModel,
  ) => {
    navigateTo(
      navBeanObj({
        ...config?.locationBlock?.currentLocation?.cta,
        params: {coords: geoCoords, address},
      }),
      navigation,
    );
  };
  const handleEditDeliveryLocation = () => {
    handleMapRedirection(geoCordinate, address);
  };
  const handleSearchRedirection = () => {
    navigateTo(
      navBeanObj({
        ...config?.locationBlock?.search?.cta,
      }),
      navigation,
    );
  };
  const handleSaveAs = (val: string) => {
    return () => {
      handleFormData(AddressFormField.ADDRESS_TYPE)(val);
    };
  };
  const handleRedirection = () => {
    if (navigation?.canGoBack()) {
      navigation?.goBack();
    }
    // navigateTo(
    //   navBeanObj({
    //     ...config?.submitButton?.cta,
    //   }),
    //   navigation,
    // );
  };
  const handleAddressSubmit = () => {
    try {
      if (!validateForm()) {
        return;
      }

      const payload: JMAddressModel = {
        id: formData[AddressFormField.ID],
        city: formData[AddressFormField.CITY],
        state: formData[AddressFormField.STATE],
        pin: formData[AddressFormField.PINCODE],
        name: formData[AddressFormField.RECEIVER_NAME],
        phone: formData[AddressFormField.RECEIVER_NUMBER],
        address_type: formData[AddressFormField.ADDRESS_TYPE],
        address: formData[AddressFormField.ADDRESS],
        flat_or_house_no: formData[AddressFormField.HOUSE_NO],
        floor_no: formData[AddressFormField.FLOOR_NO],
        tower_no: formData[AddressFormField.TOWER_NO],
        area: formData[AddressFormField.BUILDING_APARTMENT_NAME],
        landmark: formData[AddressFormField.LANDMARK_AREA],
        lat: regionCamera?.center?.latitude,
        lon: regionCamera?.center?.longitude,
        input_mode: 'map',
        is_default_address: true,
      };

      if (formData[AddressFormField.ID]) {
        const request = generateUpdateAddressRequestBody(payload);
        updateAddress.mutate(request, {
          onSuccess: res => {
            if (res?.success) {
              handleRedirection();
            }
          },
        });
      } else {
        const request = generateSaveAddressRequestBody(payload);
        saveAddress.mutate(request, {
          onSuccess: res => {
            if (res?.success) {
              handleRedirection();
            }
          },
        });
      }
    } catch (error) {}
  };

  useEffect(() => {
    navigation.setParams({
      ...route.params,
      navTitle: formData[AddressFormField.ID]
        ? config?.header?.editNavTitle
        : route.params.navTitle,
    });
  }, []);

  return {
    ...props,
    address,
    regionCamera,
    config,
    formData,
    formError,
    navigationBean: route.params,
    handleInputRef,
    handleFormError,
    onChangeText,
    validateValue,
    handleAddressSubmit,
    handleSaveAs,
    shouldShowLocationBlock,
    shouldShowMapBlock,
    handleCurrentLocation,
    handleSearchRedirection,
    handlePincodeChange,
    handleEditDeliveryLocation,
  };
};

export default useJMAddressFormV1ScreenController;
