import {addStringPref} from '@jm/jiomart-common/src/JMAsyncStorageHelper';
import {AsyncStorageKeys} from '@jm/jiomart-common/src/JMConstants';
import {capitalizeFirstLetter} from '@jm/jiomart-common/src/utils/JMStringUtility';
import type {JMAddressModel} from '@jm/jiomart-networkmanager/src/models/Address/JMAddressModel';

type AddressVisibilityOptions = Partial<{
  flat_or_house_no: {
    length: number;
    isVisible: boolean;
  };
  floor_no: {
    length: number;
    isVisible: boolean;
  };
  tower_no: {
    length: number;
    isVisible: boolean;
  };
  address: {
    length: number;
    isVisible: boolean;
  };
  area: {
    length: number;
    isVisible: boolean;
  };
  landmark: {
    length: number;
    isVisible: boolean;
  };
  city: {
    length: number;
    isVisible: boolean;
  };
  state: {
    length: number;
    isVisible: boolean;
  };
  pin: {
    length: number;
    isVisible: boolean;
  };
}>;

const applyLength = (value: string, length?: number): string =>
  length && value.length > length ? value.slice(0, length) : value;
export const getFormatterAddress = (
  address: any,
  visible: AddressVisibilityOptions = {},
): string => {
  const show = (key: keyof AddressVisibilityOptions) =>
    visible[key]?.isVisible !== false; // default to true

  const getValue = (key: keyof AddressVisibilityOptions, value?: string) => {
    const isVisible = show(key);
    const maxLength = visible[key]?.length;
    return isVisible && value
      ? applyLength(capitalizeFirstLetter(value), maxLength)
      : '';
  };

  const flatOrHouseNoDisplay = getValue(
    'flat_or_house_no',
    address?.flat_or_house_no,
  );
  const floorNoDisplay = getValue('floor_no', address?.floor_no);
  const towerNoDisplay = getValue('tower_no', address?.tower_no);
  const addressDisplay = getValue('address', address?.address);
  const areaDisplay = getValue('area', address?.area);
  const landmarkDisplay = getValue('landmark', address?.landmark);
  const cityDisplay = getValue('city', address?.city);
  const stateDisplay = getValue('state', address?.state);
  const pinDisplay = show('pin') && address?.pin ? `\n${address.pin}` : '';

  const parts = [
    flatOrHouseNoDisplay,
    floorNoDisplay,
    towerNoDisplay,
    addressDisplay,
    areaDisplay ? areaDisplay + ',\n' : '',
    landmarkDisplay,
    cityDisplay,
    stateDisplay,
    pinDisplay,
  ];

  return parts
    .filter(part => !!part)
    .map(part =>
      part.endsWith(',') || part.endsWith('\n') ? part : part + ', ',
    )
    .join('')
    .replace(/,\s*$/, '');
};
export function updateDBAddress(val: JMAddressModel) {
  addStringPref(AsyncStorageKeys.X_LOCATION_DETAIL, JSON.stringify(val));
}

export function normalizeJMAddress({
  name,
  phone,
  address_type,
  address,
  flat_or_house_no,
  floor_no,
  tower_no,
  area,
  landmark,
  city,
  state,
  pin,
  lat,
  lon,
  country_iso_code = 'IN',
  country = 'India',
}: JMAddressModel): JMAddressModel {
  return {
    name,
    phone,
    address_type,
    address,
    flat_or_house_no,
    floor_no,
    tower_no,
    area,
    landmark,
    city,
    state,
    pin,
    lat,
    lon,
    country_iso_code,
    country,
  };
}
