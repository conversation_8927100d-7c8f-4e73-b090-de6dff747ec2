import {
  addStringPref,
  getPrefString,
} from '@jm/jiomart-common/src/JMAsyncStorageHelper';
import {AsyncStorageKeys} from '@jm/jiomart-common/src/JMConstants';

export async function updateDBCart(val: any) {
  try {
    const existingData = await readDBCart();
    const parsedExisting = existingData ? JSON.parse(existingData) : {};

    // Merge existing data with new data
    const updatedData = {...parsedExisting, ...val};

    await addStringPref(
      AsyncStorageKeys.CART_DATA,
      JSON.stringify(updatedData),
    );
  } catch (error) {
    throw error;
  }
}

export async function readDBCart() {
  try {
    let data = await getPrefString(AsyncStorageKeys.CART_DATA);
    return data ? JSON.parse(data || '') : null;
  } catch (error) {}
}
