import JMCartNetworkController from '@jm/jiomart-networkmanager/src/JMNetworkController/JMCartNetworkController';
import {useMutation} from '@tanstack/react-query';
import {useEffect} from 'react';
import {useDispatch} from 'react-redux';
import {setCart} from '../slices/cartSlice';
import {JMSharedViewModel} from '@jm/jiomart-common/src/JMSharedViewModel';
import {AppSourceType} from '@jm/jiomart-common/src/SourceType';

const cartController = new JMCartNetworkController();

const useCartOperation = () => {
  const dispatch = useDispatch();

  const getCart = useMutation({
    mutationFn: cartController.fetchCart,
    onSuccess: (response: any) => {
      dispatch(setCart(response));
    },
  });

  const addToCart = useMutation({
    mutationFn: cartController.addToCart,
    onSuccess: (response: any) => {
      if (response?.success) {
        getCart.mutate();
      }
    },
  });

  const removeFromCart = useMutation({
    mutationFn: cartController.removeFromCart,
    onSuccess: (response: any) => {
      if (response?.success) {
        getCart.mutate();
      }
    },
  });
  const generateAddToCartRequest = (request: any) => {
    switch (JMSharedViewModel.Instance.appSource) {
      case AppSourceType.JM_JCP:
        return {
          slug: request?.slug,
          item_id: request?.uid,
          item_size: request?.size,
          quantity: request?.quantity,
          product_group_tag: request?.product_group_tag,
          meta: request?.meta,
          identifiers: request?.identifiers,
          parent_item_identifiers: request?.parent_item_identifiers,
        };

      case AppSourceType.JM_BAU:
        return {
          product_code: request?.uid,
          qty: request?.quantity,
          seller_id: request?.seller_id,
          store_code: request?.store_id,
        };
      default:
        throw new Error('app source not found');
    }
  };

  const generateRemoveFromCartRequest = (request: any) => {
    switch (JMSharedViewModel.Instance.appSource) {
      case AppSourceType.JM_JCP:
        return {
          item_id: request?.uid,
          item_size: request?.size,
          quantity: request?.quantity,
          meta: request?.meta,
          identifiers: request?.identifiers,
          parent_item_identifiers: request?.parent_item_identifiers,
          slug: request?.slug,
          product_group_tag: request?.product_group_tag,
        };
      case AppSourceType.JM_BAU:
        return {
          product_code: request?.uid,
          qty: request?.quantity,
          seller_id: request?.seller_id,
          store_code: request?.store_id,
        };
      default:
        throw new Error('app source not found');
    }
  };

  return {
    getCart,
    addToCart,
    removeFromCart,
    generateAddToCartRequest,
    generateRemoveFromCartRequest,
  };
};

export default useCartOperation;
