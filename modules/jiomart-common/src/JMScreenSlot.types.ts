import {StatusBarStyle} from 'react-native';
import {
  Duration,
  IconColor,
  IconSize,
  JioColor,
  SchematicState,
  ToastType,
} from '@jio/rn_components/src/index.types';
import {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {AuthState} from './JMAuthType';
import {NavigationBean} from './JMNavGraphUtil';

export type ScreenSlotProps = {
  navigationBean: NavigationBean;
  navigation: NativeStackNavigationProp<any>;
  onCustomBackPress?: () => void;
  searchTextHandler?: (text: string) => void;
  onSubmitHandler?: (text: string) => void;
  onVisible?: () => void;
  onBackPressCallback?: () => void;
  headerIconsList?: HeaderIconProps;
  toastTypeData?: ToastTypeData;
  children?: (
    authState: AuthState,
    openDeliverToBarBtmSheet?: () => void,
  ) => React.ReactNode;
  text?: string;
  logo?: string;
  showClose?: boolean;
  showBack?: boolean;
  showAvatar?: boolean;
  bottomSheetContent?: React.ReactNode;
  headerCustomStyles?: {};
  headerTextStyle?: {};
  rightIconSize?: IconSize;
  leftIconSize?: IconSize;
  rightIconColor?: IconColor;
  leftIconColor?: IconColor;
  iconWidth?: number;
  iconHeight?: number;
  isShowFullScreen?: boolean;
  statusBarColor?: string;
  displayUploadHover?: boolean;
  darkMode?: boolean;
};

export interface DeeplinkData {
  mUri?: string;
  payload?: string;
}

export interface IconListModel {
  icon: string;
  cta?: NavigationBean;
  onClick: () => void;
  size?: string;
  color?: string;
}

export type HeaderIconProps = {
  leftIconList?: IconListModel[];
  rightIconList?: IconListModel[];
};

export type DeeplinkProps = {
  navigationBean: NavigationBean | null;
  navigation: NativeStackNavigationProp<any>;
  children?: (navBean: NavigationBean) => React.ReactNode;
};

export type KeyboardProps = {
  nativeResize?: boolean;
  iosInputFieldVisible?: boolean;
  children?: React.ReactNode;
};

export enum HeaderType {
  HIDDEN = 0,
  VISIBLE = 1,
  CUSTOM = 2,
  HIDE_SUBTITLE = 3,
}

export interface ToastTypeData {
  isVisible: boolean;
  message?: string;
  semanticState?: SchematicState;
  duration?: Duration;
  showClose?: boolean;
  type?: ToastType;
  offset?: number;
  genericToastType?: GenericToast;
  disableScreenTouch?: boolean;
  onDismiss?: () => void;
}

export type StatusBarProps = {
  statusBackgroundColor?: JioColor;
  darkMode?: boolean;
  fullScreen?: boolean;
};

export enum GenericToast {
  NO_INTERNET,
  FETCHING_DETAILS,
  LOW_CONNECTIVITY,
  SUCCESS,
}

export function getStatusBarStyle(darkMode: boolean = true): StatusBarStyle {
  return darkMode ? 'dark-content' : 'light-content';
}

export function genericToastTypeData(
  genericToastType: GenericToast,
  message: string | undefined = undefined,
  disableScreenTouch: boolean | undefined = undefined,
): ToastTypeData | undefined {
  switch (genericToastType) {
    case GenericToast.FETCHING_DETAILS: {
      return {
        isVisible: true,
        message: message ?? 'Fetching details...',
        semanticState: SchematicState.INFO,
        duration: Duration.PERSIST,
        type: ToastType.SPINNER,
        disableScreenTouch: disableScreenTouch ?? true,
      };
    }
    case GenericToast.NO_INTERNET: {
      return {
        isVisible: true,
        message: message ?? 'Internet not available',
        semanticState: SchematicState.INFO,
        duration: Duration.PERSIST,
        type: ToastType.NONE,
        genericToastType: GenericToast.NO_INTERNET,
        disableScreenTouch: disableScreenTouch ?? true,
      };
    }
    case GenericToast.LOW_CONNECTIVITY: {
      return {
        isVisible: true,
        message: message ?? 'Low internet connectivity',
        semanticState: SchematicState.INFO,
        duration: Duration.PERSIST,
        type: ToastType.NONE,
        genericToastType: GenericToast.LOW_CONNECTIVITY,
        disableScreenTouch: disableScreenTouch ?? true,
      };
    }
    case GenericToast.SUCCESS: {
      return {
        isVisible: true,
        message: message ?? '',
        semanticState: SchematicState.SUCCESS,
        duration: Duration.MEDIUM,
        type: ToastType.NONE,
        disableScreenTouch: disableScreenTouch ?? true,
      };
    }
    default: {
      return undefined;
    }
  }
}
