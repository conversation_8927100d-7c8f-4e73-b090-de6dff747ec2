
export const getImageData = (iconPath: string) => {
    if (iconPath.startsWith("http://") || iconPath.startsWith("https://")) {
        return iconPath;
    } else if(iconPath.startsWith("Ic")){
       return iconPath
    }
    {
        switch (iconPath) {
            //ABDM icons
            case "jhh_national_health_authority_gray": {
                return require("../assets/jhh_national_health_authority_gray.png")
            }
            //HealthFeed icons
            case "jhh_briefcase": {
                return require("../assets/jhh_briefcase.png")
            }
            //Color Blindness images
            case "2": {
                return require("../assets/2.png")
            }
            case "5": {
                return require("../assets/5.png")
            }
            case "7": {
                return require("../assets/7.png")
            }
            case "8": {
                return require("../assets/8.png")
            }
            case "12": {
                return require("../assets/12.png")
            }
            case "16": {
                return require("../assets/16.png")
            }
            case "29": {
                return require("../assets/29.png")
            }
            case "35": {
                return require("../assets/35.png")
            }
            case "74": {
                return require("../assets/74.png")
            }
            case "96": {
                return require("../assets/96.png")
            }
            case "97": {
                return require("../assets/97.png")
            }
            case "not_readable_1": {
                return require("../assets/not_readable_1.png")
            }
            case "not_readable_2": {
                return require("../assets/not_readable_2.png")
            }
            default: {
                //return default icon URL
                return require("../assets/jhh_default.png")
                break;
            }
        }
    }
};