import {NativeModules} from 'react-native';
import { CustomCategoryItems } from '../../../jiomart-general/src/ui/SearchScreen/types/AllCategoriesDataManager';
import { CartDetailResponse } from '../../../jiomart-general/src/ui/SearchScreen/types/CartResponse';
import { fetchCartDetail } from '../../../jiomart-networkmanager/src/JMNetworkController/CartService';
// import {FirebaseCrashlytics} from '../utils/FirebaseUtility';

const {AuthModule} = NativeModules;

const havePropertyObj = {
  highlights: false,
  description: false,
};

export const isWishlistExist = async (uid: number) => {
  const isLogin = await AuthModule.isLoggedIn();
  if (isLogin) {
    // const wishlistResult = await CartModule.fetchMyWishList();
    // const wishlistData = JSON.parse(wishlistResult);
    // const isUidExists = wishlistData?.items.some(item => item.uid === uid);
    // if (isUidExists) {
    //   return true;
    // }
  }
  return false;
};

export const convertToPipeEncoded = (inputString: string): string => {
  // Split the input string by commas
  const items = inputString.split(',');
  // Join the items with '|'
  const pipeSeparated = items.join('|');
  // URL encode the string
  const encodedString = encodeURIComponent(pipeSeparated);
  return encodedString;
};

export const makeFilterQueryParams = (queryData: any) => {
  let resultString = '';
  let queryCount = 1;

  if (queryData) {
    let queryDataLength = Object.keys(queryData).length;

    for (const key in queryData) {
      if (queryData.hasOwnProperty(key)) {
        const value = queryData[key];
        let valueString = '';

        if (Array.isArray(value)) {
          valueString = value.join('||');
        } else if (value !== null && value !== undefined) {
          valueString = String(value);
        }
        resultString += `${key}:${valueString}`;
        if (queryCount < queryDataLength) {
          resultString += `:::`;
        }
        queryCount++;
      }
    }
  }
  console.log('🚀 ~ makeFilterQueryParams ~ resultString:', resultString);
  return resultString;
};
export const fetchCartDetailsUtil = async () => {
  // const response = await CartModule.fetchCartDetails();
  // const cartDetail: Cart2JSON = JSON.parse(response);
  // updateCartData(cartDetail.cartDetailResponse, productSlug);
  const cartParam = await AuthModule.getFetchRequestParams();
  const cartParams = await JSON.parse(cartParam);
  const queryParams = {
    id: cartParams.cartId,
    i: true,
    b: true,
    area_code: cartParams.pincode,
  };

  await fetchCartDetail(queryParams)
    .then(async cartdata => {
      const cart2JSON: CartDetailResponse = cartdata as CartDetailResponse;
      if (cart2JSON) {
        if (cartParams.cartId == '') {
          //await AuthModule.setCartId(cart2JSON.custom_cart.id);
          const queryParams1 = {
            id: cart2JSON.custom_cart.id,
            i: true,
            b: true,
            area_code: cartParams.pincode,
          };
          await fetchCartDetail(queryParams1)
            .then(cartdata1 => {
              const cart2JSON1: CartDetailResponse = cartdata1 as CartDetailResponse;
              if (cart2JSON1) {
                return cart2JSON1;
              }
            })
            .catch(error => {
              // FirebaseCrashlytics.sendCrashlogEvent(error.message.toString());
            });
        } else {
          return cart2JSON;
        }
      }
    })
    .catch(error => {});
};

interface PriceProps {
  price: number | undefined | null;
}

export function formatPrice(value: number | undefined) {
  if (value === 0) {
    return '0.00';
  }
  if (value) {
    let strValue = value.toString();

    let parts = strValue.split('.');
    let intValue = parts[0];
    let decimalValue = parts[1] || '00';

    if (intValue.length <= 3) {
      intValue = intValue;
    } else if (3 < intValue.length && intValue.length <= 5) {
      intValue = intValue.slice(0, -3) + ',' + intValue.slice(-3);
    } else {
      let cut = intValue.slice(0, -3);
      let o = [];
      while (cut) {
        o.push(cut.slice(-2));
        cut = cut.slice(0, -2);
      }
      o = o.reverse();
      intValue = o.join(',') + ',' + intValue.slice(-3);
    }

    decimalValue = decimalValue.padEnd(2, '0');
    let formattedValue = intValue + '.' + decimalValue;

    return formattedValue;
  }
  return null;
}



export const selectedL3Category = (
  category: CustomCategoryItems[],
  l3Slug: string,
) => {
  // category.
  let CategoryRes = category
    .map(l1Category => {
      let l2CategoryRes = l1Category.childs
        ?.map(l2Category => {
          let l3CategoryRes = l2Category.childs
            ?.map(l3Category => {
              return l3Category.slug === l3Slug ? l3Category : null;
            })
            .filter(removeNull => removeNull !== null);
          if (l3CategoryRes) {
            return l3CategoryRes[0];
          }
        })
        .filter(removeUndefined => removeUndefined !== undefined);

      if (l2CategoryRes) {
        return l2CategoryRes[0];
      }
    })
    .filter(removeUndefined => removeUndefined !== undefined);
  return CategoryRes[0];
};
export const selectedL1Category = (
  category: CustomCategoryItems[],
  l1Slug: string,
) => {
  for (let l1category of category) {
    if (l1category?.slug === l1Slug) {
      return l1category;
    }

    if (l1category?.childs) {
      for (let l2category of l1category.childs) {
        if (l2category?.slug === l1Slug) {
          return l2category;
        }
      }
    }
  }
  return null;
};

export const isAddCtaVisible = (verticalCode: string): boolean => {
  const excludedCategories = [
    'ELECTRONICS',
    'Fashion',
    'Wellness',
    'Furniture',
  ];
  return !excludedCategories.includes(verticalCode);
};

export const splitEqualText = (text: string, maxLength: number) => {
  if (text.length > maxLength) {
    const words = text.split(' '); // Split text into words
    let totalLength = 0;
    let midIndex = 0;

    // Find the index of the word closest to the middle
    for (let i = 0; i < words.length; i++) {
      totalLength += words[i].length;
      if (totalLength >= text.length / 2) {
        midIndex = i;
        break;
      }
    }

    // Concatenate words into two lines
    const firstLine = words.slice(0, midIndex).join(' ');
    const secondLine = words.slice(midIndex).join(' ');
    return `${firstLine}\n${secondLine}`;
  }
  return text;
};

export function breakSentence(sentence: string, maxLength: number): string {
  const words: string[] = sentence.split(' ');
  const chunks: string[] = [];
  let currentChunk: string = '';

  for (const word of words) {
    if ((currentChunk + ' ' + word).length <= maxLength) {
      currentChunk += (currentChunk === '' ? '' : ' ') + word;
    } else {
      chunks.push(currentChunk);
      currentChunk = word;
    }
  }

  if (currentChunk !== '') {
    chunks.push(currentChunk);
  }

  // Join the chunks with newline characters
  return chunks.join('\n');
}

export function formatDate(dateString: string) {
  const date = new Date(dateString);

  // Array of month names
  const monthNames = [
    'Jan',
    'Feb',
    'Mar',
    'Apr',
    'May',
    'Jun',
    'Jul',
    'Aug',
    'Sep',
    'Oct',
    'Nov',
    'Dec',
  ];

  const day = date.getDate();
  const month = monthNames[date.getMonth()];
  const year = date.getFullYear().toString().slice(-2); // Get last two digits of the year

  return `${day} ${month} ${year}`;
}

export const generateUniqueNumber = () => {
  const currentTime: number = new Date().getTime();
  const uniqueNumber: number = parseInt(
    currentTime.toString() + Math.floor(Math.random() * 100000).toString(),
  );
  return uniqueNumber;
};

export function upperCaseFirstWord(str: string) {
  return str.charAt(0).toUpperCase() + str.slice(1);
}
