import {
  Dimensions,
  Keyboard,
  Platform,
  Share,
  ShareContent,
} from 'react-native';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import {PlatformType, isNullOrUndefinedOrEmpty} from '../JMObjectUtility';
import DeviceInfo from 'react-native-device-info';
import {JMLogger} from './JMLogger';

export const rnVersion = '1.6';

interface VersionedItem {
  rnVersion?: string;
  androidVersion?: string;
  iosVersion?: string;
}

export const getRnNumberVersion = () => {
  const appVersion = rnVersion;
  const versionNumber = parseFloat(appVersion.replace(/\./g, ''));
  console.log(
    'getRnNumberVersion ' + appVersion + ' versionNumber ' + versionNumber,
  );
  return versionNumber;
};

export const getVersionSpecificFilterListData = <T extends VersionedItem>(
  listData: T[] | undefined | null,
): T[] | undefined | null => {
  const currentRnVersion = getRnNumberVersion();
  const currentAndroidVersion =
    Platform.OS === PlatformType.ANDROID ? getNumberAppVersion() : -1;
  const currentIosVersion =
    Platform.OS === PlatformType.IOS ? getNumberAppVersion() : -1;
  return listData?.filter(item => {
    const rnVersion = item.rnVersion || '';
    const androidVersion = item.androidVersion || '';
    const iosVersion = item.iosVersion || '';
    JMLogger.log(
      'getVersionSpecificFilterData- rnVersion ' +
        rnVersion +
        ' androidVersion ' +
        androidVersion +
        ' iosVersion ' +
        iosVersion,
    );
    return (
      (isNullOrUndefinedOrEmpty(rnVersion.toString()) ||
        splitVersionAndCheck(rnVersion, currentRnVersion)) &&
      (currentAndroidVersion === -1 ||
        isNullOrUndefinedOrEmpty(androidVersion.toString()) ||
        splitVersionAndCheck(androidVersion, currentAndroidVersion)) &&
      (currentIosVersion === -1 ||
        isNullOrUndefinedOrEmpty(iosVersion.toString()) ||
        splitVersionAndCheck(iosVersion, currentIosVersion))
    );
  });
};

export const checkVersionSpecificObjectData = <T extends VersionedItem>(
  item: T,
): boolean => {
  const currentRnVersion = getRnNumberVersion();
  const currentAndroidVersion =
    Platform.OS === PlatformType.ANDROID ? getNumberAppVersion() : -1;
  const currentIosVersion =
    Platform.OS === PlatformType.IOS ? getNumberAppVersion() : -1;
  const rnVersion = item.rnVersion || '';
  const androidVersion = item.androidVersion || '';
  const iosVersion = item.iosVersion || '';
  JMLogger.log(
    'checkVersionSpecificObjectData rnVersion ' +
      rnVersion +
      ' androidVersion ' +
      androidVersion +
      ' iosVersion ' +
      iosVersion,
  );
  return (
    (isNullOrUndefinedOrEmpty(rnVersion.toString()) ||
      splitVersionAndCheck(rnVersion, currentRnVersion)) &&
    (currentAndroidVersion === -1 ||
      isNullOrUndefinedOrEmpty(androidVersion.toString()) ||
      splitVersionAndCheck(androidVersion, currentAndroidVersion)) &&
    (currentIosVersion === -1 ||
      isNullOrUndefinedOrEmpty(iosVersion.toString()) ||
      splitVersionAndCheck(iosVersion, currentIosVersion))
  );
};

export const splitVersionAndCheck = (
  versionData: string,
  currentVersion: number,
): boolean => {
  const versionArray = versionData.split(',');
  for (let i = 0; i < versionArray.length; i++) {
    try {
      const version = versionArray[i];
      JMLogger.log('splitVersionAndCheck version' + version);
      if (version.startsWith('>=')) {
        const splitVersion = version.replace('>=', '');
        if (currentVersion >= parseInt(splitVersion)) return true;
      } else if (version.startsWith('<=')) {
        const splitVersion = version.replace('<=', '');
        if (currentVersion <= parseInt(splitVersion)) return true;
      } else if (version.indexOf('-') >= 0) {
        const versionStrings = versionData.split('-');
        if (versionStrings.length === 2) {
          const firstPart = parseInt(versionStrings[0]);
          const secondPart = parseInt(versionStrings[1]);
          if (firstPart <= currentVersion && currentVersion <= secondPart)
            return true;
        }
      } else if (currentVersion === parseInt(version)) {
        return true;
      }
    } catch (error) {
      JMLogger.log('splitVersionAndCheck ' + error);
    }
  }
  return false;
};

export const getDynamicString = (string: string, values: string[]) => {
  const stringToReplace = '_VALUE_';
  let newString = `${string}`;
  values.forEach((value, index) => {
    newString = newString.replace(`${stringToReplace}${index}`, value);
  });
  return newString;
};

export const getScreenHeight = () => {
  return Dimensions.get('window').height;
};

export const getChipHeight = () => {
  return getScreenHeight() / 800;
};

export const getChipWidth = () => {
  return getScreenWidth() / 360;
};

export const getScreenWidth = () => {
  return Dimensions.get('window').width;
};

export const getStatusBarHeight = () => {
  return useSafeAreaInsets().top;
};

export const getNavigationBarHeight = () => {
  return useSafeAreaInsets().bottom;
};

export const hideKeyboard = () => {
  Keyboard.dismiss(); // Dismiss the keyboard
};

export const handleShareIntent = (shareData: ShareContent) => {
  Share.share(shareData)
    .then(res => console.log('Share result: ', res))
    .catch(err => console.log('Error sharing: ', err));
};

export const removePrefixes = (name: string): string => {
  const prefixes = ['mr.', 'mrs.', 'ms.', 'dr.'];
  const nameLowerCase = name?.toLowerCase();
  for (const prefix of prefixes) {
    if (nameLowerCase.startsWith(prefix)) {
      return name.slice(prefix.length).trim();
    }
  }
  return name;
};

export const getProfileNameInitials = (name: string): string => {
  const processedName = removePrefixes(name);
  const nameParts = processedName.split(' ');
  const firstInitial = nameParts[0] ? nameParts[0][0].toUpperCase() : '';
  const secondInitial = nameParts[1] ? nameParts[1][0].toUpperCase() : '';
  return `${firstInitial}${secondInitial}`;
};

export function convertHashMapToQueryParam(obj?: {[key: string]: string}) {
  if (isNullOrUndefinedOrEmpty(obj)) {
    return null;
  }
  return Object.entries(obj!)
    .map(
      ([key, value]) =>
        `${encodeURIComponent(key)}=${encodeURIComponent(value)}`,
    )
    .join('&');
}

export function convertQueryParamToHashMap(
  queryString?: string,
): {[key: string]: string} | null {
  try {
    if (isNullOrUndefinedOrEmpty(queryString)) {
      return null;
    }
    return queryString!.split('&').reduce((acc, pair) => {
      const [key, value] = pair.split('=').map(decodeURIComponent);
      acc[key] = value;
      return acc;
    }, {} as {[key: string]: string});
  } catch (error) {
    return null;
  }
}

export const getAppVersion = () => {
  const appVersion = DeviceInfo.getVersion();
  console.log('getAppVersion ' + appVersion);
  return appVersion;
};

export const getNumberAppVersion = () => {
  const appVersion = DeviceInfo.getVersion();
  const versionNumber = parseFloat(appVersion.replace(/\./g, ''));
  console.log(
    'getAppVersion ' + appVersion + ' versionNumber ' + versionNumber,
  );
  return versionNumber;
};

export const isValidFileType = (
  fileName: string | null | undefined,
): boolean => {
  if (fileName === '') return false;
  try {
    const allowedExtensions = ['png', 'jpg', 'jpeg', 'pdf'];
    if (!fileName || !fileName.includes('.')) return false;
    const extension = fileName.split('.').pop()?.toLowerCase() || '';
    const result = allowedExtensions.includes(extension);
    console.log('isValidFileType result>>>>>>:', result);
    return result;
  } catch (error) {
    console.error('Error in isValidFileType:', error);
    return false;
  }
};

export const generateFilterReq = (req: {[key: string]: string[]}) => {
  return req
    ? Object.entries(req)
        .map(([key, values]) => `${key}:${values.join('||')}`)
        .join(':::')
    : '';
};

export const formatCategoryName = (value: string) => {
  if (value?.includes('::')) {
    return value?.split('::')?.pop() ?? '';
  }
  return value ?? '';
};