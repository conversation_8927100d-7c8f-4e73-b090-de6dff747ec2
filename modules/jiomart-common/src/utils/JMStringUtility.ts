export const capitalizeFirstLetter = (str: string) => {
  if (!str) {
    return '';
  }
  return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase();
};

export function formatPrice(value: number | any) {
  if (value == 0) {
    return '0.00';
  }
  if (value) {
    let strValue = value.toString();

    let parts = strValue.split('.');
    let intValue = parts[0];
    let decimalValue = parts[1] || '00';

    if (intValue.length <= 3) {
      intValue = intValue;
    } else if (3 < intValue.length && intValue.length <= 5) {
      intValue = intValue.slice(0, -3) + ',' + intValue.slice(-3);
    } else {
      let cut = intValue.slice(0, -3);
      let o = [];
      while (cut) {
        o.push(cut.slice(-2));
        cut = cut.slice(0, -2);
      }
      o = o.reverse();
      intValue = o.join(',') + ',' + intValue.slice(-3);
    }

    decimalValue = decimalValue.padEnd(2, '0');
    let formattedValue = intValue + '.' + decimalValue;

    return formattedValue;
  }
  return null;
}
