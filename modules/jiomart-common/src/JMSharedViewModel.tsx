import {NavigationContainerRef} from '@react-navigation/native';
import {useRef} from 'react';
import {Platform} from 'react-native';
import {EventTriggerChannel} from './AnalyticsParams';
import {AuthState} from './JMAuthType';
import {NavigationBean} from './JMNavGraphUtil';
import {DeeplinkData} from './JMScreenSlot.types';
import {AppSourceType} from './SourceType';
import {JMEnvironment} from '@jm/jiomart-networkmanager/src/JMEnvironmentConfig';

export class JMSharedViewModel {
  private static _instance: JMSharedViewModel;

  private constructor() {}

  public static get Instance() {
    return this._instance || (this._instance = new this());
  }
  public appSource = AppSourceType.JM_JCP;
  public environment = JMEnvironment.SIT;
  public userAuthenticationStatus = AuthState.AUTHENTICATED;
  public eventTriggerChannel = EventTriggerChannel.FIREBASE;
  public globalThemeToken = 'mint,sky_midnight,orange,light';
  public deeplinkUrl = '';
  public externalDeeplinkData: DeeplinkData | undefined = undefined;
  public navigationRef = useRef<NavigationContainerRef<any>>(null);
  public loggedInStatus = false;
  private userProfileExist = false;
  private pinCreated = false;
  private navigationBeanData: NavigationBean | undefined = undefined;
  public profileSwitchedInLastSession = false;
  public isPrimaryAccount = true;
  private userProfileApiCalled: Map<string, boolean> = new Map();
  public linkedByMeApiCalled: boolean = false;
  public appVersion =
    this.appSource === AppSourceType.JM_BAU ? '2.0.42' : '312';
  public corporateId = '';
  public appLevelJwtToken = '';
  public accountSwitched: boolean = false;
  public tempMobileNumber = '';
  public pushToken: string = '';
  public webIosClientId = '';
  public iosClientId = '';
  public statusApiInProgress = false;
  public networkAvailable = true;
  public logoutCalled = false;
  public previousPageURL = '';

  public resetViewModelData() {
    this.setUserProfileExist(false);
    this.setPinCreated(false);
    this.setPinCreated(false);
    this.accountSwitched = false;
    this.linkedByMeApiCalled = false;
    this.isPrimaryAccount = true;
    this.profileSwitchedInLastSession = false;
    this.pushToken = '';
    this.corporateId = '';
  }

  public getUserProfileApiCalled(jioId: string): boolean {
    return this.userProfileApiCalled.get(jioId) ?? false;
  }

  public setUserProfileApiCalled(jioId: string, userProfileApiCalled: boolean) {
    this.userProfileApiCalled.set(jioId, userProfileApiCalled);
  }

  public getNavigationData(): NavigationBean | undefined {
    return this.navigationBeanData;
  }

  public getLoggedInStatus(): boolean {
    return this.loggedInStatus ?? false;
  }

  public getUserProfileExist(): boolean {
    return this.userProfileExist;
  }

  public getPinCreated(): boolean {
    return this.pinCreated;
  }

  public setLoggedInStatus(status: boolean) {
    this.loggedInStatus = status;
  }

  public setNavigationBeanData(navigationBean: NavigationBean) {
    this.navigationBeanData = navigationBean;
  }

  public setUserProfileExist(userProfileExist: boolean) {
    this.userProfileExist = userProfileExist;
  }

  public setPinCreated(pinCreated: boolean) {
    this.pinCreated = pinCreated;
  }

  public setAppSourceType(sourceType: AppSourceType) {
    this.appSource = sourceType;
  }
  public setEventTriggerChannel(eventTriggerChannel: EventTriggerChannel) {
    this.eventTriggerChannel = eventTriggerChannel;
  }

  public setNetworkAvailableStatus(status: boolean) {
    this.networkAvailable = status;
  }

  public setUserAuthenticationStatus(authStatus: AuthState) {
    this.userAuthenticationStatus = authStatus;
  }
  public setDeeplinkUrlData(deeplinkUrl: string) {
    this.deeplinkUrl = deeplinkUrl;
  }

  public setPreviousPageURL(url: string) {
    this.previousPageURL = url;
  }

  public getQuery() {
    return this.appSource === AppSourceType.JM_JCP
      ? `source_attribution=JioMartApp-CPS&os=${
          Platform.OS === 'ios' ? 'iOS' : 'android'
        }&version=${this.appVersion}`
      : `source_attribution=JioMartApp-CPS&utm_source=JioMartApp-CPS&utm_medium=CPS&utm_campaign=JioMartApp&os=${
          Platform.OS === 'ios' ? 'iOS' : 'android'
        }&version=${this.appVersion}`;
  }
}
