import type {NativeStackScreenProps} from '@react-navigation/native-stack';
import type {NavigationStackData} from './JMNavGraphUtil';

enum JMAddressScreenEntry {
  ADDRESS_LIST_SCREEN = 'JMAddressListScreen',
  ADDRESS_SEARCH_SCREEN = 'JMAddressSearchScreen',
  ADDRESS_MAP_SCREEN = 'JMAddressMapScreen',
  ADDRESS_FORM_SCREEN = 'JMAddressFormScreen',
  ADDRESS_FORM_V1_SCREEN = 'JMAddressFormV1Screen',
}

enum JMCategoryScreenEntry {
  ALL_CATEGORIES = 'AllCategories',
}

enum JMSearchScreenEntry {
  SEARCH_SCREEN = 'JMSearchScreen',
}

enum JMProductScreenEntry {
  PRODUCT_LISTING_SCREEN = 'JMProductListingScreen',
  PRODUCT_GRID_LISTING_SCREEN = 'JMProductGridListingScreen',
  PRODUCT_SEARCH_LISTING_SCREEN = 'JMProductSearchListingScreen',
}

enum JMScreenEntry {
  SPLASH_SCREEN = 'JMSplashScreen',
  JIOMART_MAIN_UI = 'JioMartMainUI',
  COMMON_WEB_VIEW = 'CommonWebViewScreen',
  ONE_RETAIL_SCREEN = 'OneRetailUI',
}

const AppScreens = {
  ...JMScreenEntry,
  ...JMCategoryScreenEntry,
  ...JMProductScreenEntry,
  ...JMAddressScreenEntry,
  ...JMSearchScreenEntry,
};

type AppScreensType = (typeof AppScreens)[keyof typeof AppScreens];

export type ScreenProps<T extends AppScreensType> = NativeStackScreenProps<
  NavigationStackData,
  T
>;
export {AppScreens};
