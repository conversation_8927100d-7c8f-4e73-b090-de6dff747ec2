import {PERMISSIONS} from 'react-native-permissions';

export enum AppPrefernceKeys {
  PREFERENCE_KEY_IS_BIOMETRIC_ENABLED = 'PREFERENCE_KEY_IS_BIOMETRIC_ENABLED',
}

export enum RQKey {
  GET_ADDRESS = 'GET_ADDRESS',
  USER_PROFILE = 'USER PROFILE',
  PRODUCT_LIST = 'PRODUCT_LIST',
  PRODUCT_SEARCH_LIST = 'PRODUCT_SEARCH_LIST',
  CATEGORY = 'CATEGORY',
  PRODUCT_MULTI_VARIANTS_SIZE = 'PRODUCT_MULTI_VARIANTS_SIZE',
}

export const AndroidPermission = {...PERMISSIONS.ANDROID};
export const IOSPermission = {...PERMISSIONS.IOS};
export enum EventEmitterKeys {
  CLOSE = 'onClose',
  ON_LOGGED_IN = 'onLogin',
  WEB_VIEW_EVENT_EMITT = 'WebViewEventEmitt',
  PINCODE_CHANGE = 'PINCODE_CHANGE',
}

export enum WebViewEventKeys {
  SEND_TO_WEB_CRA_DETAILS = 'sendToWebCraDetails',
}

export enum AsyncStorageKeys {
  USER_DETAILS = 'UserDetails',
  USER_SESSION = 'UserSession',
  GUEST_USER_SESSION = 'GuestUserSession',
  CRA_USER_SESSION_DATA = 'CraUserSessionData',
  JCP_USER_SESSION_DATA = 'JcpUserSessionData',
  X_LOCATION_DETAIL = 'xLocationDetail',
  PINCODE = 'Pincode',
  CART_DATA = 'cartData',
  DISCOVER_MORE = 'DiscoverMore',
  RECENT_SEARCH = 'RecentSearch',
  ALL_CATEGORIES = 'AllCategories',
  RECOMMENDED_ITEMS = 'RecommendedItems',
  PERMISSION_GRANTED = 'PERMISSION_GRANTED',
  PRIVACY_POLICY = 'PRIVACY_POLICY',
}
