import {getPrefString, removeStringPref} from './JMAsyncStorageHelper';
import {AsyncStorageKeys} from './JMConstants';
import {isNullOrUndefinedOrEmpty} from './JMObjectUtility';

export const getUserDetails = () => {
  const userDetails = getPrefString(AsyncStorageKeys.USER_DETAILS);
  if (isNullOrUndefinedOrEmpty(userDetails)) {
    return null;
  }
  return userDetails;
};

export const getUserSession = () => {
  const userSession = getPrefString(AsyncStorageKeys.USER_SESSION);
  if (isNullOrUndefinedOrEmpty(userSession)) {
    return null;
  }
  return userSession;
};

export const getGuestUserSession = () => {
  const guestUserSession = getPrefString(AsyncStorageKeys.GUEST_USER_SESSION);
  if (isNullOrUndefinedOrEmpty(guestUserSession)) {
    return null;
  }
  return guestUserSession;
};

export const deleteUserDetails = () => {
  removeStringPref(AsyncStorageKeys.USER_DETAILS);
};

export const deleteUserSession = () => {
  removeStringPref(AsyncStorageKeys.USER_SESSION);
};

export const deleteGuestUserSession = () => {
  removeStringPref(AsyncStorageKeys.GUEST_USER_SESSION);
};
