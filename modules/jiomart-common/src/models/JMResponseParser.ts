import {
  isNullOrUndefinedOrEmpty,
  NullableString,
} from '../../../jiomart-common/src/JMObjectUtility';
import {JMBaseUrlKeys} from '../../../jiomart-networkmanager/src/JMEnvironmentConfig';
import {decryptedData} from '../../../jiomart-networkmanager/src/api/utils/JMEncryptionUtility';
import {
  JMError,
  JMErrorCodes,
  JMErrorMessages,
} from '../../../jiomart-networkmanager/src/api/utils/JMErrorCodes';

export const parseApiResponse = async <T>(
  contents: NullableString,
  plaform = JMBaseUrlKeys.LEGACY,
): Promise<T> => {
  try {
    if (isNullOrUndefinedOrEmpty(contents)) {
      throw {
        code: JMErrorCodes.DATA_ERROR,
        message: JMErrorMessages.DATA_ERROR,
      } as J<PERSON><PERSON><PERSON>;
    }
    return await decryptedData(contents, plaform);
  } catch (error) {
    console.error('Error processing response:', error);
    throw {
      code: JMErrorCodes.PARSE_ERROR,
      message: JMErrorMessages.PARSE_ERROR,
    } as JMError;
  }
};

export const jsonParse = <T>(contents: NullableString): T | null => {
  try {
    if (isNullOrUndefinedOrEmpty(contents)) {
      return null;
    }
    return JSON.parse(contents!);
  } catch (error) {
    console.error('Error processing response:', error);
    throw {
      code: JMErrorCodes.PARSE_ERROR,
      message: JMErrorMessages.PARSE_ERROR,
    } as JMError;
  }
};
