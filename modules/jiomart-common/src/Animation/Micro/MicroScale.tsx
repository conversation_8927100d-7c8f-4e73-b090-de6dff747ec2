import React from 'react';
import {GestureDetector} from 'react-native-gesture-handler';
import useMicroScale from './hook/useMicroScale';
import Animated from 'react-native-reanimated';

interface MicroScaleProps {
  children: React.ReactNode;
  scale: number;
}

const MicroScale = ({children, scale}: MicroScaleProps) => {
  if (scale === null || scale === undefined) {
    throw Error('invalid value for scale it must be number');
  }
  const {animatedStyles, tap} = useMicroScale(scale);
  return (
    <GestureDetector gesture={tap}>
      <Animated.View style={animatedStyles}>{children}</Animated.View>
    </GestureDetector>
  );
};

export default MicroScale;
