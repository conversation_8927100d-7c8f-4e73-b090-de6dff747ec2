import {AppScreens} from './JMAppScreenEntry';
import {HeaderType} from './JMScreenSlot.types';

export let navGraph = new Map<string, string>([
  [AppScreens.COMMON_WEB_VIEW, AppScreens.COMMON_WEB_VIEW],
  [AppScreens.ALL_CATEGORIES, AppScreens.ALL_CATEGORIES],
  [AppScreens.ADDRESS_LIST_SCREEN, AppScreens.ADDRESS_LIST_SCREEN],
  [AppScreens.ADDRESS_SEARCH_SCREEN, AppScreens.ADDRESS_SEARCH_SCREEN],
  [AppScreens.ADDRESS_MAP_SCREEN, AppScreens.ADDRESS_MAP_SCREEN],
  [AppScreens.ADDRESS_FORM_SCREEN, AppScreens.ADDRESS_FORM_SCREEN],
  [AppScreens.ADDRESS_FORM_V1_SCREEN, AppScreens.ADDRESS_FORM_V1_SCREEN],
  [AppScreens.PRODUCT_LISTING_SCREEN, AppScreens.PRODUCT_LISTING_SCREEN],
  [
    AppScreens.PRODUCT_GRID_LISTING_SCREEN,
    AppScreens.PRODUCT_GRID_LISTING_SCREEN,
  ],
  [
    AppScreens.PRODUCT_SEARCH_LISTING_SCREEN,
    AppScreens.PRODUCT_SEARCH_LISTING_SCREEN,
  ],
  [AppScreens.SPLASH_SCREEN, AppScreens.SPLASH_SCREEN],
  [AppScreens.ONE_RETAIL_SCREEN, AppScreens.ONE_RETAIL_SCREEN],
  [AppScreens.SEARCH_SCREEN, AppScreens.SEARCH_SCREEN],
]);

export type NavigationStackData = {
  [K in (typeof AppScreens)[keyof typeof AppScreens]]: NavigationBean;
};

export interface OtpParam {
  number: string;
}

export interface NavigationBean {
  navTitle?: string;
  destination: string;
  loginRequired?: boolean;
  actionUrl?: string;
  actionType: string;
  headerVisibility?: HeaderType;
  userJourneyRequiredState?: number;
  bundle?: string;
  userAuthenticationRequired?: number;
  tokenType?: TokenType;
  params?: any;
  navigationType?: NavigationType;
  source?: string;
  screenName?: string;
  gaModel?: GAModel;
  headerType?: number;
  shouldShowDeliverToBar?: boolean;
}

export enum UserJourneyRequiredState {
  LOGIN_NOT_REQUIRED = 0,
  LOGIN_REQUIRED = 1,
}

export interface GAModel {
  category: string;
  label: string;
  action: string;
  commonCustomDimesion: string;
  appName: string;
  entrySource: string;
}

export enum AuthenticationType {
  USER_PROFILE_CREATED = 0,
  SPECIAL_TOKEN = 1,
}

export enum TokenType {
  GENERAL_TOKEN = 0,
  SPECIAL_TOKEN = 1,
}

export enum ActionType {
  OPEN_NATIVE = 'T001',
  OPEN_EXTERNAL = 'T002',
  OPEN_DEEPLINK = 'T004',
  OPEN_WEB_URL = 'T003',
  OPEN_WEB_URL_WITH_TOKEN = 'T005',
  OPEN_WEB_HTML = 'T006',
}

export enum NavigationType {
  NAVIGATE = 'navigate',
  REPLACE = 'replace',
  RESET = 'reset',
  PUSH = 'push',
}

export function navBeanObj({
  actionType,
  destination = '',
  actionUrl = '',
  userAuthenticationRequired = 2,
  navTitle = '',
  headerVisibility = HeaderType.VISIBLE,
  loginRequired = true,
  params = null,
  navigationType = NavigationType.NAVIGATE,
  bundle = '',
  source = '',
  headerType = 1,
  userJourneyRequiredState = 0,
  shouldShowDeliverToBar = false,
}: NavigationBean): NavigationBean {
  return {
    navTitle: navTitle,
    destination: destination,
    loginRequired: loginRequired,
    actionUrl: actionUrl,
    actionType: actionType,
    headerVisibility: headerVisibility,
    bundle: bundle,
    navigationType: navigationType,
    userAuthenticationRequired: userAuthenticationRequired,
    params: params,
    source: source,
    headerType: headerType,
    userJourneyRequiredState: userJourneyRequiredState,
    shouldShowDeliverToBar: shouldShowDeliverToBar,
  };
}

export function navBeanCopy(navBean: NavigationBean): NavigationBean {
  return {
    navTitle: navBean.navTitle,
    destination: navBean.destination,
    loginRequired: navBean.loginRequired,
    actionUrl: navBean.actionUrl,
    actionType: navBean.actionType,
    headerVisibility: navBean.headerVisibility,
    bundle: navBean.bundle,
    navigationType: navBean.navigationType,
    params: navBean.params,
    source: navBean.source,
  };
}
