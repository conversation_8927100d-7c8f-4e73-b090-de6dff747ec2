import {JMLocalStorageConstants} from '../api/constants/JMNetworkRequestConstants';
import {BifrostApiResponse, JMDevice, JMUser} from '../models/JMResponseModels';
import {Decoder} from '../api/utils/Decoder';
import {jsonParse} from '../../../jiomart-common/src/models/JMResponseParser';
import {AppSourceType} from '../../../jiomart-common/src/SourceType';
import {JMLogger} from '../../../jiomart-common/src/utils/JMLogger';
import {JMSharedViewModel} from '../../../jiomart-common/src/JMSharedViewModel';
import {isNullOrUndefinedOrEmpty} from '../../../jiomart-common/src/JMObjectUtility';
import {
  addStringPref,
  getPrefString,
} from '../../../jiomart-common/src/JMAsyncStorageHelper';
import {
  JMError,
  JMErrorCodes,
  JMErrorMessages,
} from '../api/utils/JMErrorCodes';

export class JMDatabaseManager {
  static isUserLoggedIn = async (): Promise<boolean> => {
    try {
      const contents = await getPrefString(JMLocalStorageConstants.USER_INFO);
      const userString = Decoder.base64DecodeValue(contents);
      JMLogger.log('isUserLoggedIn ' + userString);
      return !isNullOrUndefinedOrEmpty(jsonParse<JMUser>(userString)?.jio_id);
    } catch {
      return false;
    }
  };
  static saveUser = async (user: JMUser | null) => {
    if (isNullOrUndefinedOrEmpty(user)) {
      return;
    }
    const userString = Decoder.base64EncodeValue(JSON.stringify(user));
    if (isNullOrUndefinedOrEmpty(userString)) {
      return;
    }
    await addStringPref(JMLocalStorageConstants.USER_INFO, userString!);
  };

  static getUser = async (): Promise<JMUser | null> => {
    try {
      const contents = await getPrefString(JMLocalStorageConstants.USER_INFO);
      const userString = Decoder.base64DecodeValue(contents);
      if (isNullOrUndefinedOrEmpty(userString)) {
        return null;
      }
      return jsonParse<JMUser>(userString);
    } catch {
      throw {
        code: JMErrorCodes.PARSE_ERROR,
        message: JMErrorMessages.PARSE_ERROR,
      } as JMError;
    }
  };

  static saveDevice = async (device: JMDevice | null) => {
    if (isNullOrUndefinedOrEmpty(device)) {
      return;
    }
    const deviceString = Decoder.base64EncodeValue(JSON.stringify(device!));
    if (isNullOrUndefinedOrEmpty(deviceString)) {
      return;
    }
    await addStringPref(JMLocalStorageConstants.DEVICE_INFO, deviceString!);
  };

  static getDevice = async (): Promise<JMDevice | null> => {
    try {
      const contents = await getPrefString(JMLocalStorageConstants.DEVICE_INFO);
      const deviceString = Decoder.base64DecodeValue(contents);
      if (isNullOrUndefinedOrEmpty(deviceString)) {
        return null;
      }
      const deviceResponse = jsonParse<JMDevice>(deviceString);
      if (!isNullOrUndefinedOrEmpty(deviceResponse)) {
        return deviceResponse;
      }
      return null;
    } catch {
      throw {
        code: JMErrorCodes.PARSE_ERROR,
        message: JMErrorMessages.PARSE_ERROR,
      } as JMError;
    }
  };

  static saveCorporateServices = async (
    response: BifrostApiResponse | null,
  ) => {
    if (isNullOrUndefinedOrEmpty(response)) {
      return;
    }
    const responseString = Decoder.base64EncodeValue(JSON.stringify(response!));
    if (isNullOrUndefinedOrEmpty(responseString)) {
      return;
    }
    await addStringPref(
      JMLocalStorageConstants.CORPORATE_SERVICES,
      responseString!,
    );
  };

  static getCorporateServices =
    async (): Promise<BifrostApiResponse | null> => {
      try {
        const contents = await getPrefString(
          JMLocalStorageConstants.CORPORATE_SERVICES,
        );
        const responseString = Decoder.base64DecodeValue(contents);
        if (isNullOrUndefinedOrEmpty(responseString)) {
          return null;
        }
        const response = jsonParse<BifrostApiResponse>(responseString);
        if (!isNullOrUndefinedOrEmpty(response)) {
          return response;
        }
        return null;
      } catch {
        throw {
          code: JMErrorCodes.UNKNOWN_ERROR,
          message: JMErrorMessages.UNKNOWN_ERROR,
        } as JMError;
      }
    };
}
