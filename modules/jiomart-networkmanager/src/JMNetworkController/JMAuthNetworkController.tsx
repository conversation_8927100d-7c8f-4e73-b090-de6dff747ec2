import {useState} from 'react';
import {
  JHPlatofrmBasedTokenType,
  JMTokenService,
} from '../api/service/JMTokenService';
import {JMStorageService} from '../api/utils/JMStorageService';
import {
  isNullOrUndefinedOrEmpty,
  JMObjectTypes,
  NullableString,
} from '../../../jiomart-common/src/JMObjectUtility';
import {JMTokenKeyFetcher} from '../api/helpers/JMTokenKeyFetcher';
import {JMBaseUrlKeys} from '../JMEnvironmentConfig';

const useJMAuthNetworkState = () => {
  const [fetchingToken, setFetchingToken] = useState<boolean>(false);
  const tokenService = new JMTokenService();

  // Function to fetch access token
  const fetchLegacyAccessToken = async (): Promise<NullableString> => {
    setFetchingToken(true);
    const tokenData = await JMStorageService.getAccessToken(
      JMTokenKeyFetcher.getKeyForAccessToken(),
    );
    if (isNullOrUndefinedOrEmpty(tokenData)) {
      const tokenResponse = await tokenService.callCommonTokenApi(
        JHPlatofrmBasedTokenType.LEGACY_REQUEST_TOKEN,
      );
      if (typeof tokenResponse === JMObjectTypes.STRING) {
        return tokenResponse as string;
      }
    }
    setFetchingToken(false);
    return tokenData;
  };

  // Function to fetch access token
  const fetchBifrostLegacyAccessToken = async (): Promise<NullableString> => {
    setFetchingToken(true);
    const tokenData = await JMStorageService.getAccessToken(
      JMTokenKeyFetcher.getKeyForAccessToken(JMBaseUrlKeys.BIFROST),
    );
    if (isNullOrUndefinedOrEmpty(tokenData)) {
      const tokenResponse = await tokenService.callCommonTokenApi(
        JHPlatofrmBasedTokenType.BIFROST_REQUEST_TOKEN,
      );
      if (typeof tokenResponse === JMObjectTypes.STRING) {
        return tokenResponse as string;
      }
    }
    setFetchingToken(false);
    return tokenData;
  };

  return {
    fetchLegacyAccessToken,
    fetchBifrostLegacyAccessToken,
    fetchingToken,
  };
};

export default useJMAuthNetworkState;
