import {JMSharedViewModel} from '../../../jiomart-common/src/JMSharedViewModel';
import {AppSourceType} from '../../../jiomart-common/src/SourceType';
import JMJCPSearchNetworkController from '../JMJCPNetworkController/JMJCPSearchNetworkController';

class JMSearchNetworkController {
  private _addressController: any;
  constructor() {
    switch (JMSharedViewModel.Instance.appSource) {
      case AppSourceType.JM_JCP:
        this._addressController = new JMJCPSearchNetworkController();
        break;
      case AppSourceType.JM_BAU:
        break;
      default:
        this._addressController = new JMJCPSearchNetworkController();
    }
  }

  public getRecommendedProducts = async (params: any) => {
    try {
      let data: any = await this._addressController.getRecommendedProducts(
        params,
      );
      return data;
    } catch (error) {
      throw error;
    }
  };

  public fetchDiscoverMoreData = async () => {
    try {
      let data: any = await this._addressController.fetchDiscoverMoreData();
      return data;
    } catch (error) {
      throw error;
    }
  };

  public getSearchResults = async (params: any) => {
    try {
      const result = await this._addressController.getSearchResults(params);
      return result; // ✅ Properly return the result
    } catch (error) {
      console.error('Error fetching search results:', error);
      throw error;
    }
  };

  public fetchRecommendedItemsServiceability = async (bodyParams: any) => {
    try {
      let data: any =
        await this._addressController.fetchRecommendedItemsServiceability(
          bodyParams,
        );
      console.log('DATA2 -- ', data);
      return data;
    } catch (error) {
      throw error;
    }
  };
}

export default JMSearchNetworkController;
