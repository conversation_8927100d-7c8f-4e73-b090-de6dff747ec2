import {JMApiClient} from '../api/service/JMApiClient';
import JMRequestHelper from '../api/helpers/JMRequestHelper';
import {JMRequestConfigObject} from '../api/helpers/JMRequestConfig';
import {addStringPref} from '../../../jiomart-common/src/JMAsyncStorageHelper';
import {JMLoginEndpointKeys} from '../api/endpoints/JMApiEndpoints';
import {JMApiLogger} from '../models/JMApiLogger';
import {JMUpdateProfileApiRequest} from '../models/JMRequestModels';
import {JMDatabaseManager} from '../db/JMDatabaseManager';
import {JMBaseUrlKeys} from '../JMEnvironmentConfig';
import {parseApiResponse} from '../../../jiomart-common/src/models/JMResponseParser';
import { JMApiResponse, JMDevice, JMDeviceResponse, JMSendOtpResponse, JMUpdateProfileApiResponse, JMUser } from '../models/JMResponseModels';

const useJMLoginNetworkState = () => {
  const apiClient = new JMApiClient();
  const sendOtpApi = async ({
    mobileNumber,
  }: {
    mobileNumber: string;
  }): Promise<any | null> => {
    const params = {
      mobile: mobileNumber,
      country_code: '+91',
    };
    let requestHelper = new JMRequestHelper(
      JMRequestConfigObject.requestConfig(
        JMBaseUrlKeys.LEGACY,
        JMLoginEndpointKeys.SEND_OTP,
        params,
        null,
      ),
    );
    const response = await apiClient.request<JMSendOtpResponse>(requestHelper);
    console.log(
      'send otp response => ',
      response.status,
      ', otp id => ',
      response.contents,
      ', decrypted otp id => ',
      await parseApiResponse(response.contents),
    );
    return await parseApiResponse(response.contents);
  };
  const validateOtpApi = async ({
    mobileNumber,
    otp,
    otpId,
  }: {
    mobileNumber: string;
    otp: string;
    otpId: number;
  }): Promise<any | null> => {
    const params = {
      mobile_no: mobileNumber,
      otp: otp,
      otp_id: otpId,
      country_code: '+91',
    };
    let requestHelper = new JMRequestHelper(
      JMRequestConfigObject.requestConfig(
        JMBaseUrlKeys.LEGACY,
        JMLoginEndpointKeys.VERIFY_OTP,
        params,
        null,
      ),
    );
    const response = await apiClient.request<JMSendOtpResponse>(requestHelper);
    const user = await parseApiResponse<JMUser>(response.contents ?? '');
    await JMDatabaseManager.saveUser(user);
    return user;
    // return await decryptedData(response.contents);
  };

  const registerDeviceApi = async (): Promise<any> => {
    try {
      let requestHelper = new JMRequestHelper(
        JMRequestConfigObject.requestConfig(
          JMBaseUrlKeys.LEGACY,
          JMLoginEndpointKeys.REGISTER_DEVICE,
          null,
          null,
        ),
      );
      const response = await apiClient.request<JMApiResponse>(requestHelper);
      const device = await parseApiResponse<JMDeviceResponse>(
        response.contents ?? '',
      );
      await JMDatabaseManager.saveDevice(device.device as JMDevice);
      return device;
    } catch (error) {
      throw error;
    }
  };

  const createUserPin = async ({mpin}: {mpin: string}): Promise<any | null> => {
    try {
      const params = {
        mpin: mpin,
        device_model: 'Google-sdk_gphone64_arm64',
      };
      let requestHelper = new JMRequestHelper(
        JMRequestConfigObject.requestConfig(
          JMBaseUrlKeys.LEGACY,
          JMLoginEndpointKeys.CREATE_MPIN,
          params,
          null,
        ),
      );
      const response = await apiClient.request<JMApiResponse>(requestHelper);
      const user = await parseApiResponse<JMUser>(response.contents ?? '');
      await JMDatabaseManager.saveUser(user);
      JMApiLogger.logResponse(`response --> ${response.status}`);
      return response;
    } catch (error) {
      throw error;
    }
  };

  const updateProfileApi = async (
    dataObj: JMUpdateProfileApiRequest,
  ): Promise<any | null> => {
    try {
      let requestHelper = new JMRequestHelper(
        JMRequestConfigObject.requestConfig(
          JMBaseUrlKeys.LEGACY,
          JMLoginEndpointKeys.UPDATE_PROFILE,
          dataObj,
          null,
        ),
      );
      const response =
        await apiClient.request<JMUpdateProfileApiResponse>(requestHelper);
      return await parseApiResponse<JMUpdateProfileApiResponse>(
        response.message ?? '',
      );
    } catch (error) {
      throw error;
    }
  };

  return {
    sendOtpApi,
    validateOtpApi,
    registerDeviceApi,
    createUserPin,
    updateProfileApi,
  };
};

export default useJMLoginNetworkState;
