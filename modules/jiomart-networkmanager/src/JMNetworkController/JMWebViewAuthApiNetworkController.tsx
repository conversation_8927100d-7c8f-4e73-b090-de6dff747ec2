import {JMApiClient} from '../api/service/JMApiClient';
import JMRequestHelper from '../api/helpers/JMRequestHelper';
import {JMRequestConfigObject} from '../api/helpers/JMRequestConfig';
import {JMLoginEndpointKeys} from '../api/endpoints/JMApiEndpoints';
import {JMBaseUrlKeys} from '../JMEnvironmentConfig';
import { JMJWTTokenResponse } from '../models/JMResponseModels';

const useJMWebViewAuthNetworkState = () => {
  const apiClient = new JMApiClient();
  const jwtTokenApi = async (bodyParams: any): Promise<JMJWTTokenResponse> => {
    try {
      let requestHelper = new JMRequestHelper(
        JMRequestConfigObject.requestConfig(
          JMBaseUrlKeys.LEGACY,
          JMLoginEndpointKeys.JWT_TOKEN,
          bodyParams,
          null,
        ),
      );
      return await apiClient.request<JMJWTTokenResponse>(requestHelper);
    } catch (error) {
      throw error;
    }
  };

  return {
    jwtTokenApi,
  };
};
export default useJMWebViewAuthNetworkState;
