import axios, {AxiosRequestConfig, AxiosResponse} from 'axios';
import qs from 'qs';
import {addStringPref} from '../../../jiomart-common/src/JMAsyncStorageHelper';
import {AsyncStorageKeys} from '../../../jiomart-common/src/JMConstants';
import {
  getGuestUserSession,
  getUserSession,
} from '../../../jiomart-common/src/JMDataBaseManager';
import {JMSharedViewModel} from '../../../jiomart-common/src/JMSharedViewModel';
import {AppSourceType} from '../../../jiomart-common/src/SourceType';
import {getBaseURL} from '../JMEnvironmentConfig';
import JMCookieManager from '../api/helpers/JMCookieManager';

export const fetchGuestUserSession = async () => {
  try {
    const guestUserSession = await getGuestUserSession();
    if (guestUserSession) {
      return;
    }

    const guestSessionURL =
      'https://jiomart-sit.jio.com/mst/rest/v1/session/create/guest?channel=app-ios';
    const data = await callAPI(guestSessionURL, 'GET');
    if (data.status === 'success') {
      const jsonString = JSON.stringify(data.result.session);
      await addStringPref(AsyncStorageKeys.GUEST_USER_SESSION, jsonString);
      return data;
    } else {
      return null;
    }
  } catch (error) {
    return null;
  }
};

export const fetchUserSession = async (authCode: string) => {
  try {
    const userSessionURL =
      JMSharedViewModel.Instance.appSource === AppSourceType.JM_BAU
        ? 'https://jiomart-headless.extensions.jiox1.de/api/v1/cra/user/session'
        : getBaseURL() + '/ext/retail-auth/api/v1.0/native-user-session-create';

    let guestUserSessionData;

    if (JMSharedViewModel.Instance.appSource === AppSourceType.JM_BAU) {
      const guestUserSession = await getGuestUserSession();
      if (!guestUserSession) {
        return;
      }
      guestUserSessionData = JSON.parse(guestUserSession);
    }
    const headers = {
      'Content-Type': 'application/x-www-form-urlencoded',
    };
    const body =
      JMSharedViewModel.Instance.appSource === AppSourceType.JM_BAU
        ? {
            auth_code: authCode,
            channel: 'app-ios',
            merge_session: guestUserSessionData.id,
          }
        : {
            auth_code: authCode,
            application_id: '663e2332b7031043551ad694',
            return_ui_url:
              'https://jiomart.sit.jiomartjcp.com/ext/retail-auth/ui/session',
          };

    let response =
      JMSharedViewModel.Instance.appSource === AppSourceType.JM_BAU
        ? await callAPI(userSessionURL, 'POST', body)
        : await callPostFormDataAPI(userSessionURL, 'POST', body, headers);

    if (JMSharedViewModel.Instance.appSource === AppSourceType.JM_BAU) {
      if (response.status === 'success') {
        const mstarJsonString = JSON.stringify(response.data.mstar_session);
        const craJsonString = JSON.stringify(response.data.cra_session);
        const jcpJsonString = JSON.stringify(response.data.jcp_session);
        await addStringPref(AsyncStorageKeys.USER_SESSION, mstarJsonString);
        await addStringPref(
          AsyncStorageKeys.CRA_USER_SESSION_DATA,
          craJsonString,
        );
        await addStringPref(
          AsyncStorageKeys.JCP_USER_SESSION_DATA,
          jcpJsonString,
        );
        return response;
      } else {
        return null;
      }
    } else {
      const isSuccess = response?.data?.success;

      response = {
        ...response,
        data: {
          ...response.data,
          status: isSuccess ? 'success' : 'fail',
          data: {
            ...response.data.data,
            cra_session: {
              ...response.data?.data?.cra_session,
              access_token: response?.data?.data.cra_session.cra_access_token,
              refresh_token: response?.data?.data.cra_session.cra_refresh_token,
            },
          },
        },
      };

      if (response.data.status === 'success') {
        const craSession = response?.data?.data?.cra_session;
        const jcpSession = response?.data?.data?.jcp_session;

        const craJsonString = JSON.stringify(craSession);
        const jcpJsonString = JSON.stringify(jcpSession);

        JMCookieManager.setCookies({
          ['f.session']: {
            domain: jcpSession?.domain,
            httpOnly: jcpSession?.http_only,
            name: 'f.session',
            path: '/',
            secure: jcpSession?.secure,
            value: jcpSession?.cookie?.['f.session'],
          },
        });

        await addStringPref(
          AsyncStorageKeys.CRA_USER_SESSION_DATA,
          craJsonString,
        );
        await addStringPref(
          AsyncStorageKeys.JCP_USER_SESSION_DATA,
          jcpJsonString,
        );
        return response.data;
      } else {
        return null;
      }
    }
  } catch (error) {
    return null;
  }
};

export async function fetchUserDetails() {
  const userProfileURL =
    JMSharedViewModel.Instance.appSource === AppSourceType.JM_BAU
      ? 'https://jiomart-sit.jio.com/mst/rest/v1/entity/customer/get_details'
      : getBaseURL() + '/service/application/user/authentication/v1.0/session';
  const userSession = await getUserSession();
  const userSessionData = userSession ? JSON.parse(userSession) : null;
  const headers = {
    authtoken: userSessionData?.id,
    userid: userSessionData?.customer_id,
    pin: '400020',
  };
  const profileResponse = await callAPI(userProfileURL, 'GET', {}, headers);
  if (profileResponse.status === 'success') {
    const userDetails = JSON.stringify(profileResponse.result.your_details);
    await addStringPref(AsyncStorageKeys.USER_DETAILS, userDetails);
    return profileResponse;
  }
  return null;
}

export async function logoutUser() {
  const logoutURL =
    JMSharedViewModel.Instance.appSource === AppSourceType.JM_BAU
      ? 'https://jiomart-sit.jio.com/mst/rest/v1/session/logout'
      : getBaseURL() + 'service/application/user/authentication/v1.0/logout';
  const userSession = await getUserSession();
  const userSessionData = userSession ? JSON.parse(userSession) : null;
  const headers = {
    authtoken: userSessionData?.id,
    userid: userSessionData?.customer_id,
    pin: '400020',
  };
  const logoutResponse = await callAPI(logoutURL, 'GET', {}, headers);
  return logoutResponse;
}

export async function callAPI(
  configUrl: string,
  method: string,
  body: any = {},
  headers: any = {},
): Promise<any> {
  try {
    const defaultHeaders = {
      Accept: 'application/json',
      'Content-Type': 'application/json',
      'Cache-Control': 'no-cache',
      Host: 'jiomart.rep.jiomartjcp.com',
      Authorization: 'Bearer NjY4M2RmZTk5NzJhZmE4OWVmOTVlYWJmOjVrQlFwZVNQUQ==',

      ...headers,
    };
    const config: AxiosRequestConfig = {
      url: configUrl,
      method: method as any,
      headers: defaultHeaders,
    };

    if (method !== 'GET') {
      config.data = body;
    }

    const curlParts = [
      `curl -X ${method}`,
      `'${configUrl}'`,
      ...Object.entries(config.headers || {}).map(
        ([key, value]) => `-H "${key}: ${value}"`,
      ),
      ...(method !== 'GET' ? [`-d '${JSON.stringify(body)}'`] : []),
    ];
    const curlCommand = curlParts.join(' ');
    console.log('CURL:', curlCommand);

    const response: AxiosResponse<any> = await axios.request(config);
    return response.data;
  } catch (error) {
    console.log('🚀 ~ error:', error);
    throw error;
  }
}

export async function callPostFormDataAPI(
  configUrl: string,
  method: string,
  body: any = {},
  header: any = {},
): Promise<any> {
  try {
    const headers = {
      Accept: 'application/json',
      'Content-Type': 'application/json',
      'Cache-Control': 'no-cache',
      ...header,
    };

    let response;

    let bodyParam = body;

    if (headers['Content-Type'] === 'application/x-www-form-urlencoded') {
      bodyParam = qs.stringify(body); // converts to x-www-form-urlencoded
    }

    if (method === 'GET') {
      response = await axios.get(configUrl, {headers});
    } else if (method === 'POST') {
      response = await axios.post(configUrl, bodyParam, {headers});
    } else if (method === 'PUT') {
      response = await axios.put(configUrl, bodyParam, {headers});
    } else {
      throw new Error(`Unsupported HTTP method: ${method}`);
    }

    return response;
  } catch (error) {
    console.log('🚀 ~ error:', error);
    return null;
  }
}
