import {ApiConstant} from './APIConstant';
import {fetchDataFromAPI} from './SearchService';

const fetchProductList = async (
  selectedSlug: String,
  queryParams: any,
  body: any,
) => {
  return new Promise((resolve, reject) => {
    fetchDataFromAPI(
      ApiConstant.productListingSubPath/*DataLoaderMap['getCollectionItemsBySlug'] ||*/
      .path
        .replace('{slug}', selectedSlug),
      ApiConstant.productListingSubPath.method,
      queryParams,
      body,
    )
      .then(items => {
        resolve(items);
      })
      .catch(error => {
        // FirebaseCrashlytics.sendCrashlogEvent(error.message.toString());
        reject(error);
      });
  });
};

const searchProducts = async (queryParams: any, body: any) => {
  return new Promise((resolve, reject) => {
    fetchDataFromAPI(
      /*DataLoaderMap['getProducts'] ||*/ ApiConstant.getSearchResultSubPath
        .path,
      ApiConstant.getSearchResultSubPath.method,
      queryParams,
      body,
    )
      .then(items => {
        resolve(items);
      })
      .catch(error => {
        // FirebaseCrashlytics.sendCrashlogEvent(error.message.toString());
        reject(error);
      });
  });
};
// const fetchCartDetail = async (queryParam: any) => {
//   return new Promise((resolve, reject) => {
//     fetchDataFromAPI(
//       ApiConstant.getCartSubPath.path,
//       ApiConstant.getCartSubPath.method,
//       queryParam,
//       null,
//     )
//       .then(items => {
//         resolve(items);
//       })
//       .catch(error => {
//         reject(error);
//       });
//   });
// };

const fetchItemSizePrice = async (
  selectedSlug: String,
  queryParams: any,
  body: any,
) => {
  return new Promise((resolve, reject) => {
    fetchDataFromAPI(
      ApiConstant.getItemSize.path.replace('{slug}', selectedSlug),
      ApiConstant.getItemSize.method,
      queryParams,
      body,
    )
      .then(items => {
        resolve(items);
      })
      .catch(error => {
        // FirebaseCrashlytics.sendCrashlogEvent(error.message.toString());
        reject(error);
      });
  });
};
const fetchProductPrice = async (slug: string, size: string) => {
  const url = ApiConstant.getPriceSubPath.path
    .replace('{slug}', slug)
    .replace('{size}', size);
  return new Promise((resolve, reject) => {
    fetchDataFromAPI(url, ApiConstant.getPriceSubPath.method, null, null)
      .then(items => {
        resolve(items);
      })
      .catch(error => {
        // FirebaseCrashlytics.sendCrashlogEvent(error.message.toString());
        reject(error);
      });
  });
};

// const initiateAddToCartApi = async (queryParams: any, body: any) => {
//   let addCartRequest: AddCartRequest = {
//     items: [body],
//   };

//   return new Promise((resolve, reject) => {
//     fetchDataFromAPI(
//       ApiConstant.initiateAddToCartSubPath.path,
//       ApiConstant.initiateAddToCartSubPath.method,
//       queryParams,
//       addCartRequest,
//     )
//       .then(items => {
//         resolve(items);
//       })
//       .catch(error => {
//         reject(error);
//       });
//   });
// };

// const updateCartApi = async (queryParams: any, body: any) => {
//   return new Promise((resolve, reject) => {
//     fetchDataFromAPI(
//       ApiConstant.updateCartSubPath.path,
//       ApiConstant.updateCartSubPath.method,
//       queryParams,
//       body,
//       true
//     )
//       .then(items => {
//         resolve(items);
//       })
//       .catch(error => {
//         reject(error);
//       });
//   });
// };
export {
  fetchProductList,
  fetchItemSizePrice,
  //fetchCartDetail,
  fetchProductPrice,
  // initiateAddToCartApi,
  searchProducts,
  // updateCartApi,
};
