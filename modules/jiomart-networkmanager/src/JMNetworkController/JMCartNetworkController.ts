import {JMSharedViewModel} from '@jm/jiomart-common/src/JMSharedViewModel';
import {AppSourceType} from '@jm/jiomart-common/src/SourceType';
import JMJ<PERSON><PERSON>artNetworkController from '../JMJCPNetworkController/JMJCPCartNetworkController';
import JMBAUCartNetworkController from '../JMBAUNetworkController/JMBAUCartNetworkController';
import type {JMCartModel} from '../models/Cart/JMCartModel';

class JMCartNetworkController {
  private _cartController: any;
  constructor() {
    switch (JMSharedViewModel.Instance.appSource) {
      case AppSourceType.JM_JCP:
        this._cartController = new JMJCPCartNetworkController();
        break;
      case AppSourceType.JM_BAU:
        this._cartController = new JMBAUCartNetworkController();
        break;
      default:
        this._cartController = new JMBAUCartNetworkController();
    }
  }

  public fetchCart = async (): Promise<any> => {
    try {
      const response = await this._cartController.fetchCart();
      return this.mapToUnifiedCartResponse(response);
    } catch (error) {
      throw error;
    }
  };

  public addToCart = async (request: any): Promise<any> => {
    try {
      return await this._cartController.addToCart(request);
    } catch (error) {
      throw error;
    }
  };

  public removeFromCart = async (request: any): Promise<any> => {
    try {
      return await this._cartController.removeFromCart(request);
    } catch (error) {
      throw error;
    }
  };

  private mapToUnifiedCartResponse = (data: any): JMCartModel => {
    switch (JMSharedViewModel.Instance.appSource) {
      case AppSourceType.JM_JCP:
        return this.generateCartResponseFromJcp(data);
      case AppSourceType.JM_BAU:
        return this.generateCartResponseFromBau(data);
      default:
        return this.generateCartResponseFromBau(data);
    }
  };

  private generateCartResponseFromJcp = (data: any): any => {
    let cart: JMCartModel = {
      cart_id: data?.cart_id,
      success: data?.success,
      message: data?.message,
      items: data?.items?.map((item: any) => ({
        uid: item?.product?.uid,
        quantity: item?.quantity,
        moq: {
          min: item?.moq?.minimum ?? 1,
          max: item?.moq?.maximum,
          increment: item?.moq?.increment_unit ?? 1,
        },
        parent_item_identifiers: item?.parent_item_identifiers,
        identifiers: item?.identifiers,
        article_id: item?.article?.uid,
      })),
      cartCount:
        data?.items?.filter(
          (isParent: any) =>
            isParent?.parent_item_identifiers?.identifier === null,
        )?.length ?? 0,
    };
    return cart;
  };

  private generateCartResponseFromBau = (data: any): any => {
    let cart: JMCartModel = {
      cart_id: data?.cart_id,
      success: data?.success,
      message: data?.message,
      items: data?.items?.map((item: any) => ({
        uid: item?.product?.uid,
        quantity: item?.quantity,
        moq: {
          min: item?.minimum ?? 1,
          max: item?.maximum,
          increment: item?.increment_unit ?? 1,
        },
      })),
      cartCount: data?.items?.length ?? 0,
    };
    return cart;
  };
}

export default JMCartNetworkController;
