import {ApiConstant} from './APIConstant';
import JMSearchNetworkController from './JMSearchNetworkController';
import {fetchDataFromAPI} from './SearchService';

const service = () => {
  const searchController = new JMSearchNetworkController();
  // const fetchTrendingSearchData = async () => {
  //   return new Promise((resolve, reject) => {
  //     fetchDataFromAPI(
  //       ApiConstant.extUrlTrendingSearch,
  //       ApiConstant.GET,
  //       null,
  //       null,
  //     )
  //       .then(items => {
  //         resolve(items);
  //       })
  //       .catch(error => {
  //         reject(error);
  //       });
  //   });
  // };

  const fetchDiscoverMoreData = async () => {
    try {
      return searchController.fetchDiscoverMoreData();
    } catch (error) {
      throw error;
    }
  };

  const getSearchResults = async (queryParams: any) => {
    const searchController = new JMSearchNetworkController();
    try {
      const result = await searchController.getSearchResults({q: 'oil'});
      return result
    } catch (error) {
      console.error('API call failed:', error);
    }
  };

  // const getSearchResults = async (queryParams: any) => {
  //   return new Promise((resolve, reject) => {
  //     fetchDataFromAPI(
  //       ApiConstant.extUrlSearchResults,
  //       ApiConstant.GET,
  //       queryParams,
  //       null,
  //     )
  //       .then(items => {
  //         resolve(items);
  //       })
  //       .catch(error => {
  //         reject(error);
  //       });
  //   });
  // };

  const fetchRecommendedItems = async (queryParams: any) => {
    try {
      return await searchController.getRecommendedProducts(queryParams);
    } catch (error) {
      throw error;
    }
  };

  const fetchRecommendedItemsServiceability = async (bodyParams: any) => {
    try {
      return await searchController.fetchRecommendedItemsServiceability(
        bodyParams,
      );
    } catch (error) {
      throw error;
    }
  };

  // const fetchRecommendedItemsServiceability = async (bodyParams: any) => {
  //   return new Promise((resolve, reject) => {
  //     fetchDataFromAPI(
  //       ApiConstant.RecommendedProductsServiceabilityPath.path,
  //       ApiConstant.RecommendedProductsServiceabilityPath.method,
  //       null,
  //       bodyParams,
  //     )
  //       .then(items => {
  //         resolve(items);
  //       })
  //       .catch(error => {
  //         reject(error);
  //       });
  //   });
  // };

  const fetchWishListItems = async (collectionType: string) => {
    return new Promise((resolve, reject) => {
      fetchDataFromAPI(
        ApiConstant.fetchWishListItems.path.replace(
          '{collection_type}',
          collectionType,
        ),
        ApiConstant.fetchWishListItems.method,
        null,
        null,
      )
        .then(items => {
          resolve(items);
        })
        .catch(error => {
          reject(error);
        });
    });
  };

  const addToWishList = async (
    collectionType: string,
    collectionId: string,
  ) => {
    return new Promise((resolve, reject) => {
      fetchDataFromAPI(
        ApiConstant.addToWishList.path
          .replace('{collection_type}', collectionType)
          .replace('{collection_id}', collectionId),
        ApiConstant.addToWishList.method,
        null,
        null,
      )
        .then(items => {
          resolve(items);
        })
        .catch(error => {
          reject(error);
        });
    });
  };

  const removeFromWishList = async (
    collectionType: string,
    collectionId: string,
  ) => {
    return new Promise((resolve, reject) => {
      fetchDataFromAPI(
        ApiConstant.removeFromWishList.path
          .replace('{collection_type}', collectionType)
          .replace('{collection_id}', collectionId),
        ApiConstant.removeFromWishList.method,
        null,
        null,
      )
        .then(items => {
          resolve(items);
        })
        .catch(error => {
          reject(error);
        });
    });
  };

  return {
    // fetchTrendingSearchData,
    fetchDiscoverMoreData,
    getSearchResults,
    fetchRecommendedItems,
    fetchWishListItems,
    addToWishList,
    removeFromWishList,
    fetchRecommendedItemsServiceability,
  };
};

const SearchService = service();

export default SearchService;
