import { JMSharedViewModel } from '../../../jiomart-common/src/JMSharedViewModel';
import { AppSourceType } from '../../../jiomart-common/src/SourceType';
import JMBAUAllCategoriesNetworkController from '../JMBAUNetworkController/JMBAUAllCategoriesNetworkController';
import { getBaseURL } from '../JMEnvironmentConfig';
import JMJCPAllCategoriesNetworkController from '../JMJCPNetworkController/JMJCPAllCategoriesNetworkController';
import { JMCategoriesModel } from '../models/Categories/JMCategoriesModel';

class JMAllCategoriesNetworkController {
    private _allCategoriesController: any;

    constructor() {
        switch (JMSharedViewModel.Instance.appSource) {
            case AppSourceType.JM_JCP:
                this._allCategoriesController = new JMJCPAllCategoriesNetworkController();
                break;
            case AppSourceType.JM_BAU:
                this._allCategoriesController = new JMBAUAllCategoriesNetworkController();
                break;
            default:
                this._allCategoriesController = new JMBAUAllCategoriesNetworkController();
        }
    }

    public fetchDepartments = async () => {
        try {
            let data: any = await this._allCategoriesController.fetchDepartments();

            return data;
        } catch (error) {
            throw error;
        }
    };
    public fetchAllCategories = async () => {
        try {
            let data: any = await this._allCategoriesController.fetchAllCategories();
            return this.mapToUnifiedCategoriesResponse(data)
        } catch (error) {
            throw error;
        }
    };

    private mapToUnifiedCategoriesResponse = (data: any): JMCategoriesModel[] => {
        switch (JMSharedViewModel.Instance.appSource) {
            case AppSourceType.JM_JCP:
                return (
                    data || []
                );
            case AppSourceType.JM_BAU:
                return (
                    data.data.map((category: any) => ({
                        slug: category.url_path,
                        name: category.name,
                        banners: {
                            portrait: {
                                url: `${getBaseURL()}/` + category.thumbnail_image_path,
                            },
                        },
                        department: [],
                        childs: (category.sub_categories || []).map((subCat: any) => ({
                            slug: subCat.url_path,
                            name: subCat.name,
                            logo: {
                                url: `${getBaseURL()}/` + subCat.thumbnail_image_path,
                            },
                            department: [],
                            childs: (subCat.sub_categories || []).map((subSubCat: any) => ({
                                slug: subSubCat.url_path,
                                name: subSubCat.name,
                                logo: {
                                    url: `${getBaseURL()}/` + subSubCat.thumbnail_image_path,
                                },
                                department: [],
                            })),
                        })),
                    })) || []
                );
            default:
                return [];
        };
    }
}

export default JMAllCategoriesNetworkController;
