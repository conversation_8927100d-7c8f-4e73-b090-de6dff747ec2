import {JMSharedViewModel} from '../../../jiomart-common/src/JMSharedViewModel';
import {AppSourceType} from '../../../jiomart-common/src/SourceType';
import JMBAUPRoductNetworkController from '../JMBAUNetworkController/JMBAUProductNetworkController';
import JMJCPPRoductNetworkController from '../JMJCPNetworkController/JMJCPProductNetworkController';

class JMProductNetworkController {
  private _productController: any;

  constructor() {
    switch (JMSharedViewModel.Instance.appSource) {
      case AppSourceType.JM_JCP:
        this._productController = new JMJCPPRoductNetworkController();
        break;
      case AppSourceType.JM_BAU:
        this._productController = new JMBAUPRoductNetworkController();
        break;
      default:
        this._productController = new JMBAUPRoductNetworkController();
    }
  }

  public fetchProductSize = async (request: any) => {
    try {
      return this._productController.fetchProductSize(request);
    } catch (error) {
      throw error;
    }
  };
  public fetchProductPrice = async (request: any): Promise<any> => {
    try {
      return this._productController.fetchProductPrice(request);
    } catch (error) {
      throw error;
    }
  };

  public fetchProductList = async (request: any): Promise<any> => {
    try {
      return await this._productController.fetchProductList(request);
    } catch (error) {
      throw error;
    }
  };
  public fetchSearchProductList(request: any): Promise<any> {
    try {
      return this._productController.fetchSearchProductList(request);
    } catch (error) {
      throw error;
    }
  }
}

export default JMProductNetworkController;
