import { updateDataLoaderMap } from "../DataLoaderMap";
import { getBaseURL, JMDataLoaderUrl, JMHttpMethods } from "../JMEnvironmentConfig";
import { callAPI } from "./JMUserAPINetworkController";

export const fetchDataLoader = async () => {
    try {
      const items = await callAPI(
        getBaseURL() + JMDataLoaderUrl.getDataLoader,
        JMHttpMethods.GET,
        null,
        null,
      );
      updateDataLoaderMap(items?.data);
    } catch (error) {}
  };

