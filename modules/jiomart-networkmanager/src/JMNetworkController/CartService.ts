import { ApiConstant } from './APIConstant';
import { fetchDataFromAPI } from './SearchService';

const fetchCartDetail = async (queryParam: any) => {
  return new Promise((resolve, reject) => {
    fetchDataFromAPI(
      ApiConstant.getCartSubPath.path,
      ApiConstant.getCartSubPath.method,
      queryParam,
      null,
    )
      .then(items => {
        resolve(items);
      })
      .catch(error => {
        // FirebaseCrashlytics.sendCrashlogEvent(error.message.toString());
        reject(error);
      });
  });
};

const initiateAddToCartApi = async (queryParams: any, body: any) => {
  let addCartRequest: AddCartRequest = {
    items: [body],
  };
  return new Promise((resolve, reject) => {
    fetchDataFromAPI(
      /* DataLoaderMap['addItems'] ||*/ ApiConstant.initiateAddToCartSubPath.path,
      ApiConstant.initiateAddToCartSubPath.method,
      queryParams,
      addCartRequest,
    )
      .then(items => {
        //console.log('check add items', items);
        resolve(items);
      })
      .catch(error => {
        reject(error);
      });
  });
};

const initiateAddMultiProductToCartApi = async (
  queryParams: any,
  addCartRequest: any,
) => {
  // let addCartRequest: AddCartRequest = {
  //   items: [body],
  // };

  return new Promise((resolve, reject) => {
    fetchDataFromAPI(
      ApiConstant.initiateAddToCartSubPath.path,
      ApiConstant.initiateAddToCartSubPath.method,
      queryParams,
      addCartRequest,
    )
      .then(items => {
        resolve(items);
      })
      .catch(error => {
        // FirebaseCrashlytics.sendCrashlogEvent(error.message.toString());
        reject(error);
      });
  });
};

const updateCartApi = async (queryParams: any, body: any) => {
  return new Promise((resolve, reject) => {
    fetchDataFromAPI(
      /* DataLoaderMap['updateCart'] ||*/ ApiConstant.updateCartSubPath.path,
      ApiConstant.updateCartSubPath.method,
      queryParams,
      body,
      true,
    )
      .then(items => {
        resolve(items);
      })
      .catch(error => {
        reject(error);
      });
  });
};

const getSaveForLaterProductsApi = async () => {
  return new Promise((resolve, reject) => {
    fetchDataFromAPI(
      ApiConstant.getSaveForLaterProductsSubPath.path,
      ApiConstant.getSaveForLaterProductsSubPath.method,
    )
      .then(items => {
        resolve(items);
      })
      .catch(error => {
        // FirebaseCrashlytics.sendCrashlogEvent(error.message.toString());
        reject(error);
      });
  });
};

const saveProductItemForLaterApi = async (queryParams: any, body: any) => {
  return new Promise((resolve, reject) => {
    fetchDataFromAPI(
      ApiConstant.saveProductItemForLaterSubPath.path,
      ApiConstant.saveProductItemForLaterSubPath.method,
      null,
      body,
      true,
    )
      .then(items => {
        resolve(items);
      })
      .catch(error => {
        // FirebaseCrashlytics.sendCrashlogEvent(error.message.toString());
        reject(error);
      });
  });
};

const deleteSaveForLaterItemApi = async (itemCode: string | null) => {
  if (itemCode === null) {
    throw new Error('Item code cannot be null.');
  }

  return new Promise((resolve, reject) => {
    fetchDataFromAPI(
      ApiConstant.deleteSaveForLaterItemSubPath.path.replace(
        '{item_code}',
        itemCode,
      ),
      ApiConstant.deleteSaveForLaterItemSubPath.method,
    )
      .then(items => {
        resolve(items);
      })
      .catch(error => {
        // FirebaseCrashlytics.sendCrashlogEvent(error.message.toString());
        reject(error);
      });
  });
};

const getProductSizesBySlugApi = async (
  slug: string | null,
  queryParam: any,
) => {
  if (slug === null) {
    throw new Error('slug code cannot be null.');
  }

  return new Promise((resolve, reject) => {
    fetchDataFromAPI(
      ApiConstant.getProductSizesBySlugSubPath.path.replace('{slug}', slug),
      ApiConstant.getProductSizesBySlugSubPath.method,
      queryParam,
    )
      .then(items => {
        resolve(items);
      })
      .catch(error => {
        // FirebaseCrashlytics.sendCrashlogEvent(error.message.toString());
        reject(error);
      });
  });
};

const getProductPriceBySlugApi = async (
  slug: string,
  size: string,
  queryParam: any,
) => {
  const url = ApiConstant.getPriceSubPath.path
    .replace('{slug}', slug)
    .replace('{size}', size);
  return new Promise((resolve, reject) => {
    fetchDataFromAPI(
      url,
      ApiConstant.getProductSizesBySlugSubPath.method,
      queryParam,
      null,
    )
      .then(items => {
        resolve(items);
      })
      .catch(error => {
        // FirebaseCrashlytics.sendCrashlogEvent(error.message.toString());
        reject(error);
      });
  });
};

const getCouponsApi = async (queryParams: any, body: any) => {
  return new Promise((resolve, reject) => {
    fetchDataFromAPI(
      ApiConstant.getCouponsSubPath.path,
      ApiConstant.getCouponsSubPath.method,
      queryParams,
      body,
    )
      .then(items => {
        resolve(items);
      })
      .catch(error => {
        // FirebaseCrashlytics.sendCrashlogEvent(error.message.toString());
        reject(error);
      });
  });
};

const applyCouponApi = async (queryParams: any, body: any) => {
  return new Promise((resolve, reject) => {
    fetchDataFromAPI(
      ApiConstant.applyCouponSubPath.path,
      ApiConstant.applyCouponSubPath.method,
      queryParams,
      body,
    )
      .then(items => {
        resolve(items);
      })
      .catch(error => {
        // FirebaseCrashlytics.sendCrashlogEvent(error.message.toString());
        reject(error);
      });
  });
};

const removeCouponApi = async (queryParams: any, body: any) => {
  return new Promise((resolve, reject) => {
    fetchDataFromAPI(
      ApiConstant.removeCouponSubPath.path,
      ApiConstant.removeCouponSubPath.method,
      queryParams,
      body,
    )
      .then(items => {
        resolve(items);
      })
      .catch(error => {
        // FirebaseCrashlytics.sendCrashlogEvent(error.message.toString());
        reject(error);
      });
  });
};

export {
  getSaveForLaterProductsApi,
  saveProductItemForLaterApi,
  deleteSaveForLaterItemApi,
  fetchCartDetail,
  initiateAddToCartApi,
  updateCartApi,
  getProductSizesBySlugApi,
  getProductPriceBySlugApi,
  getCouponsApi,
  applyCouponApi,
  removeCouponApi,
  initiateAddMultiProductToCartApi,
};
