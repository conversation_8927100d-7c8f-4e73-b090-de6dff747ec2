export interface JMAddressModel {
  id?: string | number;
  name?: string;
  phone?: string;
  address_type?: string;
  address?: string;
  flat_or_house_no?: string;
  floor_no?: string;
  tower_no?: string;
  area?: string;
  landmark?: string;
  city: string;
  state: string;
  pin: string;
  lat?: number;
  lon?: number;
  input_mode?: string;
  is_default_address?: boolean;
  created_time?: string | null;
  updated_time?: string | null;
  country_iso_code?: string;
  country?: string;
}
