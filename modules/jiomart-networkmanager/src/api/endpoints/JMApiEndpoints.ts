export const enum JMJCPAddressEndpointKeys {
  ADDRESS_LIST = '/api/service/application/cart/v1.0/address',
  REMOVE_ADDRESS = '/api/service/application/cart/v1.0/address/{address_id}',
  DEFAULT_ADDRESS = '/api/service/application/cart/v1.0/address/{address_id}',
  EDIT_ADDRESS = '/api/service/application/cart/v1.0/address/{address_id}',
  NEW_ADDRESS = '/api/service/application/cart/v1.0/address',
  GET_PINCODE = '/api/service/application/logistics/v1.0/pincode/{pincode}',
}

export const enum JMBAUAddressEndpointKeys {
  ADDRESS_LIST = '/api/service/application/cart/v1.0/address',
  REMOVE_ADDRESS = '/api/service/application/cart/v1.0/address/{address_id}',
  DEFAULT_ADDRESS = '/api/service/application/cart/v1.0/address/{address_id}',
  EDIT_ADDRESS = '/api/service/application/cart/v1.0/address/{address_id}',
  NEW_ADDRESS = '/api/service/application/cart/v1.0/address',
  GET_PINCODE = '/api/service/application/logistics/v1.0/pincode/{pincode}',
}

export const enum JMJCPCartEndpointKeys {
  CART_LIST = '/ext/jmshipmentfee/cart/v1.0/get_cart',
  INITIALIZE_CART = '/ext/jmshipmentfee/cart/v1.0/add_items',
  UPDATE_CART = '/ext/jmshipmentfee/cart/v1.0/update_cart',
}

export const enum JMBAUCartEndpointKeys {
  CART_LIST = '/mst/rest/v1/cart/get',
  CREATE_CART = '/mst/rest/v1/cart/create',
  ADD_TO_CART = '/mst/rest/v1/cart/add_item',
  REMOVE_FROM_CART = '/mst/rest/v1/cart/remove_item',
}

export const enum JMJCPAllCategoriesEndpointKeys {
  GET_DEPARTMENT = '/api/service/application/catalog/v1.0/departments/',
  GET_ALL_CATEGORIES = '/ext/algolia/application/api/v1.0/products',
}

export const enum JMBAUAllCategoriesEndpointKeys {
  GET_ALL_CATEGORIES = '/catalog/tree_json_plp/algolia',
}
export const enum JMJCPSearchEndpointKeys {
  RECOMMENDED_ITEMS = '/ext/mylist/rra',
  DISCOVER_MORE_ITEMS = '/ext/algolia/application/api/v1.0/discover-more',
  SEARCH_RESULTS = '/ext/algolia/application/api/v1.0/auto-complete',
  RECOMMENDED_ITEMS_SERVICIBILITY = '/ext/algolia/application/api/v1.0/deliverable/products/',
}

export const enum JMJCPProductEndpointKeys {
  PRODUCT_PRICE = '/api/service/application/catalog/v1.0/products/sizes/price',
  PRODUCT_SIZE = '/api/service/application/catalog/v1.0/products/{slug}/sizes',
  PRODUCT_LIST = '/api/service/application/catalog/v1.0/collections/{slug}/items',
  PRODUCT_SEARCH_LIST = '/ext/algolia/application/api/v1.0/products',
}
export const enum JMBAUProductEndpointKeys {
  PRODUCT_PRICE = '/api/service/application/catalog/v1.0/products/sizes/price',
}

export const enum JMJCPDataLoaderEndpointKeys {
  DATA_LOADER = '/api/service/application/content/v1.0/data-loader',
}

export const enum JMJCPWishlistEndpointKeys {
  WISHLIST = '/api/service/application/catalog/v1.0/follow/{collection_type}/',
  ADD_TO_WISHLIST = '/api/service/application/catalog/v1.0/follow/{collection_type}/{collection_id}/',
  REMOVE_FROM_WISHLIST = '/api/service/application/catalog/v1.0/follow/{collection_type}/{collection_id}/',
}

export const enum JMBAUWishlistEndpointKeys {}

export type JMApiEndpointKeys =
  | JMJCPAddressEndpointKeys
  | JMBAUAddressEndpointKeys
  | JMJCPAllCategoriesEndpointKeys
  | JMBAUAllCategoriesEndpointKeys
  | JMJCPCartEndpointKeys
  | JMBAUCartEndpointKeys
  | JMJCPSearchEndpointKeys
  | JMJCPProductEndpointKeys
  | JMBAUProductEndpointKeys
  | JMJCPDataLoaderEndpointKeys
  | JMJCPWishlistEndpointKeys
  | JMBAUWishlistEndpointKeys;
