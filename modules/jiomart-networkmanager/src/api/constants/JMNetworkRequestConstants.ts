export const JMNetworkRequestConstants = {
  DEFAULT_TIME_OUT: 60000,
  MAX_RETRIES: 3,
  DEVICE_TYPE: '10',
  VERSION_NUMBER: '1400',
  TTL_EXPIRATION_THRESHOLD: 130000, // 3 mins
};

export const enum JMLocalStorageConstants {
  ACCESS_TOKEN = 'YWNjZXNzX3Rva2Vu',
  BIFROST_ACCESS_TOKEN = 'bf=YWNjZXNzX3Rva2Vu',
  USER_INFO = 'dXNlcl9pbmZv',
  CURRENT_USER_INFO = 'Y3VycmVudF91c2VyX2luZm8=',
  LINKED_BY_ME = 'linked_by_me',
  DEVICE_INFO = 'ZGV2aWNlX2luZm8=',
  CORPORATE_SERVICES = 'bf=ZGV2aWNlX2luZm8=',
  APP_LANGUAGE = 'YXBwX2xhbmd1YWdl',
  JCP_ACCESS_TOKEN = 'jcpAccessToken',
  BAU_ACCESS_TOKEN = 'bauAccessToken',
}

export enum NetworkResponse {
  SUCCESS = 'success',
  ERROR = 'error',
}

export const enum APIConstant {
  BAU_SIT_HOST_URL = 'https://jiomart-sit.jio.com',
  BAU_PROD_HOST_URL = 'https://www.jiomart.com',
  BAU_UAT_HOST_URL = 'https://www.jiomart.com',

  JCP_SIT_HOST_URL = 'https://jiomart.sit.jiomartjcp.com',
  JCP_PROD_HOST_URL = 'https://jiomart.rep.jiomartjcp.com',
  JCP_UAT_HOST_URL = 'https://jiomart.rep.jiomartjcp.com',
}
