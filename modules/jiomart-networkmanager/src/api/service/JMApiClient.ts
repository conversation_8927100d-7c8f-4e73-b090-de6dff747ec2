import axios, {
  AxiosInstance,
  AxiosResponse,
  type InternalAxiosRequestConfig,
} from 'axios';
import {handleApiError} from '../utils/JMErrorHandler';
import JMRequestHelper from '../helpers/JMRequestHelper';
import {JMError, JMErrorCodes, JMErrorMessages} from '../utils/JMErrorCodes';
import {JMNetworkRequestConstants} from '../constants/JMNetworkRequestConstants';
import {JMApiLogger} from '../../models/JMApiLogger';
import networkService from '../../../../jiomart-common/src/JMNetworkConnectionUtility';
import axiosToCurl from '../utils/curl';

class JMApiClient {
  private _instance: AxiosInstance;

  constructor() {
    this._instance = axios.create();
    this.setupInterceptors();
  }

  private setupInterceptors() {
    this._instance.interceptors.request.use(
      async config => {
        this.getCurl(config);
        // JMApiLogger.logRequest(config);
        return config;
      },
      error => {
        JMApiLogger.logError(error);
        return Promise.reject(handleApiError(error));
      },
    );

    this._instance.interceptors.response.use(
      (response: AxiosResponse<any>) => {
        // JMApiLogger.logResponse(response.data);
        return response.data;
      },
      error => {
        JMApiLogger.logError(error);
        return Promise.reject(handleApiError(error));
      },
    );
  }

  private getCurl(request: InternalAxiosRequestConfig) {
    try {
      const curlCommand = axiosToCurl(request);
      console.log('------------ CURL ------------');
      console.log(curlCommand);
      console.log('------------ END CURL ------------');
    } catch (error) {}
  }

  public async request<T, M>(
    request: JMRequestHelper<M>,
    retryCount: number = 0,
  ): Promise<T> {
    try {
      if (!networkService.getConnectionStatus()) {
        throw {
          code: JMErrorCodes.NETWORK_ERROR,
          message: JMErrorMessages.NETWORK_ERROR,
        } as JMError;
      }
      await request.updateRequestDetails();
      return await this._instance.request(request.build());
    } catch (error: any) {
      if (retryCount < JMNetworkRequestConstants.MAX_RETRIES) {
        return this.request(request, retryCount + 1);
      }
      throw error;
    }
  }
}

export default JMApiClient;
