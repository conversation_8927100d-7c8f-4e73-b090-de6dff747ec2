import {AxiosRequestConfig} from 'axios';
import {JMRequestConfig, JMDefaultHeaders} from './JMRequestConfig';
import {JMNetworkRequestConstants} from '../constants/JMNetworkRequestConstants';
import {isNullOrUndefinedOrEmpty} from '../../../../jiomart-common/src/JMObjectUtility';
import {
  getApplicationId,
  getApplicationToken,
  getEnvironmentConfig,
} from '../../JMEnvironmentConfig';
import {sign} from '@gofynd/fp-signature';
import useAddress from '@jm/jiomart-address/src/hooks/useAddress';
import CryptoJS from 'crypto-js';
import JMCookieManager from './JMCookieManager';

// eslint-disable-next-line react-hooks/rules-of-hooks
const address = useAddress();

export default class JMRequestHelper<T> {
  private config: AxiosRequestConfig<T> = {};
  private _request: JMRequestConfig<T>;
  constructor(request: JMRequestConfig<T>) {
    this._request = request;
  }

  private async setupConfig() {
    this.config = {...this._request};
    this.config.url = this.getCompleteUrl();
    this.config.method = this.getHttpMethod();
    this.config.withCredentials = true;
    this.config.headers = await this.getHeaders();
    this.config.data = this.getBodyParams();
    this.config.timeout = this.getTimeout();
    this.config.params = this._request.params;
  }

  async updateRequestDetails() {
    await this.setupConfig();
    return this;
  }

  build(): AxiosRequestConfig<T> {
    if (!this.config.url || !this.config.method) {
      throw new Error(
        `Missing required fields (url => ${this.config.url}, method => ${this.config.method})`,
      );
    }
    return this.config;
  }

  private getBaseUrl() {
    const environmentConfigs = getEnvironmentConfig();
    if (environmentConfigs && environmentConfigs['JCP']) {
      return environmentConfigs[this._request.baseUrl];
    } else {
      console.error(
        `Base URL not found for Environment: ${environmentConfigs} and Service: ${this._request.baseUrl}`,
      );
      return undefined;
    }
  }

  private getEndPoint() {
    return this._request.endpoint;
  }

  private getCompleteUrl() {
    const baseUrl = this.getBaseUrl();
    if (!isNullOrUndefinedOrEmpty(baseUrl)) {
      return `${baseUrl}${this._request.endpoint}`;
    }
  }

  private getHttpMethod() {
    return this._request.method;
  }

  public async getHeaders() {
    let defaultHeaders = JMDefaultHeaders();

    const xLocationDetail = await this.getXLocationDetail();
    if (!isNullOrUndefinedOrEmpty(xLocationDetail)) {
      defaultHeaders = {...defaultHeaders, ...xLocationDetail};
    }

    const authorization = this.getAuthoriziation();
    if (!isNullOrUndefinedOrEmpty(authorization)) {
      defaultHeaders = {...defaultHeaders, ...authorization};
    }

    const cookie = await this.getCookie();
    defaultHeaders = {...defaultHeaders, ...cookie};

    const signedHeaders = this.setSignedFyndSignature();
    defaultHeaders = {...defaultHeaders, ...signedHeaders};

    return {
      ...defaultHeaders,
      ...this._request.headers,
    };
  }

  private parseBaseURL(baseURL: string) {
    const pattern = /^(https?):\/\/([^/:]+)(:\d+)?(\/.*)?$/;
    const match = baseURL.match(pattern);

    if (!match) {
      throw new Error('Invalid URL');
    }

    return {
      protocol: match[1], // e.g. 'https'
      hostname: match[2], // e.g. 'api.example.com'
      port: match[3] ? match[3].slice(1) : '', // e.g. '3000'
      path: match[4] || '/', // e.g. '/v1' or '/'
      host: match[2] + (match[3] || ''), // e.g. 'api.example.com:3000'
    };
  }

  private setSignedFyndSignature = () => {
    try {
      const method = this.getHttpMethod();
      const path = this.getEndPoint();
      const url = this.parseBaseURL(this.getCompleteUrl() ?? '');
      const body = this.getBodyParams();

      const requestToSign: any = {
        method: method,
        host: url.host,
        path: path,
        headers: this.config.headers,
        body: body,
      };

      const signature = sign(requestToSign);

      return {
        'X-Fp-Signature': signature['x-fp-signature'],
        'X-Fp-Date': signature['x-fp-date'],
      };
    } catch (error) {}
  };

  private async getXLocationDetail() {
    let data: any = await address.getDefaultAddress();
    if (!isNullOrUndefinedOrEmpty(data)) {
      const parseData = JSON.parse(data);
      const xlocation = {
        pincode: parseData?.pin,
        city: parseData?.city,
        state: parseData?.state,
        country_iso_code: parseData?.country_iso_code,
        country: parseData?.country,
      };
      return {
        'X-Location-Detail': JSON.stringify(xlocation),
      };
    }
    return null;
  }

  private async getCookie() {
    return JMCookieManager.getCookieHeader(['f.session'], this.getBaseUrl());
  }
  private getAuthoriziation() {
    const appId = getApplicationId();
    const appToken = getApplicationToken();
    const bearerToken = this.generateBearerToken(appId, appToken);
    return {
      Authorization: bearerToken,
    };
  }

  private generateBearerToken(appId: string, appToken: string) {
    const data = `${appId}:${appToken}`;
    const base64 = CryptoJS.enc.Base64.stringify(CryptoJS.enc.Utf8.parse(data));
    return `Bearer ${base64}`;
  }

  private getBodyParams() {
    return this._request.data;
  }

  private getTimeout() {
    switch (this._request.endpoint) {
      default:
        return JMNetworkRequestConstants.DEFAULT_TIME_OUT;
    }
  }
}
