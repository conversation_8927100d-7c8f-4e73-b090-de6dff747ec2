import {JMApiEndpointKeys} from '../endpoints/JMApiEndpoints';
import {JMBaseUrlKeys} from '../../JMEnvironmentConfig';
import {type AxiosRequestConfig} from 'axios';

export enum JMHttpMethods {
  GET = 'GET',
  POST = 'POST',
  PUT = 'PUT',
  PATCH = 'PATCH',
  DELETE = 'DELETE',
}

export enum JMHeaderContentType {
  ACEEPT = 'application/json, text/plain, */*',
  JSON = 'application/json',
  FORM_ENCODED = 'application/x-www-form-urlencoded',
  MULTIPART_FORM_DATA = 'multipart/form-data',
}

export const JMDefaultHeaders = (): {} => {
  return {
    Accept: JMHeaderContentType.ACEEPT,
    'Content-Type': JMHeaderContentType.JSON,
    'X-Currency-Code': 'INR',
    'Cache-Control': 'no-cache',
    'x-company-id': 2,
    'Access-Control-Allow-Credentials': true,
  };
};

// export const JMDefaultBody = {
//   device_type: Platform.OS === PlatformType.ANDROID ? 2 : 3,
//   version_number: getNumberAppVersion(),
//   language: 'eng',
// };

// export const JMDeviceInfo = async () => {
//   return {
//     device_model: DeviceInfo.getModel(),
//     device_name: await DeviceInfo.getDeviceName(),
//     device_os: DeviceInfo.getSystemVersion(),
//     device_type: Platform.OS === PlatformType.ANDROID ? 2 : 3,
//     unique_id: await DeviceInfo.getUniqueId(),
//     sync_id: !isNullOrUndefinedOrEmpty(JMSharedViewModel.Instance.pushToken)
//       ? JMSharedViewModel.Instance.pushToken
//       : '3C705F63-F6DA-41AB-B65F-055C2842B434-30481-0000310A58AD4E91',
//   };
// };

// export const JMAuthParams = async () => {
//   const user = await JMDatabaseManager.getUser();
//   const primaryUser = await JMDatabaseManager.getUser();
//   const device = await JMDatabaseManager.getDevice();
//   if (!isNullOrUndefinedOrEmpty(user)) {
//     let params = {
//       device_auth_key: user?.auth_key ?? '',
//       device_jio_id: user?.jio_id ?? '',
//       device_user_id: user?.id ?? '',
//       primary_user_id: primaryUser?.id ?? '', //TODO: switch profile handling required
//     };
//     if (!isNullOrUndefinedOrEmpty(device)) {
//       params = {...params, ...{device_id: device?.id}};
//     }
//     return params;
//   }
//   return {};
// };

export interface JMRequestConfig<T = any> extends AxiosRequestConfig {
  baseUrl: JMBaseUrlKeys;
  endpoint: JMApiEndpointKeys | string;
  data?: T;
}

export class JMRequestConfigObject {
  static requestConfig(
    baseUrl: JMBaseUrlKeys,
    endpoint: JMApiEndpointKeys | string,
    method: JMHttpMethods,
    data?: any | null,
    params?: any | null,
    headers?: any | null,
  ): JMRequestConfig {
    return {
      baseUrl,
      endpoint,
      method,
      data,
      params,
      headers,
    };
  }
}
