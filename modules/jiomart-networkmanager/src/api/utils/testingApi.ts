import {J<PERSON>og<PERSON>} from '../../../../jiomart-common/src/utils/JMLogger';
import useJMLoginNetworkState from '../../JMNetworkController/JHLoginApiNetworkController';
import useJMAuthNetworkState from '../../JMNetworkController/JHAuthNetworkController';
import {JMStorageService} from './JMStorageService';
import {JMTokenKeyFetcher} from '../helpers/JMTokenKeyFetcher';
import {isNullOrUndefinedOrEmpty} from '../../../../jiomart-common/src/JMObjectUtility';
import JMRequestHelper from '../helpers/JMRequestHelper';
import {JMRequestConfigObject} from '../helpers/JMRequestConfig';
import {JMBifrostEndpointKeys} from '../endpoints/JMApiEndpoints';
import {JMApiClient} from '../service/JMApiClient';
import {
  jsonParse,
  parseApiResponse,
} from '../../../../jiomart-common/src/models/JResponseParser';
import {BifrostApiResponse} from '../../models/JMResponseModels';
import {JMDatabaseManager} from '../../db/JMDatabaseManager';
import {encryptedDataWithAuthKey} from './JMEncryptionUtility';
import {JMTokenValidity} from './JMTokenValidity';
import {JMBaseUrlKeys} from '../../JMEnvironmentConfig';

const testingApiState = () => {
  const apiClient = new JMApiClient();

  const {fetchLegacyAccessToken, fetchBifrostLegacyAccessToken} =
    useJMAuthNetworkState();
  const {sendOtpApi, verifyOtpApi, registerDeviceApi} =
    useJMLoginNetworkState();

  const testingApi = async () => {
    await testingBifrostApi();
    // await testingLegacyApi();
    // await fetchCorporateServicesFromStroage();
  };

  const testingLegacyApi = async () => {
    await JMStorageService.clearAllKeys();
    const tokenData = await JMStorageService.getAccessToken(
      JMTokenKeyFetcher.getKeyForAccessToken(),
    );
    if (!isNullOrUndefinedOrEmpty(tokenData)) {
      await registerDeviceApi();
      return;
    }
    await fetchLegacyAccessToken();
    try {
      const response = await sendOtpApi({
        mobile: '9999900007',
        country_code: '+91',
      });
      await verifyOtpApi({
        country_code: '+91',
        mobile_no: 9999900007,
        otp: 900007,
        otp_id: response?.contents ?? '',
      });
      console.log('1234 => ', await encryptedDataWithAuthKey('1234'));
    } catch (error) {
      JMLogger.log(error, 'sendOtp in testingApiState');
    }
  };

  const testingBifrostApi = async () => {
    // await JMStorageService.clearAllKeys();
    try {
      const tokenValidity = await JMTokenValidity.isTokenValid();
      const tokenData = tokenValidity
        ? await JMStorageService.getAccessToken(
            JMTokenKeyFetcher.getKeyForAccessToken(JMBaseUrlKeys.BIFROST),
          )
        : await fetchBifrostLegacyAccessToken();
      if (!isNullOrUndefinedOrEmpty(tokenData)) {
        await getCorporateServiceApi();
        return;
      }
      await getCorporateServiceApi();
    } catch (error) {
      JMLogger.log(error, 'in testingApiState => ');
    }
  };

  const getCorporateServiceApi = async (): Promise<any> => {
    try {
      let requestHelper = new JMRequestHelper(
        JMRequestConfigObject.requestConfig(
          JMBaseUrlKeys.BIFROST,
          JMBifrostEndpointKeys.CORPORATE_SERVICES,
          {authId: ['Jio', 'ril']},
          null,
        ),
      );
      const response = await apiClient.request<string>(requestHelper);
      const parsed = await parseApiResponse(response, JMBaseUrlKeys.BIFROST);
      const apiResponse = jsonParse<BifrostApiResponse>(parsed as string);
      await JMDatabaseManager.saveCorporateServices(apiResponse);
      return response;
    } catch (error) {
      throw error;
    }
  };
  const fetchCorporateServicesFromStroage = async () => {
    const response = await JMDatabaseManager.getCorporateServices();
    console.log({response});
  };
  return {
    testingApi,
  };
};

export default testingApiState;
