import {JMBAUWishlistEndpointKeys} from '../api/endpoints/JMApiEndpoints';
import {
  JMHttpMethods,
  JMRequestConfigObject,
} from '../api/helpers/JMRequestConfig';
import JMRequestHelper from '../api/helpers/JMRequestHelper';
import J<PERSON>piClient from '../api/service/JMApiClient';
import {JMBaseUrlKeys} from '../JMEnvironmentConfig';
import JMBaseWishlistNetworkController from '../base/JMBaseWishlistNetworkController';

class JMBAUWishlistNetworkController extends JMBaseWishlistNetworkController {
  private apiClient: JMApiClient;
  private baseUrl: JMBaseUrlKeys;
  constructor() {
    super();
    this.apiClient = new JMApiClient();
    this.baseUrl = JMBaseUrlKeys.BAU;
  }

  protected fetchWishlist = async (request: any): Promise<any> => {
    try {
      const {collection} = request;
      let requestHelper = new JMRequestHelper(
        JMRequestConfigObject.requestConfig(
          this.baseUrl,
          JMBAUWishlistEndpointKeys.WISHLIST?.replace(
            '{collection_type}',
            collection,
          ),
          JMHttpMethods.GET,
        ),
      );
      return await this.apiClient.request(requestHelper);
    } catch (error) {
      throw error;
    }
  };

  protected addToWishlist = async (request: any): Promise<any> => {
    try {
      const {collection, item_id} = request;

      let requestHelper = new JMRequestHelper(
        JMRequestConfigObject.requestConfig(
          this.baseUrl,
          JMBAUWishlistEndpointKeys.ADD_TO_WISHLIST?.replace(
            '{collection_type}',
            collection,
          )?.replace('{collection_id}', item_id),
          JMHttpMethods.POST,
        ),
      );
      return await this.apiClient.request(requestHelper);
    } catch (error) {
      throw error;
    }
  };

  protected removeFromWishlist = async (request: any): Promise<any> => {
    try {
      const {collection, item_id} = request;

      let requestHelper = new JMRequestHelper(
        JMRequestConfigObject.requestConfig(
          this.baseUrl,
          JMBAUWishlistEndpointKeys.REMOVE_FROM_WISHLIST?.replace(
            '{collection_type}',
            collection,
          )?.replace('{collection_id}', item_id),
          JMHttpMethods.DELETE,
        ),
      );
      return await this.apiClient.request(requestHelper);
    } catch (error) {
      throw error;
    }
  };
}

export default JMBAUWishlistNetworkController;
