import {JMBAUProductEndpointKeys} from '../api/endpoints/JMApiEndpoints';
import {
  JMHttpMethods,
  JMRequestConfigObject,
} from '../api/helpers/JMRequestConfig';
import JMRequestHelper from '../api/helpers/JMRequestHelper';
import JMApiClient from '../api/service/JMApiClient';
import {JMBaseUrlKeys} from '../JMEnvironmentConfig';
import JMBaseProductNetworkController from '../base/JMBaseProductNetworkController';
import DataLoaderMap from '../DataLoaderMap';

class JMBAUPRoductNetworkController extends JMBaseProductNetworkController {
  private apiClient: JMApiClient;
  private baseUrl: JMBaseUrlKeys;
  constructor() {
    super();
    this.apiClient = new JMApiClient();
    this.baseUrl = JMBaseUrlKeys.BAU;
  }

  protected fetchProductSize = async (request: any) => {
      try {
        const {slug} = request;
        let requestHelper = new JMRequestHelper(
          JMRequestConfigObject.requestConfig(
            this.baseUrl,
            (
              DataLoaderMap?.getCollectionItemsBySlug ||
              JMBAUPRoductNetworkController.PRODUCT_SIZE
            )?.replace('{slug}', slug),
            JMHttpMethods.GET,
          ),
        );
        return await this.apiClient.request(requestHelper);
      } catch (error) {
        throw error;
      }
    };

  public fetchProductPrice = async (request: any): Promise<any> => {
    try {
      const {slug, size} = request;
      const body = {
        items: [{slug, size}],
      };
      let requestHelper = new JMRequestHelper(
        JMRequestConfigObject.requestConfig(
          this.baseUrl,
          JMBAUProductEndpointKeys.PRODUCT_PRICE,
          JMHttpMethods.POST,
          body,
        ),
      );
      return await this.apiClient.request(requestHelper);
    } catch (error) {
      throw error;
    }
  };

  protected fetchProductList(request: any): Promise<any> {
    try {
    } catch (error) {
      throw error;
    }
  }
  protected fetchSearchProductList(request: any): Promise<any> {
    try {
    } catch (error) {
      throw error;
    }
  }
}

export default JMBAUPRoductNetworkController;
