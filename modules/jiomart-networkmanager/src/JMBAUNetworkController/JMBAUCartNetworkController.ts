import {readDBCart, updateDBCart} from '@jm/jiomart-cart/src/utils';
import {JMBAUCartEndpointKeys} from '../api/endpoints/JMApiEndpoints';
import {
  JMHttpMethods,
  JMRequestConfigObject,
} from '../api/helpers/JMRequestConfig';
import JMRequestHelper from '../api/helpers/JMRequestHelper';
import JMApiClient from '../api/service/JMApiClient';
import JMBaseCartNetworkController from '../base/JMBaseCartNetworkController';
import {JMBaseUrlKeys} from '../JMEnvironmentConfig';
import type {
  JMBAUCartModel,
  JMBAUCreateCart,
} from '../models/Cart/JMBAUCartModel';
import {AxiosError} from 'axios';

class JMBAUCartNetworkController extends JMBaseCartNetworkController {
  private apiClient: JMApiClient;
  private baseUrl: JMBaseUrlKeys;
  constructor() {
    super();
    this.apiClient = new JMApiClient();
    this.baseUrl = JMBaseUrlKeys.BAU;
  }

  protected fetchCart = async (): Promise<any> => {
    try {
      const cart = await readDBCart();

      if (!cart && !cart?.cart_id) {
        return await this.createCart();
      }
      const n = cart?.cart_id;
      const params = {n};

      let requestHelper = new JMRequestHelper(
        JMRequestConfigObject.requestConfig(
          this.baseUrl,
          JMBAUCartEndpointKeys.CART_LIST,
          JMHttpMethods.GET,
          undefined,
          params,
        ),
      );
      return await this.apiClient.request<JMBAUCartModel, any>(requestHelper);
    } catch (error) {
      throw error;
    }
  };

  private createCart = async (): Promise<any> => {
    try {
      let requestHelper = new JMRequestHelper(
        JMRequestConfigObject.requestConfig(
          this.baseUrl,
          JMBAUCartEndpointKeys.CREATE_CART,
          JMHttpMethods.GET,
        ),
      );
      const response = await this.apiClient.request<any, JMBAUCreateCart>(
        requestHelper,
      );

      if (response?.status === 'success') {
        await updateDBCart({cart_id: response?.result.cart_id});
        return await this.fetchCart();
      } else {
        throw new AxiosError('Create Cart Api Failed', '500');
      }
    } catch (error) {
      throw error;
    }
  };

  protected addToCart = async (request: any): Promise<any> => {
    try {
      const {store_code} = request;
      let requestHelper = new JMRequestHelper(
        JMRequestConfigObject.requestConfig(
          this.baseUrl,
          JMBAUCartEndpointKeys.ADD_TO_CART,
          JMHttpMethods.GET,
          undefined,
          request,
          {Storecode: store_code},
        ),
      );
      return await this.apiClient.request(requestHelper);
    } catch (error) {
      throw error;
    }
  };

  protected removeFromCart = async (request: any): Promise<any> => {
    try {
      let requestHelper = new JMRequestHelper(
        JMRequestConfigObject.requestConfig(
          this.baseUrl,
          JMBAUCartEndpointKeys.REMOVE_FROM_CART,
          JMHttpMethods.GET,
          undefined,
          request,
        ),
      );
      return await this.apiClient.request(requestHelper);
    } catch (error) {
      throw error;
    }
  };
}

export default JMBAUCartNetworkController;
