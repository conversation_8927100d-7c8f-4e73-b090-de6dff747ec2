import {JMBAUAddressEndpointKeys} from '../api/endpoints/JMApiEndpoints';
import {
  JMHttpMethods,
  JMRequestConfigObject,
} from '../api/helpers/JMRequestConfig';
import JMRequestHelper from '../api/helpers/JMRequestHelper';
import J<PERSON><PERSON>Client from '../api/service/JMApiClient';
import JMBaseAddressNetworkController from '../base/JMBaseAddressNetworkController';
import {JMBaseUrlKeys} from '../JMEnvironmentConfig';

class JMBAUAddressNetworkController extends JMBaseAddressNetworkController {
  private apiClient: JMApiClient;
  private baseUrl: JMBaseUrlKeys;
  constructor() {
    super();
    this.apiClient = new JMApiClient();
    this.baseUrl = JMBaseUrlKeys.BAU;
  }

  protected fetchAddress = async (): Promise<any> => {
    try {
      // return JSON.stringify(DUMY_DATA);

      let requestHelper = new JMRequestHelper(
        JMRequestConfigObject.requestConfig(
          this.baseUrl,
          JMBAUAddressEndpointKeys.ADDRESS_LIST,
          JMHttpMethods.GET,
        ),
      );
      return await this.apiClient.request(requestHelper);
    } catch (error) {
      throw error;
    }
  };

  protected removeAddress = async (address_id: string): Promise<any> => {
    try {
      let requestHelper = new JMRequestHelper(
        JMRequestConfigObject.requestConfig(
          this.baseUrl,
          JMBAUAddressEndpointKeys.REMOVE_ADDRESS?.replace(
            '{address_id}',
            address_id,
          ),
          JMHttpMethods.DELETE,
        ),
      );
      return await this.apiClient.request(requestHelper);
    } catch (error) {
      throw error;
    }
  };

  protected defaultAddress = async (
    address_id: string,
    body: any,
  ): Promise<any> => {
    try {
      let requestHelper = new JMRequestHelper(
        JMRequestConfigObject.requestConfig(
          this.baseUrl,
          JMBAUAddressEndpointKeys.DEFAULT_ADDRESS?.replace(
            '{address_id}',
            address_id,
          ),
          JMHttpMethods.PUT,
          body,
        ),
      );
      return await this.apiClient.request(requestHelper);
    } catch (error) {
      throw error;
    }
  };
  protected editAddress = async (
    address_id: string,
    body: any,
  ): Promise<any> => {
    try {
      let requestHelper = new JMRequestHelper(
        JMRequestConfigObject.requestConfig(
          this.baseUrl,
          JMBAUAddressEndpointKeys.EDIT_ADDRESS?.replace(
            '{address_id}',
            address_id,
          ),
          JMHttpMethods.PUT,
          body,
        ),
      );
      return await this.apiClient.request(requestHelper);
    } catch (error) {
      throw error;
    }
  };

  protected insertAddress = async (body: any): Promise<any> => {
    try {
      let requestHelper = new JMRequestHelper(
        JMRequestConfigObject.requestConfig(
          this.baseUrl,
          JMBAUAddressEndpointKeys.NEW_ADDRESS,
          JMHttpMethods.POST,
          body,
        ),
      );
      return await this.apiClient.request(requestHelper);
    } catch (error) {
      throw error;
    }
  };

  protected fetchPincodeCity = async (pincode: string): Promise<any> => {
    try {
      let requestHelper = new JMRequestHelper(
        JMRequestConfigObject.requestConfig(
          this.baseUrl,
          JMBAUAddressEndpointKeys.GET_PINCODE?.replace('{pincode}', pincode),
          JMHttpMethods.GET,
          null,
        ),
      );
      return await this.apiClient.request(requestHelper);
    } catch (error) {
      throw error;
    }
  };
}

export default JMBAUAddressNetworkController;
