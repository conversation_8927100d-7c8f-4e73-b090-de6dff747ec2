{"searchConfig": [{"isVisibleForVersion": "1", "versionNumber": "3.1.0", "jiomart_header": {"header_type": 8}, "shopping_list_block": {"is_visible": true, "sorted_order": 1, "header_title": "Got a Shopping List?", "sub_title": "Search multiple products together."}, "searchSuggestion_Categories": {"header_title": "Categories", "is_visible": true, "maxItemsVisible": 10}, "searchSuggestion_Brands": {"header_title": "Brands", "is_visible": true, "maxItemsVisible": 10}, "searchSuggestion_List": {"is_visible": true, "maxItemsVisible": 4}, "searchSuggestion_product": {"default_image": "", "header_title": "Products", "is_visible": true, "maxItemsVisible": 10}, "recent_search": {"is_visible": true, "sorted_order": 2, "header_title": "Your recent searches", "visible_search_keyword_count": 10, "action_button": [{"is_visible": true, "button_title": "Clear All"}]}, "discover_more": {"is_visible": true, "sorted_order": 3, "header_title": "Try something new", "visible_search_keyword_count": 10}, "popular_category": {"is_visible": true, "sorted_order": 5, "header_title": "Most Trending Categories", "default_image": "", "data": []}, "recommended_product": {"is_visible": true, "sorted_order": 4, "header_title": "Recommended for you", "default_image": ""}, "btm_sheet": {"shopping_list_block": {"is_visible": true, "header_title": "Shopping List", "sub_title": "Search multiple products by entering your shopping list below", "text_input": {"is_visible": true, "label": "Enter your items", "placeholder": "e.g. Milk, Bread, Fruit", "sub_label": "Add comma as separator", "invalid_message": "Please enter text in correct Format*"}, "action_button": [{"is_visible": true, "button_title": "Clear"}, {"is_visible": true, "button_title": "Search All"}], "toast_messages": ["Please enter products to search"]}}}], "allCategoryConfig": [{"isVisibleForVersion": "1", "versionNumber": "3.1.0", "jiomart_header": {"header_type": 6, "title": "All Categories", "analytics": {"firebase": {"header_event": {"back": {"action": "back clicked", "label": "header_back_clicked", "header_page_type": "shop by category"}, "cart": {"action": "cart clicked", "label": "header_cart_clicked", "header_page_type": "shop by category", "screen": "all_category"}}}}}, "isPlpWeb": false, "l1_category": {"default_image": "", "data": ["groceries", "fashion", "premium-fruits", "home-kitchen", "electronics", "beauty", "jewellery", "home-improvement", "sports-toys-luggage", "furniture", "alcohol", "wellness", "books-music-stationery"]}, "l2_category": {"default_image": "", "data": {"groceries-old": [], "fashion-old": [], "premium-fruits-old": [], "home-kitchen-old": [], "electronics-old": [], "beauty-old": [], "jewellery-old": [], "home-improvement-old": [], "Sports-toys-old": [], "furniture-old": []}}, "l3_category": {"default_image": "", "data": []}, "topNavigation": ["groceries", "premium-fruits", "fashion", "electronics", "alcohol", "beauty", "jewellery", "home-improvement", "furniture", "sports-toys-luggage", "wellness", "books-music-stationery"]}], "permissionDataConfig": [{"isVisibleForVersion": "1", "versionNumber": "3.1.0", "is_visible": true, "title": "Allow access to JioMart", "subTitle": "Please provide us with the required permissions for a more personalised shopping experience.", "buttonText": "Proceed", "permissionsArray": [{"permissionsArrayTitle": "Allow location access", "permissionsArrayTitleSubtitle": "Stay updated with location-based offers and product availability.", "icon": "IcLocation"}, {"permissionsArrayTitle": "Allow to send notifications", "permissionsArrayTitleSubtitle": "Get notified about the latest offers, deals & new arrivals.", "icon": "IcNotification"}, {"permissionsArrayTitle": "Allow access to SMS", "permissionsArrayTitleSubtitle": "Track your orders better by enabling real time order status notifications.", "icon": "IcSms"}]}], "privacyPolicyConfig": [{"isVisibleForVersion": "1", "versionNumber": "3.1.0", "is_visible": true, "regularString": "Please note that our Privacy Policy has been updated and we have also added Retail Account Privacy Policy. By continuing to use our platform and its services, you consent to the updated policies.", "clickableString": "Privacy Policy,Retail Account Privacy Policy", "redirectionUrl": "privacy-policy,https://account.relianceretail.com/privacy-policy/", "openInBrowser": false, "buttonText": "Agree"}], "jiomartFlags": [{"versionNumber": "3.1.0", "isVisibleForVersion": "1", "is_visible": true, "isPlpWeb": false}], "app_update_details": [{"versionNumber": "3.1.0", "isVisibleForVersion": "1", "immediate_update": false, "visible_update_for": [], "show_popup_frequency": 4, "title": "Yay, a new update is here!", "message": "Now, experience a faster and smoother app with an improved design and new features.", "is_close_button_show": true, "button_text": "Update", "image_url": "https://jep-asset.akamaized.net/JioMart/Common/Rocket-Final.gif", "lottie": {"icon_url": "", "autoPlay": true, "speed": 1, "duration": 5000, "loop": true}}]}