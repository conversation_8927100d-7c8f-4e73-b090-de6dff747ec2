{"data": {"location": {"pincode": "400001", "city": "Mumbai", "name": "", "state": "Maharashtra", "country_iso_code": "IN", "country": "India"}, "privacyPolicyConfig": [{"isVisibleForVersion": "1", "versionNumber": "3.1.0", "is_visible": true, "regularString": "Please note that our Privacy Policy has been updated and we have also added Retail Account Privacy Policy. By continuing to use our platform and its services, you consent to the updated policies.", "clickableString": "Privacy Policy,Retail Account Privacy Policy", "redirectionUrl": "privacy-policy,https://account.relianceretail.com/privacy-policy/", "openInBrowser": false, "buttonText": "Agree"}], "permissionDataConfig": [{"isVisibleForVersion": "1", "versionNumber": "3.1.0", "is_visible": true, "title": "Allow access to JioMart", "subTitle": "Please provide us with the required permissions for a more personalised shopping experience.", "buttonText": "Proceed", "permissionsArray": [{"permissionsArrayTitle": "Allow location access", "permissionsArrayTitleSubtitle": "Stay updated with location-based offers and product availability.", "icon": "IcLocation"}, {"permissionsArrayTitle": "Allow to send notifications", "permissionsArrayTitleSubtitle": "Get notified about the latest offers, deals & new arrivals.", "icon": "IcNotification"}]}], "jiomartFlags": [{"isVisibleForVersion": "1", "versionNumber": "3.1.0", "is_visible": true, "isPlpWeb": false, "isAppsFlyerEventsEnabled": 1}]}}