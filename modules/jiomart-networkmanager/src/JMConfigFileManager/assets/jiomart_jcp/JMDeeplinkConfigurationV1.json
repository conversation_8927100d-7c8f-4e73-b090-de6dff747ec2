{"deeplinks": [{"navTitle": "Emotional Wellness", "headerVisibility": 0, "destination": "JHCommonWebView", "navigationType": "push", "source": "JHH-WEBAPP", "deeplinkIdentifier": "wellness", "bundle": "consult_type_id=2&filter_specialty_ids=76&nav_page=emotional-dashboard&isMentalWellness=true", "actionUrl": "https://healthhub.jio.com/avatar/authorise?source=JHH-WEBAPP&pnp=true", "actionType": "T005"}, {"navTitle": "PME", "headerVisibility": 0, "destination": "JHCommonWebView", "navigationType": "push", "source": "JHH-WEBAPP", "androidVersion": "<=408", "iosVersion": "<=1105", "userJourneyRequiredState": 2, "deeplinkIdentifier": "pme,https://https://healthhub.jio.com/pme", "bundle": "partner_id=ril&dashboard_title=PME&nav_page=/pme", "actionUrl": "https://healthhub.jio.com/pme/authorise?source=JHH-WEBAPP&pnp=true", "actionType": "T005"}, {"navTitle": "PME", "headerVisibility": 0, "bnbVisibility": 0, "androidVersion": ">=409", "iosVersion": ">=1106", "destination": "JHPmeAppointmentDetailsScreen", "deeplinkIdentifier": "pme,https://https://healthhub.jio.com/pme,100", "animation": "default", "actionUrl": "", "bundle": "", "actionType": "T001", "navigationType": "push"}, {"navTitle": "PME", "headerVisibility": 0, "bnbVisibility": 0, "androidVersion": ">=409", "iosVersion": ">=1106", "destination": "JHPmeLandingScreen", "deeplinkIdentifier": "101", "animation": "default", "actionUrl": "", "bundle": "", "actionType": "T001", "navigationType": "push"}, {"navTitle": "Doctor <PERSON>t", "headerVisibility": 0, "destination": "JHCommonWebView", "navigationType": "push", "source": "JHH-WEBAPP", "deeplinkIdentifier": "Doctor_Consult_Info_Screen,https://healthhub.jio.com/avatar", "bundle": "nav_page=/corporate-consult&consult_type_id=2&exclusive_partner_ids=607~419~1083~1084&partner_id=ril&dashboard_title=Consult", "actionUrl": "https://healthhub.jio.com/avatar/authorise?source=JHH-WEBAPP&pnp=true", "actionType": "T005"}, {"navTitle": "Doctor <PERSON>t", "headerVisibility": 0, "destination": "JHDocConsultAppointmentDetailsScreen", "navigationType": "push", "androidVersion": ">=4011", "iosVersion": ">=1108", "deeplinkIdentifier": "36,myconsult", "bundle": "", "actionUrl": "", "actionType": "T001"}, {"navTitle": "Emotional Wellness", "headerVisibility": 0, "destination": "JHCommonWebView", "source": "JHH-WEBAPP", "deeplinkIdentifier": "https://healthhub.jio.com/avatar/emotional-dashboard", "bundle": "consult_type_id=4&filter_specialty_ids=47&nav_page=emotional-dashboard&isMentalWellness=true", "actionUrl": "https://healthhub.jio.com/authorise?source=JHH-WEBAPP&pnp=true", "actionType": "T005"}, {"navTitle": "HealthPatri", "headerVisibility": 0, "destination": "JHCommonWebView", "navigationType": "push", "source": "JHH-WEBAPP", "deeplinkIdentifier": "health_patri,https://healthhub.jio.com/healthpatri", "bundle": "nav_page=/", "actionUrl": "https://healthhub.jio.com/healthpatri/authorise?source=JHH-WEBAPP&pnp=true", "actionType": "T005"}, {"navTitle": "", "headerVisibility": 0, "bnbVisibility": 0, "destination": "JHViewRecord", "deeplinkIdentifier": "my_health_data,https://healthhub.jio.com/healthlocker,3,6,12,health_locker", "userJourneyRequiredState": 3, "animation": "default", "actionUrl": "", "bundle": "", "actionType": "T001", "navigationType": "push"}, {"navTitle": "HealthFeed", "headerVisibility": 0, "destination": "JHCommonWebView", "navigationType": "push", "source": "JHH-WEBAPP", "deeplinkIdentifier": "article,https://healthhub.jio.com/healthfeed/home", "bundle": "", "actionUrl": "https://healthhub.jio.com/healthfeed", "actionType": "T005"}, {"navTitle": "ABHA", "headerVisibility": 0, "destination": "JHCommonWebView", "navigationType": "push", "source": "JHH-WEBAPP", "deeplinkIdentifier": "abdm,301", "bundle": "nav_page=/", "actionUrl": "https://healthhub.jio.com/abdm-phr/authorise?source=JHH-WEBAPP&pnp=true", "actionType": "T005"}, {"navTitle": "Lab Test", "headerVisibility": 0, "bnbVisibility": 0, "androidVersion": ">=4011", "iosVersion": ">=1108", "destination": "JHBatOrderDetailsScreen", "deeplinkIdentifier": "23,24", "animation": "default", "actionUrl": "", "bundle": "", "actionType": "T001", "navigationType": "push"}, {"navTitle": "Lab Test", "headerVisibility": 0, "bnbVisibility": 0, "androidVersion": ">=4011", "iosVersion": ">=1108", "destination": "JHBatLandingScreen", "deeplinkIdentifier": "booktest", "animation": "default", "actionUrl": "", "bundle": "", "actionType": "T001", "navigationType": "push"}, {"navTitle": "Lab Test", "headerVisibility": 0, "bnbVisibility": 0, "androidVersion": ">=4011", "iosVersion": ">=1108", "destination": "JHBatProviderTestPackageListScreen", "deeplinkIdentifier": "booktestprovider", "animation": "default", "actionUrl": "", "bundle": "", "actionType": "T001", "navigationType": "push"}, {"navTitle": "Health Chart", "headerVisibility": 0, "destination": "", "source": "", "deeplinkIdentifier": "14", "bundle": "", "actionUrl": "/chart", "actionType": "T007"}, {"navTitle": "Self-Health Check", "headerVisibility": 1, "destination": "JHSelfAssessmentLanding", "source": "", "deeplinkIdentifier": "JHSelfAssessment<PERSON><PERSON>ing,selfHealthCheck", "bundle": "", "actionUrl": "", "actionType": "T001"}, {"actionType": "T007", "actionUrl": "/blood_pressure_monitor", "deeplinkIdentifier": "blood_pressure_monitor", "bundle": "", "destination": "", "headerVisibility": 0, "navTitle": "BP Monitor", "source": ""}]}