{"searchConfig": [{"isVisibleForVersion": "1", "versionNumber": "2.0.33", "jiomart_header": {"header_type": 8}, "hideAddToCartFor": [], "hideWishListFor": ["ALCOHOL", "LOCALSHOPS"], "alcoholSection": {"bannerImageUrl": "https://jep-asset.akamaized.net/JioMart/Common/Introducing_Alcohol_Banner.jpg", "isHidden": false, "ActionTag": "100", "ActionType": "T001", "actionTag": "T003", "callActionLink": "open_webview", "commonActionUrl": "/?tab=alcohol", "ActionWebURL": "/?tab=alcohol", "HeaderType": 14, "QueryToAppend": "&os=iOS&version=%@", "NavigationTitle": "Alcohol"}, "localshopsSection": {"bannerImageUrl": "https://jep-asset.akamaized.net/JioMart/Common/search-banner-flowers.jpg", "isHidden": false, "ActionTag": "100", "ActionType": "T001", "actionTag": "T003", "callActionLink": "open_webview", "commonActionUrl": "/?tab=local-shops", "ActionWebURL": "/?tab=local-shops", "HeaderType": 14, "QueryToAppend": "&os=iOS&version=%@", "NavigationTitle": "Local Shops"}, "shopping_list_block": {"is_visible": true, "sorted_order": 1, "header_title": "Got a Shopping List?", "sub_title": "Search multiple products together."}, "recent_search": {"is_visible": true, "sorted_order": 2, "header_title": "Your recent searches", "visible_search_keyword_count": 10, "action_button": [{"is_visible": true, "button_title": "Clear All"}], "cta": {"actionType": "T001", "destination": "JMProductSearchListingScreen", "headerVisibility": 2, "navigationType": "push", "loginRequired": false, "actionUrl": "/products/?q=[QUERY]", "headerType": 9, "shouldShowDeliverToBar": true}}, "discover_more": {"is_visible": true, "sorted_order": 3, "header_title": "Try something new", "visible_search_keyword_count": 10, "cta": {"actionType": "T001", "destination": "JMProductSearchListingScreen", "headerVisibility": 2, "navigationType": "push", "loginRequired": false, "actionUrl": "/products/?q=[QUERY]", "headerType": 9, "shouldShowDeliverToBar": true}}, "popular_category": {"is_visible": true, "sorted_order": 5, "header_title": "Most Trending Categories", "default_image": "", "categoryViewContent": [{"Key": "GROCERIES", "IconAsset": "ic_grocery", "IconURL": "https://jep-asset.akamaized.net/JioMart/Common/JDSgrocery.png", "ActionWebURL": "groceries", "Title": "Grocery"}, {"Key": "FASHION", "IconAsset": "ic_apparel", "IconURL": "https://jep-asset.akamaized.net/JioMart/Common/JDSfashion.png", "ActionWebURL": "fashion", "Title": "Fashion"}, {"Key": "JEWELLERY", "IconAsset": "ic_jewellery", "IconURL": "https://jep-asset.akamaized.net/JioMart/Common/JDSjewelry.png", "ActionWebURL": "fashion-jewellery-may-2023", "Title": "Jewellery"}, {"Key": "BEAUTY", "IconAsset": "ic_beauty", "IconURL": "https://jep-asset.akamaized.net/JioMart/Common/JDSbeauty.png", "ActionWebURL": "beauty", "Title": "Beauty", "isVisibleForVersion": "2", "versionNumber": "1.0.10"}, {"ActionWebURL": "electronics", "IconAsset": "ic_digital", "IconURL": "https://jep-asset.akamaized.net/JioMart/Common/JDSelectronics.png", "Key": "ELECTRONICS", "Title": "Electronics"}, {"ActionWebURL": "premium-fruits", "IconAsset": "ic_premiumfruits", "IconURL": "https://jep-asset.akamaized.net/JioMart/Common/JDSpremium_fruits.png", "Key": "PREMIUMFRUITS", "Title": "Premium fruits"}, {"ActionWebURL": "kitchen-and-dining", "IconAsset": "ic_home_kitchen", "IconURL": "https://jep-asset.akamaized.net/JioMart/Common/JDShome_ktchen.png", "Key": "HOMEANDKITCHEN", "Title": "Home & Kitchen"}], "cta": {"actionType": "T001", "destination": "JMProductListingScreen", "headerVisibility": 2, "navigationType": "push", "loginRequired": false, "actionUrl": "/collection/[SLUG]", "headerType": 9, "shouldShowDeliverToBar": true}}, "recommended_product": {"is_visible": true, "sorted_order": 4, "header_title": "Recommended for you", "default_image": "", "maxItemsVisible": 10, "is_buy_again_appear_first": true, "is_buy_again_api_enabled": true, "is_recommended_product_api_enabled": true}, "searchSuggestion_product": {"is_visible": true, "header_title": "Products", "default_image": "", "maxItemsVisible": 10}, "searchSuggestion_List": {"is_visible": true, "maxItemsVisible": 4, "cta": {"actionType": "T001", "destination": "JMProductSearchListingScreen", "headerVisibility": 2, "navigationType": "push", "loginRequired": false, "actionUrl": "/products/?q=[QUERY]", "headerType": 9, "shouldShowDeliverToBar": true}}, "searchSuggestion_Categories": {"is_visible": true, "header_title": "Categories", "maxItemsVisible": 10, "cta": {"actionType": "T001", "destination": "JMProductSearchListingScreen", "headerVisibility": 2, "navigationType": "push", "loginRequired": false, "actionUrl": "/products/?q=[QUERY]&l3_category_names=[L3_CATEGORY_NAMES]", "headerType": 9, "shouldShowDeliverToBar": true}}, "searchSuggestion_Brands": {"is_visible": true, "header_title": "Brands", "maxItemsVisible": 10, "cta": {"actionType": "T001", "destination": "JMProductSearchListingScreen", "headerVisibility": 2, "navigationType": "push", "loginRequired": false, "actionUrl": "/products/?q=[QUERY]&brand=[BRAND]", "headerType": 9, "shouldShowDeliverToBar": true}}, "hyperlocal_product": {"hyperlocalCustomDeliveryMessageV1": "Scheduled Delivery by\n#5", "hyperlocalCustomDeliveryMessageV1New": "Scheduled Delivery by #5", "hyperlocalDefaultDeliveryMessage": "Scheduled delivery \nwithin 5 days", "hyperlocalDefaultDeliveryMessageNew": "Scheduled delivery within 5 days", "isDefaultDeliveryMsgEnabled": false, "hyperLocalSellerId": "1", "hyperLocalVerticalCode": "GROCERIES"}, "algoliaSearchConfiguration": {"uiPathID": "M1lQMEhQM1dTSA==", "algoliaEnvironment": "prod_mart_", "algoliaIndexNameForAllCategory": "prod_mart_master_vertical", "cardViewID": "YWFjZTNmMTg0MzBhNDllMTg1ZDJjMTExMTYwMmU0YjE=", "algoliaIndexName": "prod_mart_query_suggestions", "algoliaFirstIndexName": "prod_mart_query_suggestions", "algoliaSecondIndexName": "prod_mart_master_vertical", "hitsPerPageForMasterVertical": 50, "hitsPerPageForSearchQuery": 4, "hitsPerPageForDiscoverMoreQuery": 10}, "btm_sheet": {"shopping_list_block": {"is_visible": true, "header_title": "Shopping List", "sub_title": "Search multiple products by entering your shopping list below", "text_input": {"is_visible": true, "label": "Enter your items", "placeholder": "e.g. Milk, Bread, Fruit", "sub_label": "Add comma as separator", "invalid_message": "Please enter text in correct Format*"}, "action_button": [{"is_visible": true, "button_title": "Clear"}, {"is_visible": true, "button_title": "Search All"}], "toast_messages": ["Please enter products to search"], "cta": {"actionType": "T001", "destination": "JMProductSearchListingScreen", "headerVisibility": 2, "navigationType": "push", "loginRequired": false, "actionUrl": "/products/?q=[QUERY]&slist=[SLIST]", "shouldShowDeliverToBar": true, "headerType": 9}}, "replace_block": {"is_visible": true, "header_title": "Replace item in Stores Basket?", "sub_title": "Your Stores Basket contains item(s) from another store. Do you want to discard selection and add item(s) from %@?", "default_store_name_enable": false, "default_store_name_text": "Previous Store", "action_button": [{"is_visible": true, "button_title": "No"}, {"is_visible": true, "button_title": "Yes"}]}}}]}