import {JMSharedViewModel} from '../../../../jiomart-common/src/JMSharedViewModel';
import {AppSourceType} from '../../../../jiomart-common/src/SourceType';
import {JMConfigFileName} from '../JMFileName';

const CONFIG_FILE_MAP: Record<string, Record<string, any>> = {
  [AppSourceType.JM_BAU]: {
    [JMConfigFileName.JMAddressConfigurationFileNAme]: require('../assets/jiomart_bau/JMRNAddressConfigurationV1.json'),
    [JMConfigFileName.JMCommonContentFileName]: require('../assets/jiomart_bau/JMRNCommanConfigsV1.json'),
    [JMConfigFileName.JMPLPConfigFileName]: require('../assets/jiomart_bau/JMRNPLPConfigurationV1.json'),
    [JMConfigFileName.JMGridPLPConfigurationFileName]: require('../assets/jiomart_bau/JMRNGridPLPConfigurationV1.json'),
    [JMConfigFileName.JMSearchPLPConfigurationFileName]: require('../assets/jiomart_bau/JMRNSearchPLPConfigurationV1.json'),
    [JMConfigFileName.JMSearchConfigurationFileName]: require('../assets/jiomart_bau/JMRNSearchConfigurationV1.json'),
    [JMConfigFileName.JMProfileConfigurationFileName]: require('../assets/jiomart_bau/JMRNProfileConfigurationV1.json'),
    [JMConfigFileName.JMPDPConfigurationFileName]: require('../assets/jiomart_bau/JMRNPDPConfigurationV1.json'),
    [JMConfigFileName.JMHeaderConfigurationFileName]: require('../assets/jiomart_bau/JMHeaderConfigurationV1.json'),
    [JMConfigFileName.JMAllCategoriesConfigurationFileName]: require('../assets/jiomart_bau/JMAllCategoriesConfigurationV1.json'),
    [JMConfigFileName.JMDeeplinkConfigurationFileName]: require('../assets/jiomart_bau/JMDeeplinkConfigurationV1.json'),
  },
  [AppSourceType.JM_JCP]: {
    [JMConfigFileName.JMAddressConfigurationFileNAme]: require('../assets/jiomart_jcp/JMRNAddressConfigurationV1.json'),
    [JMConfigFileName.JMCommonContentFileName]: require('../assets/jiomart_jcp/JMRNCommanConfigsV1.json'),
    [JMConfigFileName.JMPLPConfigFileName]: require('../assets/jiomart_jcp/JMRNPLPConfigurationV1.json'),
    [JMConfigFileName.JMGridPLPConfigurationFileName]: require('../assets/jiomart_jcp/JMRNGridPLPConfigurationV1.json'),
    [JMConfigFileName.JMSearchPLPConfigurationFileName]: require('../assets/jiomart_jcp/JMRNSearchPLPConfigurationV1.json'),
    [JMConfigFileName.JMSearchConfigurationFileName]: require('../assets/jiomart_jcp/JMRNSearchConfigurationV1.json'),
    [JMConfigFileName.JMProfileConfigurationFileName]: require('../assets/jiomart_jcp/JMRNProfileConfigurationV1.json'),
    [JMConfigFileName.JMPDPConfigurationFileName]: require('../assets/jiomart_jcp/JMRNPDPConfigurationV1.json'),
    [JMConfigFileName.JMHeaderConfigurationFileName]: require('../assets/jiomart_jcp/JMHeaderConfigurationV1.json'),
    [JMConfigFileName.JMAllCategoriesConfigurationFileName]: require('../assets/jiomart_jcp/JMAllCategoriesConfigurationV1.json'),
    [JMConfigFileName.JMDeeplinkConfigurationFileName]: require('../assets/jiomart_jcp/JMDeeplinkConfigurationV1.json'),
  },
};

export const getAssetConfigFileData = (fileName: string) => {
  try {
    return CONFIG_FILE_MAP[JMSharedViewModel.Instance.appSource][fileName];
  } catch (error) {}
};
