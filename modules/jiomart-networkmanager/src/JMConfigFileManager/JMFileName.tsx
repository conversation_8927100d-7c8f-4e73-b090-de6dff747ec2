export enum JMConfigFileName {
  JMVersionFileName = 'JMRNLocalFileVersionList',
  JMCommonContentFileName = 'JMRNCommanConfigsV1',
  JMPLPConfigFileName = 'JMRNPLPConfigurationV1',
  JMGridPLPConfigurationFileName = 'JMRNGridPLPConfigurationV1',
  JMSearchPLPConfigurationFileName = 'JMRNSearchPLPConfigurationV1',
  JMSearchConfigurationFileName = 'JMRNSearchConfigurationV1',
  JMProfileConfigurationFileName = 'JMRNProfileConfigurationV1',
  JMAddressConfigurationFileNAme = 'JMRNAddressConfigurationV1',
  JMPDPConfigurationFileName = 'JMRNPDPConfigurationV1',
  JMHeaderConfigurationFileName = 'JMHeaderConfigurationV1',
  JMAllCategoriesConfigurationFileName = 'JMAllCategoriesConfigurationV1',
  JMDeeplinkConfigurationFileName = 'JMDeeplinkConfigurationV1',
}
