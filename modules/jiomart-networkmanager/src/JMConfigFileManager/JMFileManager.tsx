import {
  getPrefString,
  addStringPref,
} from '../../../jiomart-common/src/JMAsyncStorageHelper';
import axios, {AxiosResponse} from 'axios';
import {setServerFileData} from './JMFileSlice';
import {JMLogger} from '../../../jiomart-common/src/utils/JMLogger';
import {JMConfigFileName} from './JMFileName';
import {
  JMConfigNames,
  JMBaseUrlKeys,
  getAwsConfigPath,
  getJioMartEnvironmentConfig,
} from '../JMHeliosNetworkManager/JMEnvironmentConfig';
import {isNullOrUndefinedOrEmpty} from '../../../jiomart-common/src/JMObjectUtility';
import {getAssetConfigFileData} from './utils/JMAssetConfigFileUtility';
import {Platform} from 'react-native';
import store from '../../../jiomart-main/src/store/store';

export interface JHFileData {
  version?: string | '';
  data?: any | null;
}

function getAwsUrl(fileName: string): string {
  return (
    getJioMartEnvironmentConfig()[JMBaseUrlKeys.AWS_STORAGE] +
    '/' +
    getAwsConfigPath()[JMConfigNames.CONFIG_FILES] +
    '/' +
    fileName +
    (Platform.OS == 'ios' ? '.json' : '.txt')
  );
}

export async function getConfigDataFromServer(configUrl: string): Promise<any> {
  JMLogger.log(
    `busiCode request url (${new Date().toISOString()}):` + configUrl,
  );
  const response: AxiosResponse<any> = await axios.get<AxiosResponse<any>>(
    configUrl,
    {
      headers: {
        Accept: 'application/json',
        'Content-Type': 'application/json',
        'Cache-Control': 'no-cache',
      },
    },
  );
  JMLogger.log(
    `busiCode response filename (${new Date().toISOString()}): ` +
      configUrl +
      ' response ' +
      JSON.stringify(response.data).length,
  );
  return response.data;
}

export function getConfigDataFromStorage(
  fileName: string,
  resultCallback: (data?: any) => void,
) {
  getPrefString(fileName)
    .then(data => {
      if (data) {
        let jsonData = JSON.parse(data);
        if (jsonData) {
          resultCallback(jsonData['data']);
        }
      } else {
        resultCallback(null);
      }
    })
    .catch(() => {
      resultCallback(null);
    });
}

export function getConfigDataFromAssetFile(
  fileName: string,
  resultCallback: (data?: any) => void,
) {
  resultCallback(getAssetConfigFileData(fileName));
}

export async function readConfigFileFromServer(
  fileName: string,
  resultCallback: (data?: any) => void,
) {
  const fileVersionNumber = await checkConfigFileServerCallRequired(fileName);
  console.log('fileVersionNumber -- ', fileVersionNumber, fileName);
  if (fileVersionNumber && fileVersionNumber > 0) {
    let configUrl = getAwsUrl(fileName);
    console.log('URL ---  ', configUrl);
    getConfigDataFromServer(configUrl)
      .then(data => {
        let jsonData = data;
        if (jsonData) {
          insertConfigDataInStorage(
            fileName,
            jsonData,
            fileVersionNumber.toString(),
          );
          resultCallback(jsonData);
        } else {
          resultCallback(null);
        }
      })
      .catch(() => {
        resultCallback(null);
      });
  } else {
    resultCallback(null);
  }
}

const checkConfigFileServerCallRequired = async (fileName: string) => {
  let existingConfigFileDataString = await getPrefString(fileName);
  let existingConfigFileVersion = -1;
  if (existingConfigFileDataString) {
    console.log('fileVersionConfigData 2 -- ', existingConfigFileDataString);
    const jsonObject = JSON.parse(existingConfigFileDataString ?? '');
    existingConfigFileVersion = parseFloat(
      fromFileDataToJSON(jsonObject).version ?? '0',
    );
  }
  let versionFileData = getVersionConfigFileDataFromRedux();
  if (versionFileData) {
    console.log('fileVersionConfigData 3 -- ', versionFileData);
    let jsonData = JSON.parse(versionFileData);
    let version = (jsonData[fileName] as string)
      ? parseFloat(jsonData[fileName])
      : 0;
    JMLogger.log(
      'version fileName' + fileName + version + ' ' + existingConfigFileVersion,
    );
    if (isNullOrUndefinedOrEmpty(jsonData[fileName])) {
      console.log('fileVersionConfigData 4 -- ', 1);
      return 1;
    }
    return version > existingConfigFileVersion ? version : -1;
  } else {
    let fileVersionConfigData = await getPrefString(
      JMConfigFileName.JMVersionFileName,
    );
    console.log('fileVersionConfigData -- ', fileVersionConfigData);
    if (fileVersionConfigData) {
      let jsonData = JSON.parse(fileVersionConfigData);
      let version = (jsonData[fileName] as string)
        ? parseFloat(jsonData[fileName])
        : 0;
      if (isNullOrUndefinedOrEmpty(jsonData[fileName])) {
        return 1;
      }
      JMLogger.log(
        'version fileName' +
          fileName +
          version +
          ' ' +
          existingConfigFileVersion,
      );
      return version > existingConfigFileVersion ? version : -1;
    }
  }
};

function getVersionConfigFileDataFromRedux() {
  const serverFileData = store.getState().fileVersion.serverFileVersionData;
  if (serverFileData) {
    return serverFileData;
  }
}

function insertConfigDataInStorage(
  fileName: string,
  data: any,
  fileVersionNumber: string,
) {
  const fileModel: JHFileData = {
    version: fileVersionNumber,
    data: data,
  };
  let jsonString = JSON.stringify(fileModel);
  addStringPref(fileName, jsonString);
}

function fromFileDataToJSON(json: any): JHFileData {
  return {
    version: json['version'],
    data: json['data'],
  };
}

//wrap promise callVersion file

export const callVersionFileAsync = (fileName: string): Promise<boolean> => {
  return new Promise((resolve, reject) => {
    callVersionFile(fileName, isSuccess => {
      if (isSuccess) {
        resolve(true);
      } else {
        reject(false);
      }
    });
  });
};

// version file logic
export async function callVersionFile(
  fileName: string,
  resultCallback: (isSuccess: any) => void,
) {
  let configUrl = getAwsUrl(fileName);
  getConfigDataFromServer(configUrl)
    .then(data => {
      let jsonString = JSON.stringify(data);
      JMLogger.log(
        'callVersionFile getConfigDataFromServer fileName ' +
          fileName +
          ' data ' +
          jsonString,
      );
      if (data) {
        store.dispatch(setServerFileData(jsonString));
        addStringPref(fileName, jsonString);
        resultCallback(true);
      } else {
        setFileVersionConfigDataInRedux();
        resultCallback(false);
      }
    })
    .catch(() => {
      setFileVersionConfigDataInRedux();
      resultCallback(false);
    });
}

async function setFileVersionConfigDataInRedux() {
  getPrefString(JMConfigFileName.JMVersionFileName).then(data => {
    if (data) {
      store.dispatch(setServerFileData(data));
    }
  });
}
// end region of version file logic

// get config file data from server and then async storage and then asset file
export async function getConfigFileData(
  fileName: string,
  resultCallback: (data?: any) => void,
) {
  readConfigFileFromServer(fileName, configServerData => {
    JMLogger.log(
      'getConfigFileData readConfigFileFromServer fileName ' +
        fileName +
        configServerData,
    );
    if (configServerData) {
      resultCallback(configServerData);
      return configServerData;
    } else {
      getConfigDataFromStorage(fileName, configStorageData => {
        if (configStorageData) {
          JMLogger.log(
            'getConfigFileData getConfigDataFromStorage fileName ' +
              fileName +
              ' data ' +
              JSON.stringify(configStorageData),
          );
          resultCallback(configStorageData);
          return configStorageData;
        } else {
          getConfigDataFromAssetFile(fileName, configAssetData => {
            resultCallback(configAssetData);
            return configAssetData;
          });
        }
      });
    }
  });
}

// wrap a promise to getConfigFileData

export async function getConfigFileDataAsync(fileName: string) {
  return new Promise(resolve => {
    readConfigFileFromServer(fileName, configServerData => {
      JMLogger.log(
        'getConfigFileData readConfigFileFromServer fileName ' +
          fileName +
          configServerData,
      );
      if (configServerData) {
        resolve(configServerData);
      } else {
        getConfigDataFromStorage(fileName, configStorageData => {
          if (configStorageData) {
            JMLogger.log(
              'getConfigFileData getConfigDataFromStorage fileName ' +
                fileName +
                ' data ' +
                JSON.stringify(configStorageData),
            );
            resolve(configStorageData);
          } else {
            getConfigDataFromAssetFile(fileName, configAssetData => {
              resolve(configAssetData);
            });
          }
        });
      }
    });
  });
}
