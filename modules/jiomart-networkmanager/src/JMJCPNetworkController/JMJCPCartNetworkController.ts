import useAddress from '@jm/jiomart-address/src/hooks/useAddress';
import {JMJCPCartEndpointKeys} from '../api/endpoints/JMApiEndpoints';
import {
  JMHttpMethods,
  JMRequestConfigObject,
} from '../api/helpers/JMRequestConfig';
import JMRequestHelper from '../api/helpers/JMRequestHelper';
import JMApiClient from '../api/service/JMApiClient';
import JMBaseCartNetworkController from '../base/JMBaseCartNetworkController';
import {JMBaseUrlKeys} from '../JMEnvironmentConfig';
import {isNullOrUndefinedOrEmpty} from '@jm/jiomart-common/src/JMObjectUtility';
import JMJCPPRoductNetworkController from './JMJCPProductNetworkController';
import {AxiosError} from 'axios';

// eslint-disable-next-line react-hooks/rules-of-hooks
const address = useAddress();

class JMJCPCartNetworkController extends JMBaseCartNetworkController {
  private apiClient: JMApiClient;
  private baseUrl: JMBaseUrlKeys;
  private productNetworkController: JMJCPPRoductNetworkController;
  constructor() {
    super();
    this.apiClient = new JMApiClient();
    this.baseUrl = JMBaseUrlKeys.JCP;
    this.productNetworkController = new JMJCPPRoductNetworkController();
  }

  protected fetchCart = async (): Promise<any> => {
    try {
      let data: any = await address.getDefaultAddress();
      let location = !isNullOrUndefinedOrEmpty(data) ? JSON.parse(data) : {};
      const params = {
        i: true,
        b: true,
        buy_now: false,
        area_code: location?.pin,
        cart_type: 'universal',
      };
      let requestHelper = new JMRequestHelper(
        JMRequestConfigObject.requestConfig(
          this.baseUrl,
          JMJCPCartEndpointKeys.CART_LIST,
          JMHttpMethods.GET,
          undefined,
          params,
        ),
      );
      return await this.apiClient.request(requestHelper);
    } catch (error) {
      throw error;
    }
  };

  protected addToCart = async (request: any): Promise<any> => {
    try {
      const {quantity} = request;
      if (quantity <= 1) {
        const req = {
          items: [request],
        };
        return await this.initiateAddToCart(req);
      } else {
        const req = {
          operation: 'update_item',
          items: [request],
        };
        return await this.updateCart(req);
      }
    } catch (error) {
      throw error;
    }
  };

  protected removeFromCart = async (request: any): Promise<any> => {
    try {
      const {quantity} = request;
      let operation = 'update_item';
      if (quantity === 0) {
        operation = 'remove_item';
      }
      const req = {
        operation,
        items: [request],
      };
      return await this.updateCart(req);
    } catch (error) {
      throw error;
    }
  };

  private initiateAddToCart = async (request: any) => {
    try {
      const params = {
        i: true,
        b: true,
        buy_now: false,
      };
      const requestBody = await this.fetchProductPriceData(request);

      request.items[0] = {
        ...request.items[0],
        ...requestBody,
      };
      let requestHelper = new JMRequestHelper(
        JMRequestConfigObject.requestConfig(
          this.baseUrl,
          JMJCPCartEndpointKeys.INITIALIZE_CART,
          JMHttpMethods.POST,
          request,
          params,
        ),
      );
      return await this.apiClient.request(requestHelper);
    } catch (error) {
      throw error;
    }
  };

  private updateCart = async (request: any) => {
    try {
      const params = {
        i: true,
        b: true,
        buy_now: false,
      };
      let requestHelper = new JMRequestHelper(
        JMRequestConfigObject.requestConfig(
          this.baseUrl,
          JMJCPCartEndpointKeys.UPDATE_CART,
          JMHttpMethods.PUT,
          request,
          params,
        ),
      );
      return await this.apiClient.request(requestHelper);
    } catch (error) {
      throw error;
    }
  };

  private async fetchProductPriceData(request: any) {
    try {
      const {item_size, slug} = request.items[0];
      const priceResponse: any =
        await this.productNetworkController.fetchProductPrice({
          slug,
          size: item_size,
        });

      if (!priceResponse?.items?.[0]?.is_serviceable) {
        throw new AxiosError(priceResponse?.items?.[0]?.error, '500');
      }

      const requestBody = {
        seller_id: priceResponse?.items?.[0].seller?.uid,
        store_id: priceResponse?.items?.[0].store?.uid,
        article_assignment: priceResponse?.items?.[0]?.article_assignment,
        article_id: priceResponse?.items?.[0]?.article_id,
      };
      return requestBody;
    } catch (error) {
      throw error;
    }
  }
}

export default JMJCPCartNetworkController;
