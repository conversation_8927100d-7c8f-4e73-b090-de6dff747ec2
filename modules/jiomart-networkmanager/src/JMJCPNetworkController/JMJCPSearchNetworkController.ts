import axios from 'axios';
import {JMJCPSearchEndpointKeys} from '../api/endpoints/JMApiEndpoints';
import {
  JMHttpMethods,
  JMRequestConfigObject,
} from '../api/helpers/JMRequestConfig';
import JMRequestHelper from '../api/helpers/JMRequestHelper';
import JMApiClient from '../api/service/JMApiClient';
import {JMBaseUrlKeys} from '../JMEnvironmentConfig';

export default class JMJCPSearchNetworkController {
  private apiClient: JMApiClient;
  private baseUrl: JMBaseUrlKeys;
  constructor() {
    this.apiClient = new JMApiClient();
    this.baseUrl = JMBaseUrlKeys.JCP;
  }
  protected getRecommendedProducts = async (params: any) => {
    try {
      let requestHelper = new JMRequestHelper(
        JMRequestConfigObject.requestConfig(
          this.baseUrl,
          JMJCPSearchEndpointKeys.RECOMMENDED_ITEMS,
          JMHttpMethods.GET,
          undefined,
          params,
        ),
      );
      return await this.apiClient.request(requestHelper);
    } catch (error) {
      throw error;
    }
  };

  protected fetchDiscoverMoreData = async () => {
    try {
      let requestHelper = new JMRequestHelper(
        JMRequestConfigObject.requestConfig(
          this.baseUrl,
          JMJCPSearchEndpointKeys.DISCOVER_MORE_ITEMS,
          JMHttpMethods.GET,
          undefined,
          null,
        ),
      );
      return await this.apiClient.request(requestHelper);
    } catch (error) {
      throw error;
    }
  };

  protected getSearchResults = async (params: any) => {
    console.log('This is Called');
    const headers = {
      Accept: 'application/json, text/plain, */*',
      'Access-Control-Allow-Credentials': 'true',
      Authorization: 'Bearer NjYzZTIzMzJiNzAzMTA0MzU1MWFkNjk0OlB6aGdqaXV4dg==',
      'Cache-Control': 'no-cache',
      'Content-Type': 'application/json',
      Cookie: 'f.session=...',
      'X-Currency-Code': 'INR',
      'X-Fp-Date': '20250509T103748Z',
      'X-Fp-Signature': 'v1.1:...',
      'X-Location-Detail':
        '{"pincode":"400001","city":"Mumbai","state":"Maharashtra","country_iso_code":"IN","country":"India"}',
      'x-company-id': '2',
    };

    try {
      const response = await axios.get(
        'https://jiomart.sit.jiomartjcp.com/ext/algolia/application/api/v1.0/auto-complete',
        {
          params,
          headers,
        },
      );
      return response.data;
    } catch (error) {
      console.error('Error in getSearchResults:', error);
      throw error;
    }
  };

  protected fetchRecommendedItemsServiceability = async (bodyParams: any) => {
    try {
      let requestHelper = new JMRequestHelper(
        JMRequestConfigObject.requestConfig(
          this.baseUrl,
          JMJCPSearchEndpointKeys.RECOMMENDED_ITEMS_SERVICIBILITY,
          JMHttpMethods.POST,
          bodyParams,
          null,
        ),
      );
      return await this.apiClient.request(requestHelper);
    } catch (error) {
      throw error;
    }
  };
}
