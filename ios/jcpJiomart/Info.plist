<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CFBundleDevelopmentRegion</key>
	<string>en</string>
	<key>CFBundleDisplayName</key>
	<string>jcpJiomart</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIcons</key>
	<dict>
		<key>CFBundleAlternateIcons</key>
		<dict>
			<key>JMSaleAppIcon</key>
			<dict>
				<key>CFBundleIconFiles</key>
				<array>
					<string>JMSaleAppIcon</string>
				</array>
			</dict>
		</dict>
	</dict>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>$(PRODUCT_NAME)</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>$(MARKETING_VERSION)</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleVersion</key>
	<string>$(CURRENT_PROJECT_VERSION)</string>
	<key>GOOGLE_MAPS_API_KEY</key>
	<string>$(GOOGLE_MAPS_API_KEY)</string>
	<key>LSApplicationCategoryType</key>
	<dict/>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>NSAppTransportSecurity</key>
	<dict>
		<key>NSAllowsArbitraryLoads</key>
		<true/>
		<key>NSAllowsLocalNetworking</key>
		<true/>
	</dict>
	<key>NSAppleMusicUsageDescription</key>
	<string>$(APP_NAME) media library use</string>
	<key>NSBluetoothPeripheralUsageDescription</key>
	<string>$(APP_NAME) bluetooth peripheral use</string>
	<key>NSCameraUsageDescription</key>
	<string>We use your camera to scan QR codes</string>
	<key>NSContactsUsageDescription</key>
	<string>Sync your contacts to send SMS. No charges apply.</string>
	<key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
	<string>We need your location to find the right products deliverable to you</string>
	<key>NSLocationWhenInUseUsageDescription</key>
	<string>We need your location to find the right products deliverable to you</string>
	<key>NSMicrophoneUsageDescription</key>
	<string>We require access of your microphone to enable voice based search for products within JioMart</string>
	<key>NSMotionUsageDescription</key>
	<string>$(APP_NAME) motion sensor use</string>
	<key>NSPhotoLibraryAddUsageDescription</key>
	<string>We need permission to access the scanned QR code from your library</string>
	<key>NSPhotoLibraryUsageDescription</key>
	<string>We need permission to access the scanned QR code from your library</string>
	<key>NSSiriUsageDescription</key>
	<string>$(APP_NAME) siri use</string>
	<key>NSSpeechRecognitionUsageDescription</key>
	<string>Speech recognition will be used to determine which words you speak into this device's microphone.</string>
	<key>NSUserTrackingUsageDescription</key>
	<string>Allow tracking to help us provide you a better personalized ad experience tailored to your interests across app and website.</string>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	<key>UIRequiredDeviceCapabilities</key>
	<array>
		<string>arm64</string>
	</array>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
	</array>
	<key>UISupportedInterfaceOrientations~ipad</key>
	<array>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
		<string>UIInterfaceOrientationPortrait</string>
	</array>
	<key>UIViewControllerBasedStatusBarAppearance</key>
	<false/>
</dict>
</plist>
