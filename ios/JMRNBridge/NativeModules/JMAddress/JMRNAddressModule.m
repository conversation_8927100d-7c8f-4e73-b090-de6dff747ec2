//
//  JMRNAddressModule.m
//  jcpJiomart
//
//  Created by Manish4 Sah on 25/04/25.
//

#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Wstrict-prototypes"
#import <React/RCTBridgeModule.h>

@interface RCT_EXTERN_MODULE(JMRNAddressModule, NSObject)
  
RCT_EXTERN_METHOD(getReverseGeoCodeFromLatLong:(nonnull NSNumber *)latitude
                  longitude:(nonnull NSNumber *)longitude
                  resolver:(RCTPromiseResolveBlock)resolve
                  rejecter:(RCTPromiseRejectBlock)reject)

@end
#pragma clang diagnostic pop
