//
//  JMAddressModel.swift
//  jcpJiomart
//
//  Created by Manish4 Sah on 25/04/25.
//


import Foundation
import CoreLocation

struct JMAddressModel: Codable {
    var id: String?
    var name: String?
    var phone: String?
    var addressType: String?
    var address: String?
    var flatOrHouseNo: String?
    var floorNo: String?
    var towerNo: String?
    var area: String?
    var landmark: String?
    var city: String
    var state: String
    var pin: String
    var lat: Double?
    var lon: Double?
    var inputMode: String?
    var isDefaultAddress: Bool?
    var createdTime: String?
    var updatedTime: String?
    var countryIsoCode: String?
    var country: String?
    
  var coordinate: CLLocationCoordinate2D? {
      get {
          guard let lat = lat, let lon = lon else { return nil }
          return CLLocationCoordinate2D(latitude: lat, longitude: lon)
      }
      set {
          guard let newValue = newValue else {
              lat = nil
              lon = nil
              return
          }
          lat = newValue.latitude
          lon = newValue.longitude
      }
  }

}
