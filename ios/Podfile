# Resolve react_native_pods.rb with node to allow for hoisting

source 'https://github.com/CocoaPods/Specs.git'
source 'https://cdn.cocoapods.org/'
source 'http://git-ds.jio.com/cocoapods/jio-design-system--podspecs.git'

def node_require(script)
  # Resolve script with node to allow for hoisting
  require Pod::Executable.execute_command('node', ['-p',
  "require.resolve(
  '#{script}',
  {paths: [process.argv[1]]},
  )", __dir__]).strip
end

ENV['RCT_NEW_ARCH_ENABLED'] = '0'

# Use it to require both react-native's and this package's scripts:
node_require('react-native/scripts/react_native_pods.rb')
node_require('react-native-permissions/scripts/setup.rb')

platform :ios, '15.1'
prepare_react_native_project!

setup_permissions([
    'AppTrackingTransparency',
    # 'Bluetooth',
    # 'Calendars',
    # 'CalendarsWriteOnly',
    'Camera',
    # 'Contacts',
    # 'FaceID',
    'LocationAccuracy',
    'LocationAlways',
    'LocationWhenInUse',
    'MediaLibrary',
    'Microphone',
    # 'Motion',
    'Notifications',
    'PhotoLibrary',
    'PhotoLibraryAddOnly',
    # 'Reminders',
    # 'Siri',
    'SpeechRecognition',
    # 'StoreKit',
])

linkage = ENV['USE_FRAMEWORKS']

if linkage != nil
  Pod::UI.puts "Configuring Pod with #{linkage}ally linked Frameworks".green
  use_frameworks! :linkage => linkage.to_sym
end

target 'jcpJiomart' do
  use_frameworks!
  $RNFirebaseAsStaticFramework = true
  config = use_native_modules!
  
  pod 'GoogleAnalytics'
  pod 'CleverTap-iOS-SDK', '~> 5.2.2'
  pod 'GoogleMaps', '7.4.0'
  pod 'GooglePlaces', :modular_headers => true
  # pod 'react-native-google-maps', :path => '../node_modules/react-native-maps'
  
  
  pod 'FirebaseAnalytics'
  pod 'Firebase/Crashlytics'
  pod 'Firebase/Performance'
  
  use_react_native!(
        :path => config[:reactNativePath],
        :hermes_enabled => true,
        :app_path => "#{Pod::Config.instance.installation_root}/..",
        :new_arch_enabled => false,
        :fabric_enabled => false
  )
                    
  target 'jcpJiomartTests' do
  inherit! :complete
  # Pods for testing
end

pre_install do |installer|
  installer.pod_targets.each do |pod|
    if ['RNScreens',
        'RNPermissions',
        'RNReanimated',
        'react-native-mmkv',
        'react-native-google-maps',
        'react-native-maps',
        'react-native-google-places-sdk',
        'RNReactNativeHapticFeedback'
       ].include?(pod.name)
      def pod.build_type
        Pod::BuildType.static_library
      end
    end
  end
end

post_install do |installer|
  # https://github.com/facebook/react-native/blob/main/packages/react-native/scripts/react_native_pods.rb#L197-L202
  react_native_post_install(
        installer,
        config[:reactNativePath],
        :mac_catalyst_enabled => false,
      # :ccache_enabled => true
      )
  end
end
