//
//  JMRN.swift
//  jcpJiomart
//
//  Created by Manish4 Sah on 28/04/25.
//

import Foundation

typealias InitialProps = InfoPlist

@objc(JMRN)
class JMRN: NSObject {
  @objc public static let shared = JMRN()
  @objc public var initialProps: [String:Any] = [:]
  
  private override init(){
    super.init();
    self.setInitialPropsValue()
  }
  
  private func setInitialPropsValue() {
     self.initialProps[InitialProps.googleMapKey] = JMInfoPlist.googleMapKey
   }
  
}
